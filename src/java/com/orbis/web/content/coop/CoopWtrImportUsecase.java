package com.orbis.web.content.coop;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.poi.POIXMLProperties;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.df.DFAnswerEntity;
import com.orbis.df.DFHelper;
import com.orbis.df.DFQuestion;
import com.orbis.portal.OrbisHibernateTemplate;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.ClassUtils;
import com.orbis.utils.I18nLabel;
import com.orbis.utils.NumberUtils;
import com.orbis.web.content.acrm.ImportHelper;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.acrm.dataImport.AcrmDataImportHelper.DATATYPE;
import com.orbis.web.content.acrm.dataImport.ColumnMeta;
import com.orbis.web.content.acrm.dataImport.ColumnMetaMap;
import com.orbis.web.content.acrm.dataImport.ImportRowException;
import com.orbis.web.content.acrm.dataImport.ImportUsecase;
import com.orbis.web.content.np.NPosting;

public class CoopWtrImportUsecase implements ImportUsecase
{
    private static final String ENTITY_KEY = "record";

    private static final Log logger = LogFactory.getLog(CoopWtrImportUsecase.class);

    private final CoopModule coopModule;

    private final String wtrModelKey;

    public CoopWtrImportUsecase(CoopModule coopModule, String wtrModelKey)
    {
        this.coopModule = coopModule;
        this.wtrModelKey = wtrModelKey;
    }

    @Override
    public String getUsecaseKey()
    {
        return wtrModelKey;
    }

    @Override
    public String getUsecaseLabel()
    {
        CoopWtrModelAbstract model = getWtrModel();
        return model != null ? model.getLabel() : "Work Term Record";
    }

    @Override
    public String getL2UsecaseLabel()
    {
        CoopWtrModelAbstract model = getWtrModel();
        return model != null ? model.getL2Label() : "Placement";
    }

    @Override
    public String validateUploadedColumns(ColumnMetaMap uploadedColumns,
            XSSFRow masterRow, Locale locale)
    {
        String error = null;
        List<ColumnMeta> templateColumns = getTemplateColumns(locale);

        if (null == uploadedColumns || uploadedColumns.size() < 2)
        {
            error = "Missing mandatory columns!";
        }
        else if (uploadedColumns.get(templateColumns.get(0).getGuid()) == null
                || uploadedColumns.get(templateColumns.get(1).getGuid()) == null)
        {
            error = "Invalid mandatory columns!";
        }

        return error;
    }

    @Override
    public void importRow(XSSFRow importRow, ColumnMetaMap columnMeta,
            UserDetailsImpl importUser, HttpServletRequest request)
    {
        OrbisHibernateTemplate ht = PortalUtils.getHt();
        Locale locale = PortalUtils.getLocale(request);
        CoopWtrModelAbstract model = getWtrModel();

        String userId = ImportHelper.getCellValue(importRow,
                columnMeta.get(ENTITY_KEY, "User ID"));
        String termId = ImportHelper.getCellValue(importRow,
                columnMeta.get(ENTITY_KEY, "Coop Term"));
        String status = ImportHelper.getCellValue(importRow,
                columnMeta.get(ENTITY_KEY, "Status"));

        // Fix for excel changing dashes to hyphens
        termId = termId.replaceAll("–", "-");

        CoopAdmissionSeq cas = ht.getFirst(
                "from CoopAdmissionSeq cas where cas.admission.student.username=? "
                        + " and cas.actualTerm.name=? and cas.admission.coopTermProgram.term.module=?",
                userId, termId, coopModule);
        if (cas == null)
        {
            throw new ImportRowException(new I18nLabel(
                    "i18n.CoopWtrImportUsecase.Nomatching2418855932503604")
                    .getTranslation(locale));
        }
        CoopWtrAbstract wtr;
        try
        {
            wtr = (CoopWtrAbstract) ClassUtils
                    .loadClass("com.orbis.web.content.coop." + model.getClass()
                            .getSimpleName().replaceAll("Model", ""))
                    .newInstance();
        }
        catch (Exception e)
        {
            logger.error(
                    "Error occurred while trying to create new work term record",
                    e);
            throw new ImportRowException(new I18nLabel(
                    "i18n.CoopWtrImportUsecase.Erroroccur7619526680184934")
                    .getTranslation(locale));
        }
        boolean isWtrMain = wtr instanceof CoopWtrMain;

        wtr.setSeq(cas);
        wtr.setOwner(cas.getAdmission().getStudent());
        wtr.setCreatedBy(importUser);
        wtr.setDateCreated(new Date());
        wtr.setDfModel(model.getDFModel());
        wtr.getSeq().setPlaced(true);
        if (!StringUtils.isEmpty(status))
        {
            wtr.setStatus(status);
        }

        if (isWtrMain)
        {
            String employer = ImportHelper.getCellValue(importRow,
                    columnMeta.get(ENTITY_KEY, "Employer Username"));
            String jobId = ImportHelper.getCellValue(importRow,
                    columnMeta.get(ENTITY_KEY, "Job ID"));

            CoopWtrMain mainWtr = (CoopWtrMain) wtr;
            mainWtr = CoopWtrMainHelper.updateWithExpSnapshot(mainWtr,
                    mainWtr.getOwner().getMajor1Code(),
                    mainWtr.getOwner().getYearLevel(), new Date());

            if (cas.getAdmission().getCoopTermProgram().getTerm().getModule()
                    .isEnableRankAndMatch())
            {
                mainWtr.setDirectPlaced(true);
            }

            mainWtr.setWorkTermSequence(ht.findInt(
                    "select max(m.workTermSequence) from CoopWtrMain m where m.seq = ?",
                    cas) + 1);

            if (!StringUtils.isEmpty(jobId))
            {
                try
                {
                    NPosting posting = (NPosting) ht.load(NPosting.class,
                            new Integer(jobId));

                    mainWtr.setJobPosting(posting);
                    if (StringUtils.isEmpty(employer))
                    {
                        mainWtr.setEmployerContact(posting.getPostedBy());
                    }
                }
                // we ignore the job id column if there is no job with
                // the specified ID
                catch (Exception e)
                {
                    logger.warn("No job with the provided ID was found", e);
                }
            }

            if (!StringUtils.isEmpty(employer))
            {
                mainWtr.setEmployerContact(
                        UserDetailsHelper.getUserByUsername(employer));
            }
        }
        else
        {
            int workTermSequence = NumberUtils
                    .parseInt(ImportHelper.getCellValue(importRow,
                            columnMeta.get(ENTITY_KEY, "Work Term Sequence")), 1);
            List<Integer> wtrIds = ht.find(
                    "select wtr.id from CoopWtrMain wtr where wtr.seq=? order by wtr.id asc",
                    new Object[] { cas });

            if (wtrIds.size() == 1)
            {
                wtr.setMainRecordId(wtrIds.get(0));
            }
            else if (wtrIds.size() > 1 && workTermSequence >= 1)
            {
                wtr.setMainRecordId(
                        wtrIds.get(Math.min(workTermSequence, wtrIds.size()) - 1));
            }
            else
            {
                throw new ImportRowException(new I18nLabel(
                        "i18n.CoopWtrImportUsecase.0worktermr0851522194333320",
                        List.of(wtrIds.size())).getTranslation(locale));
            }
        }

        columnMeta.stream()
                .filter(c -> !c.getFieldMapping().equals("User ID")
                        && !c.getFieldMapping().equals("Coop Term")
                        && !c.getFieldMapping().equals("Employer Username")
                        && !c.getFieldMapping().equals("Job ID")
                        && !c.getFieldMapping().equals("Status")
                        && !c.getFieldMapping().equals("Work Term Sequence"))
                .forEach(c -> bindValue(wtr,
                        ImportHelper.getCellValue(importRow, c), c));

        ht.saveOrUpdate(wtr);

        if (isWtrMain)
        {
            cas.setPlaced(true);
            ht.saveOrUpdate(cas);
        }
    }

    private void bindValue(DFAnswerEntity entity, String value,
            ColumnMeta columnMeta)
    {
        String fieldMapping = columnMeta.getFieldMapping();
        value = wrapInCarets(fieldMapping, value, entity);
        DFHelper.processFieldMappings(value, entity, fieldMapping);
    }

    private String wrapInCarets(String fieldMapping, String value,
            DFAnswerEntity entity)
    {
        Optional<DFQuestion> question = PortalUtils.getHt().findFirst(
                "from DFQuestion dfq where dfq.answerField1=? and dfq.category.model.modelEntityClassName=?",
                new Object[] { fieldMapping,
                        entity.getDfModel().getModelEntityClassName() });
        if (question.isPresent()
                && question.get().getType() == DFQuestion.TYPE_MULTI_CHOICE
                && !StringUtils.isEmpty(value) && !value.startsWith("^"))
        {
            StringBuilder result = new StringBuilder("^");
            String[] values = value.split("[,;\n]");
            if (PortalUtils.isSiteInMultilingualMode())
            {
                for (String c : values)
                {
                    String patternEn = String.format("^%s\\|.*$", c.trim());
                    String patternFr = String.format(".*\\|%s.*$", c.trim());
                    String[] matches = question.get().getChoices().split("\\r?\\n");
                    for (String match : matches)
                    {
                        if (match.matches(patternEn) || match.matches(patternFr))
                        {
                            result.append(match).append("^");
                        }
                    }
                }
            }
            else
            {
                for (String s : values)
                {
                    result.append(s.trim()).append("^");
                }
            }
            return result.toString();
        }

        return value;
    }

    @Override
    public List<ColumnMeta> getTemplateColumns(
            POIXMLProperties.CoreProperties coreProps, Locale locale)
    {
        return getTemplateColumns(locale);
    }

    @Override
    public List<ColumnMeta> getTemplateColumns(Locale locale)
    {
        List<ColumnMeta> columns = new ArrayList<>();

        CoopWtrModelAbstract wtrModel = getWtrModel();

        ColumnMeta col = null;

        // ADD MANDATORY COLUMNS...
        col = new ColumnMeta(ENTITY_KEY, "User ID", "User ID", true, null,
                DATATYPE.TEXT);
        col.setOptionalInclude(false);
        col.setOptionalRequired(false);
        columns.add(col);

        col = new ColumnMeta(ENTITY_KEY, "Coop Term", "Coop Term", true, null,
                DATATYPE.TEXT);
        col.setOptionalInclude(false);
        col.setOptionalRequired(false);
        columns.add(col);

        if (wtrModel instanceof CoopWtrModelMain)
        {
            col = new ColumnMeta(ENTITY_KEY, "Employer Username",
                    "Employer Username", false, null, DATATYPE.TEXT);
            col.setOptionalInclude(false);
            col.setOptionalRequired(true);
            columns.add(col);

            col = new ColumnMeta(ENTITY_KEY, "Job ID", "Job ID", false, null,
                    DATATYPE.TEXT);
            col.setOptionalInclude(false);
            col.setOptionalRequired(true);
            columns.add(col);
        }

        col = new ColumnMeta(ENTITY_KEY, "Status", "Status", false, null,
                DATATYPE.TEXT);
        col.setOptionalInclude(false);
        col.setOptionalRequired(true);
        col.setAcceptedValues(new String[] { CoopWtrAbstract.STATUS_APPROVED,
                CoopWtrAbstract.STATUS_DECLINED, CoopWtrAbstract.STATUS_INACTIVE,
                CoopWtrAbstract.STATUS_PENDING });
        columns.add(col);

        if (wtrModel != null)
        {
            if (!(wtrModel instanceof CoopWtrModelMain))
            {
                col = new ColumnMeta(ENTITY_KEY, "Work Term Sequence",
                        "Work Term Sequence", false, null, DATATYPE.TEXT);
                col.setOptionalInclude(false);
                col.setOptionalRequired(true);
                columns.add(col);
            }

            try
            {
                Class wtrClass = ClassUtils.loadClass(
                        "com.orbis.web.content.coop." + wtrModel.getClass()
                                .getSimpleName().replaceAll("Model", ""));
            }
            catch (ClassNotFoundException e)
            {
                logger.error(
                        "Error occurred while trying to instantiate work term record class",
                        e);
            }

            List<DFQuestion> questions = DFHelper
                    .getModelQuestions(wtrModel.getDFModel());
            for (DFQuestion q : questions)
            {
                col = new ColumnMeta(ENTITY_KEY, q.getQuestionText(),
                        q.getAnswerField1(), false, null,
                        DFHelper.getDataTypeEquivalent(q));
                if (q.getType() == DFQuestion.TYPE_MATRIX_MULTI
                        || q.getType() == DFQuestion.TYPE_MATRIX_SINGLE)
                {
                    col.setDataFormat("^row~column^row~column^etc.");
                }
                if (q.getType() == DFQuestion.TYPE_MULTI_CHOICE)
                {
                    col.setDataFormat("^value^value^etc.");
                }
                columns.add(col);
            }
        }

        return columns;
    }

    @Override
    public Map<XSSFCell, String> validateRow(XSSFRow importRow,
            ColumnMetaMap columnMeta)
    {
        XSSFCell cellUserID = null, cellCoopTerm = null,
                cellWorkTermSequence = null;
        String userIDValue = "", coopTermValue = "";
        String workTermSequenceValue = "";

        Map<XSSFCell, String> errors = new LinkedHashMap<>();

        for (ColumnMeta colMeta : columnMeta.values())
        {
            XSSFCell cell = importRow.getCell(
                    colMeta.getImportOptions().getColumnIndex(),
                    XSSFRow.CREATE_NULL_AS_BLANK);

            String cellValue;
            if (XSSFCell.CELL_TYPE_STRING != cell.getCellType())
            {
                cellValue = cell.getRawValue();
                cell.setCellType(XSSFCell.CELL_TYPE_STRING);
            }
            else
            {
                cellValue = cell.getStringCellValue();
            }

            if (("User ID").equals(colMeta.getFieldMapping()))
            {
                userIDValue = cellValue;
                cellUserID = cell;
            }
            else if (("Coop Term").equals(colMeta.getFieldMapping()))
            {
                coopTermValue = cellValue;
                cellCoopTerm = cell;
            }
            else if (("Work Term Sequence").equals(colMeta.getFieldMapping()))
            {
                workTermSequenceValue = cellValue;
                cellWorkTermSequence = cell;
            }
        }

        Integer userId = PortalUtils.getJt().findInt(
                "select u.user_details_id from user_details u where u.username = ?",
                userIDValue);

        if (userId == null)
        {
            errors.put(cellUserID,
                    new I18nLabel(
                            "i18n.CoopWtrImportUsecase.Thisuserdo9215972632829013")
                            .getTranslation());

            if (PortalUtils.getJt().notExists(
                    "select 1 from coop_term t where t.name = ? and t.module=?",
                    coopTermValue, coopModule.getId()))
            {
                errors.put(cellCoopTerm, new I18nLabel(
                        "i18n.CoopWtrImportUsecase.Atermwitht1895546052035163")
                        .getTranslation());
            }
        }
        else if (CoopWtrModelMain.MODEL_KEY.equals(this.wtrModelKey))
        {
            if (PortalUtils.getHt().findInt(
                    "select count(cas.id) from CoopAdmissionSeq cas where cas.admission.student.username=? "
                            + " and cas.actualTerm.name=? and cas.admission.coopTermProgram.term.module=?",
                    userIDValue, coopTermValue, coopModule) == 0)
            {
                errors.put(cellCoopTerm, new I18nLabel(
                        "i18n.CoopWtrImportUsecase.Nomatching2418855932503604")
                        .getTranslation());
            }
        }
        else
        {
            int wtrCount = PortalUtils.getHt().findInt(
                    "select count(wtr.id) from CoopWtrMain wtr join wtr.seq cas join cas.actualTerm ct where wtr.owner.id=? and ct.name=? and ct.module=?",
                    userId, coopTermValue, coopModule);

            if (StringUtils.isNotEmpty(workTermSequenceValue)
                    && !NumberUtils.isInteger(workTermSequenceValue)
                    || NumberUtils.parseInt(workTermSequenceValue, 1) < 1)
            {
                if (wtrCount > 1)
                {
                    errors.put(cellWorkTermSequence, new I18nLabel(
                            "i18n.CoopWtrImportUsecase.Worktermse1050586629700563",
                            List.of(wtrCount)).getTranslation());
                }
                else
                {
                    errors.put(cellWorkTermSequence, new I18nLabel(
                            "i18n.CoopWtrImportUsecase.Worktermse8756887204881547")
                            .getTranslation());
                }
            }

            if (wtrCount <= 0)
            {
                errors.put(cellCoopTerm, new I18nLabel(
                        "i18n.CoopWtrImportUsecase.Nomatching4955105415900245")
                        .getTranslation());
            }
        }

        return errors;
    }

    private CoopWtrModelAbstract getWtrModel()
    {
        return PortalUtils.getHt()
                .getFirst(
                        "select m from " + CoopHelper.getCoopWtrModelClassNames()
                                .get(wtrModelKey) + " m where m.coopModule=?",
                        coopModule);
    }
}