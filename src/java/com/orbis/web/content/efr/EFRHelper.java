package com.orbis.web.content.efr;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.context.MessageSource;
import org.springframework.orm.hibernate.HibernateTemplate;
import org.springframework.web.servlet.ModelAndView;

import com.orbis.acegi.providers.dao.hibernate.PersonGroupHelper;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.search.SearchModel;
import com.orbis.search.criteria.CriteriaAnswer;
import com.orbis.search.criteria.CriteriaGroup;
import com.orbis.search.criteria.CriteriaModel;
import com.orbis.search.criteria.CriteriaQuestion;
import com.orbis.search.entity.Entity;
import com.orbis.search.entity.Relationship;
import com.orbis.utils.DBUtils;
import com.orbis.utils.NumberUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.ecommerce.EcommerceHelper;
import com.orbis.web.content.ecommerce.EcommerceOrder;
import com.orbis.web.content.ecommerce.EcommerceTax;
import com.orbis.web.site.SiteElement;

public final class EFRHelper
{
    public static final String SEARCH_TYPE_APPROVED_PROFILES = "approvedProfiles";

    public static final String SEARCH_TYPE_PENDING_PROFILES = "pendingProfiles";

    public static final String SEARCH_TYPE_INCOMPLETE_PROFILES = "incompleteProfiles";

    public static final String SEARCH_TYPE_UNPAID_REGISTRATIONS = "unpaidRegistrations";

    public static final String SEARCH_TYPE_PAID_REGISTRATIONS = "paidRegistrations";

    public static final String SEARCH_TYPE_CANCELED = "canceled";

    public static final String SEARCH_TYPE_INCOMPLETE = "incomplete";

    public static final String SEARCH_TYPE_COMPLETE = "complete";

    public static final String ORDER_NUMBER_PREFIX = "FAIR";

    public static List<String> organizationTypes;

    static
    {
        organizationTypes = new LinkedList<String>();
        organizationTypes.add("Agriculture/Forestry");
        organizationTypes.add("Architecture/Construction/Landscaping");
        organizationTypes.add("Consulting (Engineering)");
        organizationTypes.add("Consulting (Management)");
        organizationTypes.add("Education/Teaching");
        organizationTypes.add("Environmental");
        organizationTypes.add("Finance/Accounting/Banking/Insurance");
        organizationTypes.add("Government");
        organizationTypes.add("Health/Healthcare");
        organizationTypes.add("Hospitality/Tourism/Travel");
        organizationTypes.add("Information Technology/Software");
        organizationTypes.add("Law Enforcement/Security");
        organizationTypes.add("Manufacturing/Food Processing");
        organizationTypes.add("Marketing/Advertising/Communications");
        organizationTypes.add("Professional Association");
        organizationTypes.add("Real Estate/Property Management");
        organizationTypes.add("Recruitment/Staffing");
        organizationTypes.add("Retail/Wholesale");
        organizationTypes.add("Science/Labs/Pharmaceutical");
        organizationTypes.add("Social Services");
        organizationTypes.add("Sports/Recreation");
        organizationTypes.add("Supply Chain");
        organizationTypes.add("Telecommunications");
        organizationTypes.add("Utilities/Mining/Oil and Gas");
        organizationTypes.add("Other");
    }

    private EFRHelper()
    {
    }

    static void initializePermissions()
    {
        PersonGroupHelper.initializePermissions(null,
                PersonGroupHelper.EFR_CAN_MANAGE_REGISTRATION,
                PersonGroupHelper.EFR_CAN_EDIT_GUIDEBOOK,
                PersonGroupHelper.EFR_CAN_SWITCH_REGISTRATION_BOOTHS);
    }

    public static void getFloorplan(ModelAndView mv, List<Integer> adjBoothIds,
            Integer statusFilter)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        Map<String, EFRBooth> ret = new HashMap<String, EFRBooth>();

        String additionalParameters = "";
        String registrationRestriction = "";

        if (adjBoothIds != null)
        {
            additionalParameters = " where b.id in "
                    + DBUtils.buildInClause(adjBoothIds);

            registrationRestriction = " where r.booth1.id in "
                    + DBUtils.buildInClause(adjBoothIds);
        }

        if (statusFilter != null)
        {
            if (adjBoothIds != null)
            {
                additionalParameters += " and b.status = "
                        + statusFilter.toString();
            }
            else
            {
                additionalParameters += " where b.status = "
                        + statusFilter.toString();
            }
        }
        List<EFRBooth> bs = ht.find("from EFRBooth b " + additionalParameters);

        // r.booth1, r.booth2, r.org, r.guidebookData,
        Map<Integer, EFRBooth> booth2Collection = new HashMap<Integer, EFRBooth>();

        List<Object[]> registrations = ht
                .find("select r.id, r.org, r.booth1 from EFRRegistration r "
                        + registrationRestriction);

        List<Object[]> registrationBooth2 = ht
                .find("select r.id, r.booth2 from EFRRegistration r "
                        + registrationRestriction);
        for (Object[] o : registrationBooth2)
        {
            booth2Collection.put((Integer) o[0], (EFRBooth) o[1]);
        }

        Map<Integer, Map<String, Object>> bookedBoothMap = new HashMap<Integer, Map<String, Object>>();

        for (EFRBooth b : bs)
        {
            ret.put(b.getCode(), b);
        }

        for (Object[] reg : registrations)
        {
            Map<String, Object> details = new HashMap<String, Object>();
            details.put("org", reg[1]);
            details.put("id", reg[0]);
            if (reg[2] != null)
            {
                bookedBoothMap.put(((EFRBooth) reg[2]).getId(), details);
            }

            if (booth2Collection.containsKey(reg[0]))
            {
                bookedBoothMap.put(booth2Collection.get(reg[0]).getId(), details);
            }
        }

        mv.addObject("booths", ret);
        mv.addObject("bookedBoothMap", bookedBoothMap);
    }

    public static List<Integer> getAdjacentBooths(EFRBooth b)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        List<Integer> booths1 = ht.find(
                "select a.booth1.id from EFRBoothsAdjacent a where a.booth2 = ?",
                b);
        List<Integer> booths2 = ht.find(
                "select a.booth2.id from EFRBoothsAdjacent a where a.booth1 = ?",
                b);
        booths1.removeAll(booths2);
        booths1.addAll(booths2);
        return booths1;
    }

    public static SearchModel getSearchModel_Registrations(String searchType,
            EFRModule efrModule, SiteElement se, Locale orbisLocale)
    {
        MessageSource messageSource = PortalUtils.getMessageSource();

        SearchModel searchModel = new SearchModel();
        searchModel.setOwnerModuleClassName(se.getContentItemClass().getName());
        searchModel.setOwnerModuleId(se.getContentItemId().toString());
        searchModel.setCanSave(true);
        searchModel.setCanExport(true);
        searchModel.setCanViewCriteria(true);
        searchModel.setCanViewDetails(true);
        searchModel.setShowQuestionOrder(false);
        searchModel.setState(SearchModel.STATE_CONFIG_MASTER);

        Entity master = new Entity(
                "i18n.gridSearch.criteriaModel.entities.Registrations",
                EFRRegistration.class, "r", "id",
                getCriteriaModel_Registrations(searchType, orbisLocale),
                "r.dateRegistered", "desc", true);

        if (!StringUtils.isEmpty(searchType))
        {
            searchModel.setCanViewCriteria(false);
            searchModel.setState(SearchModel.STATE_SHOW_RESULTS);

            List<Integer> filterIds = null;

            if (searchType.equals(SEARCH_TYPE_COMPLETE))
            {
                master.setEntityLabel(messageSource.getMessage(
                        "i18n.gridSearch.criteriaModel.entities.Registrations.Completed",
                        null, orbisLocale));
                filterIds = getCompletedRegistrationIds(efrModule);
            }
            else if (searchType.equals(SEARCH_TYPE_INCOMPLETE))
            {
                master.setEntityLabel(messageSource.getMessage(
                        "i18n.gridSearch.criteriaModel.entities.Registrations.Incompleted",
                        null, orbisLocale));
                filterIds = getIncompleteRegistrationIds(efrModule);
            }
            else if (searchType.equals(SEARCH_TYPE_CANCELED))
            {
                master.setEntityLabel(messageSource.getMessage(
                        "i18n.gridSearch.criteriaModel.entities.Registrations.Canceled",
                        null, orbisLocale));
                filterIds = getCanceledRegistrationIds(efrModule);
            }
            else if (searchType.equals(SEARCH_TYPE_PAID_REGISTRATIONS))
            {
                master.setEntityLabel(messageSource.getMessage(
                        "i18n.gridSearch.criteriaModel.entities.Registrations.Paid",
                        null, orbisLocale));
                filterIds = getPaidRegistrationIds(efrModule);
            }
            else if (searchType.equals(SEARCH_TYPE_UNPAID_REGISTRATIONS))
            {
                master.setEntityLabel(messageSource.getMessage(
                        "i18n.gridSearch.criteriaModel.entities.Registrations.Unpaid",
                        null, orbisLocale));
                filterIds = getUnpaidRegistrationIds(efrModule);
            }
            else if (searchType.equals(SEARCH_TYPE_INCOMPLETE_PROFILES))
            {
                master.setEntityLabel(messageSource.getMessage(
                        "i18n.gridSearch.criteriaModel.entities.Registrations.IncompleteProfiles",
                        null, orbisLocale));
                filterIds = getRegistrationIdsWithIncompleteProfiles(efrModule);
            }
            else if (searchType.equals(SEARCH_TYPE_PENDING_PROFILES))
            {
                master.setEntityLabel(messageSource.getMessage(
                        "i18n.gridSearch.criteriaModel.entities.Registrations.PendingProfiles",
                        null, orbisLocale));
                filterIds = getRegistrationIdsWithPendingProfiles(efrModule);
            }
            else if (searchType.equals(SEARCH_TYPE_APPROVED_PROFILES))
            {
                master.setEntityLabel(messageSource.getMessage(
                        "i18n.gridSearch.criteriaModel.entities.Registrations.ApprovedProfiles",
                        null, orbisLocale));
                filterIds = getRegistrationIdsWithApprovedProfiles(efrModule);
            }

            String staticWhereHql = " and r.id in "
                    + DBUtils.buildInClause(filterIds);

            master.setStaticWhereHql(staticWhereHql);
        }

        master.setDataSausageClass(EFRAdminSearchDataSausage.class);

        List<Relationship> relationships = new ArrayList<Relationship>();
        relationships.add(EcommerceHelper.getRelationship_OrderHistory(
                EFRRegistration.class, "Registration Orders", orbisLocale));
        master.setRelationships(relationships);

        searchModel.setMasterEntity(master);

        return searchModel;
    }

    private static CriteriaModel getCriteriaModel_Registrations(String searchType,
            Locale locale)
    {
        CriteriaModel criteriaModel = new CriteriaModel();
        CriteriaGroup cg = null;
        LinkedList<CriteriaQuestion> questions = null;
        CriteriaQuestion q = null;

        cg = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.OrganizationInformation");
        questions = new LinkedList<CriteriaQuestion>();

        if (StringUtils.isEmpty(searchType))
        {
            CriteriaAnswer a = null;

            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.RegistrationCompleted");
            q.setQuestionKey("r.completed");
            q.setType(CriteriaQuestion.TYPE_BOOLEAN);
            q.setColWidth(150);
            a = new CriteriaAnswer();
            a.setValue("1");
            q.setAnswer(a);
            questions.add(q);

            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.RegistrationCanceled");
            q.setQuestionKey("r.canceled");
            q.setType(CriteriaQuestion.TYPE_BOOLEAN);
            q.setColWidth(150);
            a = new CriteriaAnswer();
            a.setValue("0");
            q.setAnswer(a);
            questions.add(q);
        }

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.RegistrationDate");
        q.setQuestionKey("r.dateRegistered");
        q.setType(CriteriaQuestion.TYPE_DATE);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.organization");
        q.setQuestionKey("r.org");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.AddressLine.1");
        q.setQuestionKey("r.address1");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.AddressLine.2");
        q.setQuestionKey("r.address2");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.City");
        q.setQuestionKey("r.city");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Province");
        q.setQuestionKey("r.province");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.PostalCode");
        q.setQuestionKey("r.postalCode");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Country");
        q.setQuestionKey("r.country");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(getCountriesMap());
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.OtherCountry");
        q.setQuestionKey("r.countryOther");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Website");
        q.setQuestionKey("r.website");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.MemberOf");
        q.setQuestionKey("r.discount");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(getDiscountMap());
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.organizationType");
        q.setQuestionKey("r.orgType");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(getOrgTypeMap());
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.OtherOrganizationType");
        q.setQuestionKey("r.orgTypeOther");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.CharitableRegistrationNumber");
        q.setQuestionKey("r.charityNumber");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.ThirdPartyRecruiter");
        q.setQuestionKey("r.thirdParty");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(getYesNoMap());
        q.setColWidth(150);
        questions.add(q);

        cg.setQuestions(questions);
        criteriaModel.addCriteriaGroup(cg);

        cg = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.ContactInformation");
        questions = new LinkedList<CriteriaQuestion>();

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.username");
        q.setQuestionKey("r.user.username");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.firstName");
        q.setQuestionKey("r.firstName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.lastName");
        q.setQuestionKey("r.lastName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Title");
        q.setQuestionKey("r.title");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.phoneNumber");
        q.setQuestionKey("r.tel");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.phoneExt");
        q.setQuestionKey("r.ext");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.FaxNumber");
        q.setQuestionKey("r.fax");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.emailAddress");
        q.setQuestionKey("r.email");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Notes");
        q.setQuestionKey("r.contactNotes");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        cg.setQuestions(questions);
        criteriaModel.addCriteriaGroup(cg);

        cg = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.FairInformation");
        questions = new LinkedList<CriteriaQuestion>();

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.NumberOfBooths");
        q.setQuestionKey("r.booths");
        q.setType(CriteriaQuestion.TYPE_NUMBER);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Booth.1");
        q.setQuestionKey("r.booth1.code");
        q.setType(CriteriaQuestion.TYPE_NUMBER);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Booth.2");
        q.setQuestionKey("r.booth2.code");
        q.setType(CriteriaQuestion.TYPE_NUMBER);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.NumberOfRepresentatives");
        q.setQuestionKey("r.representatives");
        q.setType(CriteriaQuestion.TYPE_NUMBER);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Guidebook.SubmittingInfo");
        q.setQuestionKey("r.guidebook");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(getYesNoMap());
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Guidebook.AdditionalEntry");
        q.setQuestionKey("r.subsidiary");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(getYesNoMap());
        q.setColWidth(150);
        questions.add(q);

        Map<String, String> prev = getYesNoMap();
        prev.put("not sure", "Not Sure");
        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.ParticipatedPreviousFairs");
        q.setQuestionKey("r.participated");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(prev);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.SpecialRequests");
        q.setQuestionKey("r.requests");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.SpecialRequests");
        q.setQuestionKey("r.requests");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        cg.setQuestions(questions);
        criteriaModel.addCriteriaGroup(cg);

        cg = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.GuidebookInformation");
        questions = new LinkedList<CriteriaQuestion>();

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.organizationProfileStatus");
        q.setQuestionKey("r.guidebookStatus");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        HashMap<String, String> guideBookStatuses = new LinkedHashMap<String, String>();
        guideBookStatuses.put("0", "Pending");
        guideBookStatuses.put("1", "Approved");
        q.setOptionChoices(guideBookStatuses);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.organizationProfile");
        q.setQuestionKey("r.guidebookSub");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setDisplayFormatMultiline(
                CriteriaQuestion.DISPLAY_FORMAT_MULTILINE_FCK_RICH);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.PositionsAvailable");
        q.setQuestionKey("r.positionsAvailable");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setDisplayFormatMultiline(
                CriteriaQuestion.DISPLAY_FORMAT_MULTILINE_FCK_RICH);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Employment.Types");
        q.setQuestionKey("r.empType");
        q.setType(CriteriaQuestion.TYPE_CHOICE_MULTI);
        q.setOperation(CriteriaQuestion.OPERATION_CONTAINS);
        q.setAnswerDelimiter("|");
        q.setOptionChoices(getEmploymentTypesMap());
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Employment.Locations");
        q.setQuestionKey("r.empLocation");
        q.setType(CriteriaQuestion.TYPE_CHOICE_MULTI);
        q.setOperation(CriteriaQuestion.OPERATION_CONTAINS);
        q.setAnswerDelimiter("|");
        q.setOptionChoices(getEmploymentLocationsMap());
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.LinkedIn");
        q.setQuestionKey("r.linkedIn");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Twitter");
        q.setQuestionKey("r.twitter");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Facebook");
        q.setQuestionKey("r.facebook");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.contactInformation");
        q.setQuestionKey("r.contactInfo");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setDisplayFormatMultiline(
                CriteriaQuestion.DISPLAY_FORMAT_MULTILINE_FCK_RICH);
        q.setColWidth(150);
        questions.add(q);

        cg.setQuestions(questions);
        criteriaModel.addCriteriaGroup(cg);

        cg = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.PaymentInformation");
        questions = new LinkedList<CriteriaQuestion>();

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Registration.PaymentDate");
        q.setQuestionKey("r.datePayed");
        q.setType(CriteriaQuestion.TYPE_DATE);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Registration.PaymentMethod");
        q.setQuestionKey("r.ecommerceOrder.paymentType");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(getPaymentTypeMap());
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Registration.PaymentStatus");
        q.setQuestionKey("r.ecommerceOrder.status");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(EcommerceHelper.getSearchOptions_PaymentStatus(locale));
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Registration.TotalCost");
        q.setQuestionKey("r.totalCost");
        q.setType(CriteriaQuestion.TYPE_NUMBER);
        q.setColWidth(150);
        q.setColAlignment("right");
        questions.add(q);

        cg.setQuestions(questions);
        criteriaModel.addCriteriaGroup(cg);

        cg = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.BrandingOpportunities");
        questions = new LinkedList<CriteriaQuestion>();

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.WebsiteLogo");
        q.setQuestionKey("r.logoForWebsite");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(getYesNoMap());
        q.setColWidth(150);
        questions.add(q);

        Map<String, String> adInGuidebook = new LinkedHashMap<String, String>();
        adInGuidebook.put("no", "No");
        adInGuidebook.put("half", "Half - 7.5\" x 4.75\"");
        adInGuidebook.put("full", "Full - 7.5\" x 9.5\"");

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.AdInGuidebook");
        q.setQuestionKey("r.adInGuidebook");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(adInGuidebook);
        q.setColWidth(150);
        questions.add(q);

        cg.setQuestions(questions);
        criteriaModel.addCriteriaGroup(cg);

        return criteriaModel;
    }

    public static Integer getInteger(HttpServletRequest request, String key)
    {
        Integer i = null;

        if (request.getAttribute(key) != null)
        {
            i = (Integer) request.getAttribute(key);
        }
        else if (request.getParameter(key) != null)
        {
            String id = request.getParameter(key);

            if (!StringUtils.isInteger(id))
            {
                id = StringUtils.DES_decrypt(id);
            }

            if (StringUtils.isInteger(id))
            {
                i = new Integer(id);
            }
        }

        return i;
    }

    public static EFRRegistration getRegistration(Integer registrationId)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        EFRRegistration r = null;

        try
        {
            r = (EFRRegistration) ht.load(EFRRegistration.class, registrationId);
        }
        catch (Exception e)
        {
            // e.printStackTrace();
        }

        return r;
    }

    public static EFRRegistration getRegistration(HttpServletRequest request)
    {
        return getRegistration(getInteger(request, "registrationId"));
    }

    public static EFRBooth getBooth(HttpServletRequest request)
    {
        return getBooth(getInteger(request, "boothId"));
    }

    public static EFRBooth getBooth(Integer boothId)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        EFRBooth b = null;

        try
        {
            b = (EFRBooth) ht.load(EFRBooth.class, boothId);
        }
        catch (Exception e)
        {
        }

        return b;
    }

    public static void populatePriceParamsForEdit(ModelAndView mv,
            EFRRegistration r)
    {
        double totalCost = r.getModule().getStandardPrice();
        if (r.getDiscount().equals("cacee"))
        {
            totalCost = r.getModule().getCaceePrice();
        }
        else if (r.getDiscount().equals("charity"))
        {
            totalCost = r.getModule().getCharityPrice();
        }

        mv.addObject("registrationFee", totalCost);

        totalCost = totalCost * r.getBooths();
        if (r.getBooth1() != null
                && r.getBooth1().getType() == EFRBooth.TYPE_PREMIUM)
        {
            totalCost += r.getModule().getPremiumPrice() * r.getBooths();
        }

        mv.addObject("totalBeforeTotal", totalCost);

        if (r.getBooths() == 1 && r.getRepresentatives() > 4)
        {
            totalCost += (r.getRepresentatives() - 4) * r.getModule().getRepPrice();
            mv.addObject("repCalcPrice",
                    (r.getRepresentatives() - 4) * r.getModule().getRepPrice());
        }
        else if (r.getBooths() == 2 && r.getRepresentatives() > 8)
        {
            totalCost += (r.getRepresentatives() - 8) * r.getModule().getRepPrice();
            mv.addObject("repCalcPrice",
                    (r.getRepresentatives() - 8) * r.getModule().getRepPrice());
        }

        if (r.getLogoForWebsite() != null && r.getLogoForWebsite().equals("yes"))
        {
            totalCost += r.getModule().getLogoForWebsite();
        }

        if (r.getAdInGuidebook() != null && r.getAdInGuidebook().equals("half"))
        {
            totalCost += r.getModule().getGuidebookAdHalf();
            mv.addObject("guidebookAd", r.getModule().getGuidebookAdHalf());
        }
        else if (r.getAdInGuidebook() != null
                && r.getAdInGuidebook().equals("full"))
        {
            totalCost += r.getModule().getGuidebookAdFull();
            mv.addObject("guidebookAd", r.getModule().getGuidebookAdFull());
        }

        if (r.getSubsidiary() != null && r.getSubsidiary().equals("yes"))
        {
            totalCost += r.getModule().getSubsidiaryPrice();
        }

        mv.addObject("premiumPrice", r.getModule().getPremiumPrice());
        mv.addObject("logoForWebsite", r.getModule().getLogoForWebsite());

        mv.addObject("repPrice", r.getModule().getRepPrice());
        mv.addObject("subsidiaryPrice", r.getModule().getSubsidiaryPrice());
        // mv.addObject("taxes", totalCost * r.getModule().getTax());
        // mv.addObject("finalTotal", totalCost + (totalCost *
        // r.getModule().getTax()));
        mv.addObject("taxes", 0d);
        mv.addObject("finalTotal", totalCost);
    }

    public static ModelAndView displayGuidebookDetails(HttpServletRequest request,
            String comingFrom)
    {
        EFRRegistration r = getRegistration(request);
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        ModelAndView mv = new ModelAndView("efr/efr_guidebookDetails");
        mv.addObject("comingFrom", comingFrom);
        mv.addObject("registration", r);

        EFRGuidebook guidebook = new EFRGuidebook();

        if (r.getGuidebookData() != null)
        {
            guidebook = r.getGuidebookData();
        }

        mv.addObject("readOnly", Boolean.TRUE);

        if (userLoggedIn != null)
        {
            if (r.getGuidebookData() != null)
            {
                if ((userLoggedIn.getId().equals(r.getId()) && r.getGuidebookData()
                        .getStatus() == EFRGuidebook.STATUS_PENDING)
                        || userLoggedIn.getAssignedTypes().containsKey(
                                PersonGroupHelper.EFR_CAN_EDIT_GUIDEBOOK))
                {
                    mv.addObject("readOnly", Boolean.FALSE);
                }
            }
            else if (userLoggedIn.getId().equals(r.getId())
                    || userLoggedIn.getAssignedTypes()
                            .containsKey(PersonGroupHelper.EFR_CAN_EDIT_GUIDEBOOK))
            {
                mv.addObject("readOnly", Boolean.FALSE);
            }
        }

        mv.addObject("guidebook", guidebook);
        return mv;
    }

    public static ModelAndView saveGuidebook(HttpServletRequest request,
            String comingFrom)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        EFRGuidebook guidebook = new EFRGuidebook();

        if (!StringUtils.isEmpty(request.getParameter("guidebookId")))
        {
            guidebook = (EFRGuidebook) ht.load(EFRGuidebook.class,
                    new Integer(request.getParameter("guidebookId")));
        }

        guidebook.bindForm(request);
        ht.saveOrUpdate(guidebook);

        EFRRegistration r = getRegistration(request);
        r.setGuidebookData(guidebook);
        ht.update(r);

        ModelAndView mv = displayGuidebookDetails(request, comingFrom);
        mv.addObject("saved", Boolean.TRUE);
        return mv;
    }

    public static ModelAndView changeGuidebookStatus(HttpServletRequest request,
            String comingFrom)
    {
        PortalUtils.getJt().update(
                "update efr_guidebook set status = ? where id = ?",
                new Object[] {
                        new Integer(request.getParameter("status")).intValue(),
                        new Integer(request.getParameter("guidebookId")) });
        return displayGuidebookDetails(request, comingFrom);
    }

    public static EFRModule getEFRModule()
    {
        EFRModule ret = null;
        SiteElement se = PortalUtils.getSiteManager()
                .getFirstElementByType("efrController");

        if (se != null)
        {
            ret = (EFRModule) se.getContentItem();
        }

        return ret;
    }

    public static List<EFRModule> getEFRModules()
    {
        List<EFRModule> ret = new ArrayList<EFRModule>();
        List<SiteElement> efrSes = PortalUtils.getSiteManager()
                .getElementsByType("efrController");

        for (SiteElement se : efrSes)
        {
            EFRModule efrModule = (EFRModule) se.getContentItem();
            if (efrModule != null)
            {
                ret.add(efrModule);
            }
        }

        return ret;
    }

    private static Map<String, String> getOrgTypeMap()
    {
        Map<String, String> orgTypes = new HashMap<String, String>();

        orgTypes.put("Agriculture/Forestry", "Agriculture/Forestry");
        orgTypes.put("Architecture/Construction/Landscaping",
                "Architecture/Construction/Landscaping");
        orgTypes.put("Biotechnology/Pharmaceuticals",
                "Biotechnology/Pharmaceuticals");
        orgTypes.put("Consulting", "Consulting");
        orgTypes.put("Education/Teaching", "Education/Teaching");
        orgTypes.put("Environment", "Environment");
        orgTypes.put("Finance/Accounting/Banking/Insurance",
                "Finance/Accounting/Banking/Insurance");
        orgTypes.put("Government", "Government");
        orgTypes.put("Health/Healthcare", "Health/Healthcare");
        orgTypes.put("Hospitality/Tourism/Travel", "Hospitality/Tourism/Travel");
        orgTypes.put("Information Technology/Software",
                "Information Technology/Software");
        orgTypes.put("Law Enforcement/Security", "Law Enforcement/Security");
        orgTypes.put("Manufacturing", "Manufacturing");
        orgTypes.put("Marketing/Advertising", "Marketing/Advertising");
        orgTypes.put("Media/Broadcasting/Publishing",
                "Media/Broadcasting/Publishing");
        orgTypes.put("Professional Association", "Professional Association");
        orgTypes.put("Recruitment/Staffing", "Recruitment/Staffing");
        orgTypes.put("Retail/Wholesale", "Retail/Wholesale");
        orgTypes.put("Science", "Science");
        orgTypes.put("Social Services", "Social Services");
        orgTypes.put("Sports/Recreation", "Sports/Recreation");
        orgTypes.put("Telecommunications", "Telecommunications");
        orgTypes.put("Transportation", "Transportation");
        orgTypes.put("Utilities/Mining/Oil and Gas",
                "Utilities/Mining/Oil and Gas");

        return orgTypes;
    }

    private static Map<String, String> getEmploymentTypesMap()
    {
        Map<String, String> orgTypes = new HashMap<String, String>();

        orgTypes.put("Full-time", "Full-time");
        orgTypes.put("Summer", "Summer");
        orgTypes.put("Co-op/Internship", "Co-op/Internship");
        orgTypes.put("Contract", "Contract");
        orgTypes.put("Part-time / Casual", "Part-time / Casual");

        return orgTypes;
    }

    private static Map<String, String> getEmploymentLocationsMap()
    {
        Map<String, String> orgTypes = new HashMap<String, String>();

        orgTypes.put("Eastern Canada", "Eastern Canada");
        orgTypes.put("Northern Canada", "Northern Canada");
        orgTypes.put("Ontario (GTA)", "Ontario (GTA)");
        orgTypes.put("Ontario (excluding GTA)", "Ontario (excluding GTA)");
        orgTypes.put("Quebec", "Quebec");
        orgTypes.put("Western Canada", "Western Canada");
        orgTypes.put("U.S.", "U.S.");
        orgTypes.put("Other", "Other");

        return orgTypes;
    }

    private static Map<String, String> getCountriesMap()
    {
        Map<String, String> orgTypes = new HashMap<String, String>();

        orgTypes.put("Canada", "Canada");
        orgTypes.put("USA", "USA");

        return orgTypes;
    }

    private static Map<String, String> getYesNoMap()
    {
        Map<String, String> yesNo = new HashMap<String, String>();

        yesNo.put("Yes", "Yes");
        yesNo.put("No", "No");

        return yesNo;
    }

    private static Map<String, String> getDiscountMap()
    {
        Map<String, String> discounts = new HashMap<String, String>();

        discounts.put("cacee", "Member of the CACEE");
        discounts.put("charity", "Registered Charitable Organization");

        return discounts;
    }

    private static Map<String, String> getPaymentTypeMap()
    {
        Map<String, String> paymentTypes = new HashMap<String, String>();

        paymentTypes.put(String.valueOf(EcommerceOrder.PAYMENT_TYPE_CREDIT_CARD),
                "CREDIT CARD");
        paymentTypes.put(String.valueOf(EcommerceOrder.PAYMENT_TYPE_CHEQUE),
                "CHEQUE");

        return paymentTypes;
    }

    public static EFRRegistration getRegistration(UserDetailsImpl user,
            boolean completed)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        EFRRegistration ret = null;

        List<EFRRegistration> regs = ht.find(
                "from EFRRegistration r where r.user=? and (r.completed=? or r.ecommerceOrder is not null) and r.canceled=0",
                new Object[] { user, completed });

        if (!regs.isEmpty())
        {
            ret = regs.get(0);
        }

        return ret;
    }

    public static ModelAndView getErrorPage(String errMsg)
    {
        ModelAndView mv = new ModelAndView("efr/efr_error");
        mv.addObject("errMsg", errMsg);
        return mv;
    }

    public static void populatePriceParams(ModelAndView mv, EFRRegistration r)
    {
        double totalCost = getTotalCost(r);
        double taxes = getTotalTaxes(totalCost, r.getModule());
        double finalTotal = totalCost + taxes;

        mv.addObject("totalBeforeTotal", totalCost);
        mv.addObject("taxes", taxes);
        mv.addObject("finalTotal", finalTotal);
    }

    public static double getTotalTaxes(double totalCost, EFRModule module)
    {
        double regularTax1Nett = getNettTax(totalCost, module.getDefaultTax1());
        double regularTax2Nett = getNettTax(totalCost, module.getDefaultTax2());
        double regularTax3Nett = getNettTax(totalCost, module.getDefaultTax3());
        return regularTax1Nett + regularTax2Nett + regularTax3Nett;
    }

    private static double getNettTax(double grossCost, EcommerceTax tax)
    {
        double nettTax = 0;

        if (grossCost > 0d && tax != null && tax.getTaxValue() >= 0d)
        {
            nettTax = NumberUtils.round(grossCost * tax.getTaxValue() / 100, 2);
        }

        return nettTax;
    }

    public static void bindRegistration(HttpServletRequest request,
            EFRRegistration r)
    {
        // Guard...
        if (r == null)
        {
            throw new RuntimeException(
                    "Null UserDetailsImpl in EFRRegistration is not allowed!");
        }

        // Guard...
        if (r.getModule() == null)
        {
            throw new RuntimeException(
                    "Null EFRModule in EFRRegistration is not allowed!");
        }

        r.setAdminNotes(request.getParameter("adminNotes"));
        r.setOrg(request.getParameter("Organization"));
        r.setOrgType(request.getParameter("Type"));
        r.setOrgTypeOther(request.getParameter("TypeOther"));
        r.setThirdParty(request.getParameter("ThirdParty"));
        r.setDiscount(request.getParameter("Discount"));
        r.setCharityNumber(request.getParameter("CharityNumber"));
        r.setAddress1(request.getParameter("Address1"));
        r.setAddress2(request.getParameter("Address2"));
        r.setCity(request.getParameter("City"));
        r.setProvince(request.getParameter("Province"));
        r.setPostalCode(request.getParameter("PostalCode"));
        r.setCountry(request.getParameter("Country"));
        r.setFirstName(request.getParameter("FirstName"));
        r.setLastName(request.getParameter("LastName"));
        r.setTitle(request.getParameter("Title"));
        r.setTel(request.getParameter("Tel"));
        r.setExt(request.getParameter("Ext"));
        r.setFax(request.getParameter("Fax"));
        r.setEmail(request.getParameter("Email"));
        r.setWebsite(request.getParameter("Website"));
        r.setContactNotes(request.getParameter("ContactNotes"));
        r.setBooths(Integer.parseInt(request.getParameter("Booths")));
        r.setRepresentatives(
                Integer.parseInt(request.getParameter("Representatives")));
        r.setGuidebook(request.getParameter("Guidebook"));
        r.setSubsidiary(request.getParameter("Subsidiary"));
        r.setRequests(request.getParameter("Requests"));
        r.setHow(request.getParameter("How"));
        r.setHowOther(request.getParameter("HowOther"));
        r.setParticipated(request.getParameter("New"));
        r.setAdInGuidebook(request.getParameter("adInGuidebook"));
        r.setLogoForWebsite(request.getParameter("logoForWebsite"));

        bindGuidebook(r, request);
    }

    public static void bindGuidebook(EFRRegistration r, HttpServletRequest request)
    {
        r.setEmpLocation(
                StringUtils.join(request.getParameterValues("empLocation"), "|"));
        r.setEmpType(StringUtils.join(request.getParameterValues("empType"), "|"));
        r.setGuidebookSub(request.getParameter("guidebookSub"));
        r.setPositionsAvailable(request.getParameter("positionsAvailable"));
        r.setLinkedIn(request.getParameter("linkedIn"));
        r.setFacebook(request.getParameter("facebook"));
        r.setTwitter(request.getParameter("twitter"));
        r.setContactInfo(request.getParameter("contactInfo"));
    }

    public static double getTotalCost(EFRRegistration r)
    {
        EFRModule module = r.getModule();

        double totalCost = module.getStandardPrice();
        if (r.getDiscount().equals("cacee"))
        {
            totalCost = module.getCaceePrice();
        }
        else if (r.getDiscount().equals("charity"))
        {
            totalCost = module.getCharityPrice();
        }

        totalCost = totalCost * r.getBooths();

        if (r.getBooth1() != null
                && r.getBooth1().getType() == EFRBooth.TYPE_PREMIUM)
        {
            totalCost += module.getPremiumPrice() * r.getBooths();
        }
        else if (r.getBooth2() != null
                && r.getBooth2().getType() == EFRBooth.TYPE_PREMIUM)
        {
            totalCost += module.getPremiumPrice() * r.getBooths();
        }

        if (r.getBooths() == 1 && r.getRepresentatives() > 4)
        {
            int d = r.getRepresentatives() - 4;
            totalCost += d * module.getRepPrice();
        }

        if (r.getBooths() == 2 && r.getRepresentatives() > 8)
        {
            int d = r.getRepresentatives() - 8;
            totalCost += d * module.getRepPrice();
        }

        if (r.getLogoForWebsite().equals("yes"))
        {
            totalCost += module.getLogoForWebsite();
        }

        if (r.getAdInGuidebook().equals("half"))
        {
            totalCost += module.getGuidebookAdHalf();
        }
        else if (r.getAdInGuidebook().equals("full"))
        {
            totalCost += module.getGuidebookAdFull();
        }

        return totalCost;
    }

    public static List<EFRRegistration> getUsersOtherRegistrations(
            EFRRegistration notThis)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        List<EFRRegistration> userRegistrations = ht.find(
                "from EFRRegistration r where r.user=? and r.module=? and r!=? order by r.dateCreated desc",
                new Object[] { notThis, notThis.getModule(), notThis });
        return userRegistrations;
    }

    public static List<Integer> getCompletedRegistrationIds(EFRModule module)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        return ht.find(
                "select r.id from EFRRegistration r where r.completed=1 and r.canceled = 0 and r.module=?",
                module);
    }

    public static List<Integer> getIncompleteRegistrationIds(EFRModule module)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        return ht.find(
                "select r.id from EFRRegistration r where r.completed=0 and r.module=?",
                module);
    }

    public static List<Integer> getCanceledRegistrationIds(EFRModule module)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        return ht.find(
                "select r.id from EFRRegistration r where r.canceled = 1 and r.module=?",
                module);
    }

    public static List<Integer> getPaidRegistrationIds(EFRModule module)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        return ht.find(
                "select r.id from EFRRegistration r where r.completed=1 and r.canceled = 0 and r.ecommerceOrder.status = 2 and r.module=?",
                module);
    }

    public static List<Integer> getUnpaidRegistrationIds(EFRModule module)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        return ht.find(
                "select r.id from EFRRegistration r where r.completed=1 and r.canceled = 0 and r.ecommerceOrder.status != 2 and r.module=?",
                module);
    }

    public static List<Integer> getRegistrationIdsWithIncompleteProfiles(
            EFRModule module)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        return ht.find(
                "select r.id from EFRRegistration r where r.completed=1 and r.canceled = 0 and r.guidebook='yes' and r.guidebookData is null and r.module=?",
                module);
    }

    public static List<Integer> getRegistrationIdsWithPendingProfiles(
            EFRModule module)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        return ht.find(
                "select r.id from EFRRegistration r where r.completed=1 and r.canceled = 0 and r.guidebook='yes' and r.guidebookData.status = 0 and r.module=?",
                module);
    }

    public static List<Integer> getRegistrationIdsWithApprovedProfiles(
            EFRModule module)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        return ht.find(
                "select r.id from EFRRegistration r where r.completed=1 and r.canceled = 0 and r.guidebook='yes' and r.guidebookData.status = 1 and r.module=?",
                module);
    }
}
