package com.orbis.web.content.interaction;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.orm.hibernate.HibernateTemplate;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.OrbisThread;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.acrm.AcrmProgram;
import com.orbis.web.content.acrm.AcrmRegionQuebec;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.np.NHelper;

public class InteractionRegionalServiceTeamImportThread extends OrbisThread
{
    private InteractionModule module;

    private XSSFWorkbook workbook;

    private HibernateTemplate ht = PortalUtils.getHt();

    private final int PROGRAM_COLUMNS_START = 8;

    private final int PROGRAM_COLUMNS_END = 61;

    private final int REGION_CODE_CELL_INDEX = 0;

    private final int REGION_NAME_CELL_INDEX = 1;

    private final int RCM_CODE_CELL_INDEX = 2;

    private final int RCM_NAME_CELL_INDEX = 3;

    private final int MUNICIPAL_CODE_CELL_INDEX = 4;

    private final int MUNICIPAL_NAME_CELL_INDEX = 5;

    private final int POSTAL_CODE_CELL_INDEX = 6;

    Map<Integer, AcrmProgram> programCellMap = new HashMap();

    InteractionRole role = (InteractionRole) InteractionHelper
            .getRolesMapKeyedByLevel().get(InteractionRole.ROLE_ONE);

    public InteractionRegionalServiceTeamImportThread(InteractionModule module,
            XSSFWorkbook workbook, HttpServletRequest request)
    {
        super(request);
        this.workbook = workbook;
        this.module = module;
    }

    @Override
    public String getThreadName()
    {
        return "Interaction Regional Service Team Import";
    }

    @Override
    public void doRun() throws Exception
    {
        processPrograms();
        processServiceTeams();
    }

    private void processServiceTeams()
    {
        try
        {
            XSSFSheet sheet = workbook.getSheetAt(0);
            setTotal(sheet.getLastRowNum());

            for (int i = 1; i <= sheet.getLastRowNum(); i++)
            {
                XSSFRow row = sheet.getRow(i);

                String regionCode = new Integer(new Double(
                        row.getCell(REGION_CODE_CELL_INDEX).getNumericCellValue())
                                .intValue()).toString();
                String regionName = NHelper.excelGetCellValueAsString(
                        row.getCell(REGION_NAME_CELL_INDEX));
                String rcmCode = new Integer(new Double(
                        row.getCell(RCM_CODE_CELL_INDEX).getNumericCellValue())
                                .intValue()).toString();
                String rcmName = NHelper.excelGetCellValueAsString(
                        row.getCell(RCM_NAME_CELL_INDEX));
                String municipalCode = new Integer(
                        new Double(row.getCell(MUNICIPAL_CODE_CELL_INDEX)
                                .getNumericCellValue()).intValue()).toString();
                String municipalName = NHelper.excelGetCellValueAsString(
                        row.getCell(MUNICIPAL_NAME_CELL_INDEX));
                String postalCode = NHelper.excelGetCellValueAsString(
                        row.getCell(POSTAL_CODE_CELL_INDEX));

                AcrmRegionQuebec region = null;
                List<AcrmRegionQuebec> regionMatch = ht.find(
                        "from AcrmRegionQuebec r where r.postalCode=? and municipalityCode=? and r.municipalityName=? "
                                + " and r.rcmCode=? and r.rcmName=? and r.regionCode=? and r.regionName=?",
                        new Object[] { postalCode, municipalCode, municipalName,
                                rcmCode, rcmName, regionCode, regionName });
                if (regionMatch.isEmpty())
                {
                    region = new AcrmRegionQuebec();
                    region.setPostalCode(postalCode);
                    region.setMunicipalityCode(municipalCode);
                    region.setMunicipalityName(municipalName);
                    region.setRcmCode(rcmCode);
                    region.setRcmName(rcmName);
                    region.setRegionCode(regionCode);
                    region.setRegionName(regionName);
                    region.setProvince("Québec");
                    region.setCountry("Canada");
                    ht.save(region);
                }
                else
                {
                    region = regionMatch.get(0);
                }

                for (int j = PROGRAM_COLUMNS_START; j <= PROGRAM_COLUMNS_END; j++)
                {
                    if (!this.isInterrupted())
                    {
                        try
                        {
                            String username = NHelper
                                    .excelGetCellValueAsString(row.getCell(j));
                            UserDetailsImpl user = UserDetailsHelper
                                    .getUserByUsername(username);

                            if (user != null)
                            {
                                AcrmProgram acrmProgram = programCellMap.get(j);
                                InteractionTeamMemberRole tmr = getTeamMemberRole(
                                        user, acrmProgram);

                                if ((Integer) ht.find(
                                        "select count(stm) from InteractionTeamMemberRoleRegion stm where stm.teamMemberRole=? "
                                                + " and stm.program=? and stm.region=?",
                                        new Object[] { tmr, acrmProgram, region })
                                        .get(0) == 0)
                                {
                                    InteractionTeamMemberRoleRegion rr = new InteractionTeamMemberRoleRegion();
                                    rr.setProgram(acrmProgram);
                                    rr.setRegion(region);
                                    rr.setTeamMemberRole(tmr);
                                    ht.save(rr);
                                }
                            }
                        }
                        catch (Exception e)
                        {
                            e.printStackTrace();
                        }
                    }
                }

                setProgress(getProgress() + 1);
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    private InteractionTeamMemberRole getTeamMemberRole(UserDetailsImpl user,
            AcrmProgram acrmProgram)
    {
        return InteractionHelper.getOrCreateTeamMemberRole(user, role, acrmProgram);
    }

    private void processPrograms()
    {
        XSSFSheet sheet = workbook.getSheetAt(0);
        XSSFRow row = sheet.getRow(0);

        if (!NHelper.excelEmptyRow(row))
        {
            for (int i = PROGRAM_COLUMNS_START; i <= PROGRAM_COLUMNS_END; i++)
            {
                String program = NHelper.excelGetCellValueAsString(row.getCell(i))
                        .replaceAll("\\\"", "");

                String code = StringUtils.extract(program, "^(\\d+)").trim();
                String name = program.replaceAll(code, "").trim();

                AcrmProgram acrmProgram = null;

                List<AcrmProgram> matches = ht
                        .find("from AcrmProgram p where p.code=?", code);
                if (matches.isEmpty())
                {
                    acrmProgram = new AcrmProgram();
                    acrmProgram.setCode(code);
                    acrmProgram.setTitle(name);
                    acrmProgram.setL2Title(name);
                    ht.save(acrmProgram);
                }
                else
                {
                    acrmProgram = matches.get(0);
                }

                programCellMap.put(i, acrmProgram);
            }
        }

        setProgress(getProgress() + 1);
    }

    public InteractionModule getModule()
    {
        return module;
    }

    public void setModule(InteractionModule module)
    {
        this.module = module;
    }

}
