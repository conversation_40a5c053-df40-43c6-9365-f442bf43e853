package com.orbis.web.content.np;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;

import javax.servlet.http.HttpServletRequest;

import com.orbis.df.DFQuestion;
import com.orbis.utils.I18nMessageList;
import com.orbis.utils.JSONExportable;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.RequestUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.ContentItem;
import com.orbis.web.content.np.function.NPostingQuestionTextSearchAllPredicate;
import com.orbis.web.content.np.function.NPostingQuestionTextSearchDataViewerContainsPredicate;
import com.orbis.web.content.np.function.NPostingQuestionTextSearchDataViewerDoesNotContainPredicate;
import com.orbis.web.content.np.function.NPostingQuestionTextSearchDataViewerEmptyPredicate;
import com.orbis.web.content.np.function.NPostingQuestionTextSearchDataViewerEndsWithPredicate;
import com.orbis.web.content.np.function.NPostingQuestionTextSearchDataViewerExactMatchPredicate;
import com.orbis.web.content.np.function.NPostingQuestionTextSearchDataViewerNotEmptyPredicate;
import com.orbis.web.content.np.function.NPostingQuestionTextSearchDataViewerNotExactMatchPredicate;
import com.orbis.web.content.np.function.NPostingQuestionTextSearchDataViewerStartsWithPredicate;
import com.orbis.web.content.np.function.NPostingQuestionTextSearchExactPredicate;
import com.orbis.web.content.np.function.NPostingQuestionTextSearchNonePredicate;
import com.orbis.web.content.np.function.NPostingQuestionTextSearchOnePredicate;
import com.orbis.web.content.np.function.NPostingQuestionTextSearchPredicate;

public class NPostingQuestion extends ContentItem
        implements NPostingSearchableQuestion, JSONExportable<NPostingQuestion>
{

    private static final long serialVersionUID = -4082469776071279755L;

    public static class QuestionType
    {
        public static final int TEXT = 1;

        public static final int DATE_DEPRECATED = 2;

        public static final int NUMBER = 3;

        public static final int CHOICE_SINGLE = 4;

        public static final int CHOICE_MULTI = 5;

        public static final int MATRIX_SINGLE = 6;

        public static final int MATRIX_MULTI = 7;

        public static final int FILE_UPLOAD = 8;

        public static final int DESCRIPTIVE = 9;

        public static final int DATE = 10;

        public static final int THIRD_PARTY_RECRUITER = 11;

        public static final int SALARY = 12;
    }

    public static class DisplayFormat
    {
        public static final int GENERAL = 0;

        public static final int VERTICAL = 1;

        public static final int HORIZONTAL = 2;

        public static final int COMBO = 3;

        public static final int DATE = 4;

        public static final int TIME = 5;

        public static final int DATETIME = 6;

        public static final int MULTILINE = 7;
    }

    public static class DisplayFormatMultiLine
    {
        public static final int TEXTAREA = 0;

        public static final int FCK_SIMPLE = 1;

        public static final int FCK_RICH = 2;
    }

    public static enum TextSearchOption
    {
        all,
        one,
        exact,
        none,
        dataViewer_0,
        dataViewer_1,
        dataViewer_2,
        dataViewer_3,
        dataViewer_4,
        dataViewer_5,
        dataViewer_6,
        dataViewer_7;

        private final String WHITESPACE = "\\s+";

        public NPostingQuestionTextSearchPredicate getPredicate(
                final String searchText, final NPostingQuestion question,
                final String loggedInUsername)
        {
            final NPostingQuestionTextSearchPredicate ret = getPredicate(searchText,
                    loggedInUsername);
            ret.setPostingQuestion(question);
            return ret;
        }

        public NPostingQuestionTextSearchPredicate getPredicate(
                final String searchText, final DFQuestion question,
                final String loggedInUsername)
        {
            final NPostingQuestionTextSearchPredicate ret = getPredicate(searchText,
                    loggedInUsername);
            ret.setDFQuestion(question);
            return ret;
        }

        public NPostingQuestionTextSearchPredicate getPredicate(
                final String searchText, final WidgetQuery widgetQuery,
                final String loggedInUsername)
        {
            final NPostingQuestionTextSearchPredicate ret = getPredicate(searchText,
                    loggedInUsername);
            ret.setWidgetQuery(widgetQuery);
            return ret;
        }

        private NPostingQuestionTextSearchPredicate getPredicate(
                final String searchText, final String loggedInUsername)
        {
            final NPostingQuestionTextSearchPredicate ret;
            final String searchTextLower = searchText.toLowerCase(Locale.ROOT);
            final String[] searchTextLowerTokens = searchTextLower
                    .split(WHITESPACE);
            switch (this)
            {
                case all:
                    ret = new NPostingQuestionTextSearchAllPredicate(
                            searchTextLower, searchTextLowerTokens,
                            loggedInUsername);
                    break;
                case one:
                    ret = new NPostingQuestionTextSearchOnePredicate(
                            searchTextLowerTokens, searchTextLower,
                            loggedInUsername);
                    break;
                case exact:
                    ret = new NPostingQuestionTextSearchExactPredicate(
                            searchTextLower, loggedInUsername);
                    break;
                case none:
                    ret = new NPostingQuestionTextSearchNonePredicate(
                            searchTextLowerTokens, searchTextLower,
                            loggedInUsername);
                    break;
                case dataViewer_0:
                    ret = new NPostingQuestionTextSearchDataViewerContainsPredicate(
                            searchTextLowerTokens, searchTextLower,
                            loggedInUsername);
                    break;

                case dataViewer_1:
                    ret = new NPostingQuestionTextSearchDataViewerDoesNotContainPredicate(
                            searchTextLowerTokens, searchTextLower,
                            loggedInUsername);
                    break;

                case dataViewer_2:
                    ret = new NPostingQuestionTextSearchDataViewerStartsWithPredicate(
                            searchTextLowerTokens, searchTextLower,
                            loggedInUsername);
                    break;

                case dataViewer_3:
                    ret = new NPostingQuestionTextSearchDataViewerEndsWithPredicate(
                            searchTextLowerTokens, searchTextLower,
                            loggedInUsername);
                    break;

                case dataViewer_4:
                    ret = new NPostingQuestionTextSearchDataViewerExactMatchPredicate(
                            searchTextLowerTokens, searchTextLower,
                            loggedInUsername);
                    break;

                case dataViewer_5:
                    ret = new NPostingQuestionTextSearchDataViewerNotExactMatchPredicate(
                            searchTextLowerTokens, searchTextLower,
                            loggedInUsername);
                    break;

                case dataViewer_6:
                    ret = new NPostingQuestionTextSearchDataViewerEmptyPredicate(
                            searchTextLowerTokens, searchTextLower,
                            loggedInUsername);
                    break;

                case dataViewer_7:
                    ret = new NPostingQuestionTextSearchDataViewerNotEmptyPredicate(
                            searchTextLowerTokens, searchTextLower,
                            loggedInUsername);
                    break;
                default:
                    ret = null;
                    break;
            }

            return ret;
        }
    }

    private static Map<String, NPostingQuestion> questionDefaults = new HashMap<String, NPostingQuestion>();

    private static Map<String, String> hardFieldMappings = new HashMap<String, String>();
    static
    {
        hardFieldMappings.put("postingId", "id");
        hardFieldMappings.put("Organization", "organization");
        hardFieldMappings.put("Division", "division");
        hardFieldMappings.put("Salutation", "salutation");
        hardFieldMappings.put("ContactTitle", "contactTitle");
        hardFieldMappings.put("FirstName", "firstName");
        hardFieldMappings.put("LastName", "lastName");
        hardFieldMappings.put("Address1", "address1");
        hardFieldMappings.put("Address2", "address2");
        hardFieldMappings.put("City", "city");
        hardFieldMappings.put("Province", "province");
        hardFieldMappings.put("PostalCode", "postalCode");
        hardFieldMappings.put("Region", "region");
        hardFieldMappings.put("Country", "country");
        hardFieldMappings.put("Phone", "phone");
        hardFieldMappings.put("AlternatePhone", "alternatePhone");
        hardFieldMappings.put("Fax", "fax");
        hardFieldMappings.put("Email", "email");
        hardFieldMappings.put("Website", "website");
        hardFieldMappings.put("JobType", "jobType");
        hardFieldMappings.put("Position", "position");
        hardFieldMappings.put("NumberOfPositions", "numberOfPositions");
        hardFieldMappings.put("Location", "location");
        hardFieldMappings.put("GeographicLocation", "geographicLocation");
        hardFieldMappings.put("JobDescription", "jobDescription");
        hardFieldMappings.put("Qualifications", "qualifications");
        hardFieldMappings.put("SkillsHighlights", "skillsHighlights");
        hardFieldMappings.put("NAICCode", "naic");
        hardFieldMappings.put("NOCCode", "noc");
        hardFieldMappings.put("EmploymentSector", "sector");
        hardFieldMappings.put("CompetitionNumber", "competitionNumber");
        hardFieldMappings.put("Salary", "salary");
        hardFieldMappings.put("Salary2", "salaryAmount");
        hardFieldMappings.put("ThirdPartyRecruiter", "thirdPartyRecruiter");
        hardFieldMappings.put("ApplicationInstructions", "applicationInstructions");
        hardFieldMappings.put("Level", "level");
        hardFieldMappings.put("cf1", "cf1");
        hardFieldMappings.put("cf2", "cf2");
        hardFieldMappings.put("cf3", "cf3");
        hardFieldMappings.put("cf4", "cf4");
        hardFieldMappings.put("cf5", "cf5");
        hardFieldMappings.put("cf6", "cf6");
        hardFieldMappings.put("cf7", "cf7");
        hardFieldMappings.put("cf8", "cf8");
        hardFieldMappings.put("cf9", "cf9");
        hardFieldMappings.put("cf10", "cf10");
        hardFieldMappings.put("cf11", "cf11");
        hardFieldMappings.put("cf12", "cf12");
        hardFieldMappings.put("cf13", "cf13");
        hardFieldMappings.put("cf14", "cf14");
        hardFieldMappings.put("GPA", "gpa");
        hardFieldMappings.put("UpperWage", "upperWage");
        hardFieldMappings.put("LowerWage", "lowerWage");

        hardFieldMappings.put("PreferredResponseMethod", "preferredResponse");
        hardFieldMappings.put("OpenToAlumni", "openToAlumni");
        hardFieldMappings.put("OpenToAllMajors", "openToAllMajors");
        hardFieldMappings.put("Duration", "duration");
        hardFieldMappings.put("Term", "term.title");
        hardFieldMappings.put("Degree", "degree");
        hardFieldMappings.put("StartDate", "startDate");
        hardFieldMappings.put("EndDate", "endDate");
        hardFieldMappings.put("HoursPerWeek", "hoursPerWeek");

        hardFieldMappings.put("IfByEmail", "ifByEmail");
        hardFieldMappings.put("IfByWebsite", "ifByWebsite");
        hardFieldMappings.put("IfByFax", "ifByFax");
        hardFieldMappings.put("IfByPhone", "ifByPhone");
        hardFieldMappings.put("ApplicationMaterialRequired",
                "applicationMaterialRequired");
        hardFieldMappings.put("ApplicationDocumentsRequired",
                "applicationDocumentsRequired");
    }

    private NPostingModule nPostingModule;

    private int questionOrder = 0;

    private int questionType = -1;

    private String questionText = null, questionText2;

    private String headerText = null, headerText2;

    private String footerText = null, footerText2;

    private String optionChoices = null;

    private String optionDefault = null, optionDefault2;

    private boolean optionSortFlag = false;

    private boolean optionIncludeOtherFlag = false;

    private String rowLabels = null, rowLabels2;

    private String columnLabels = null, columnLabels2;

    private boolean requiredFlag = false;

    private int characterMaximum = 0;

    private int displayFormatIndicator = 0;

    private int displayFormatMultiLine = 0;

    private long maxUploadSize = 0L;

    private String fileUploadTypes = null;

    private String validationRegEx = null;

    private String validationSample = null;

    private int displayedQuestionOrder = 0;

    private String internalCode;

    private boolean showInStudentsJobHeader, showInEmployerJobHeader,
            showInAdminJobHeader;

    private int jobHeaderOrder;

    private boolean active, visible, adminOnly, searchable, inResultFilter,
            searchableAdmin, inAdminSearchResults, questionOnPendingPage,
            visibleEmployer;

    private String tooltip;

    private boolean disabledSalaryNotAvailableOption;

    /**
     * @transient
     */
    private List<String> naics, nocs;

    public String getL2Value(String l1)
    {
        String ret = l1;
        if (this.optionChoices != null && !StringUtils.isEmpty(l1))
        {
            String[] manyAnswers = l1.split("\\^");
            String[] allOptions = this.optionChoices.split("\r\n");
            if (manyAnswers.length > 0 && l1.contains("^"))
            {
                String newAnswer = "";
                for (String answer : manyAnswers)
                {
                    boolean isFound = false;
                    for (String choice : allOptions)
                    {
                        String[] multiChoice = choice.split("\\|");
                        if (multiChoice.length == 2
                                && answer.equals(multiChoice[0]))
                        {
                            newAnswer += "^" + multiChoice[1];
                            isFound = true;
                            break;
                        }
                    }
                    if (!isFound && !answer.isEmpty())
                    {
                        newAnswer += "^" + answer;
                    }
                }
                if (newAnswer.length() > 0)
                {
                    ret = newAnswer + "^";
                }
            }
            else
            {
                for (String choice : allOptions)
                {
                    String[] multiChoice = choice.split("\\|");
                    if (multiChoice.length == 2 && l1.equals(multiChoice[0]))
                    {
                        ret = multiChoice[1];
                        break;
                    }
                }
            }
        }

        return ret;
    }

    public String getL1Value(String l2)
    {
        String ret = l2;
        if (this.optionChoices != null && !StringUtils.isEmpty(l2))
        {
            String[] manyAnswers = l2.split("\\^");
            String[] allOptions = this.optionChoices.split("\r\n");
            if (manyAnswers.length > 0 && l2.contains("^"))
            {
                String newAnswer = "";
                for (String answer : manyAnswers)
                {
                    boolean isFound = false;
                    for (String choice : allOptions)
                    {
                        String[] multiChoice = choice.split("\\|");
                        if (multiChoice.length == 2
                                && answer.equals(multiChoice[1]))
                        {
                            newAnswer += "^" + multiChoice[0];
                            isFound = true;
                            break;
                        }
                    }
                    if (!isFound && !answer.isEmpty())
                    {
                        newAnswer += "^" + answer;
                    }
                }
                if (newAnswer.length() > 0)
                {
                    ret = newAnswer + "^";
                }
            }
            else
            {
                for (String choice : allOptions)
                {
                    String[] multiChoice = choice.split("\\|");
                    if (multiChoice.length == 2 && l2.equals(multiChoice[1]))
                    {
                        ret = multiChoice[0];
                        break;
                    }
                }
            }
        }

        return ret;
    }

    public boolean isQuestionOnPendingPage()
    {
        return questionOnPendingPage;
    }

    public void setQuestionOnPendingPage(boolean questionOnPendingPage)
    {
        this.questionOnPendingPage = questionOnPendingPage;
    }

    public static String getHardFieldMapping(String softFieldName)
    {
        return hardFieldMappings.get(softFieldName);
    }

    public static Map<String, String> getHardFieldMapping()
    {
        return hardFieldMappings;
    }

    public boolean isSearchable()
    {
        return searchable;
    }

    public void setSearchable(boolean searchable)
    {
        this.searchable = searchable;
    }

    public int getDisplayFormatMultiLine()
    {
        return displayFormatMultiLine;
    }

    public void setDisplayFormatMultiLine(int displayFormatMultiLine)
    {
        this.displayFormatMultiLine = displayFormatMultiLine;
    }

    public String getTooltip()
    {
        return tooltip;
    }

    public void setTooltip(String tooltip)
    {
        this.tooltip = tooltip;
    }

    public boolean isAdminOnly()
    {
        return adminOnly;
    }

    public void setAdminOnly(boolean adminOnly)
    {
        this.adminOnly = adminOnly;
    }

    public boolean isActive()
    {
        return active;
    }

    public void setActive(boolean active)
    {
        this.active = active;
    }

    public boolean isVisible()
    {
        return visible;
    }

    public void setVisible(boolean visible)
    {
        this.visible = visible;
    }

    public String getInternalCode()
    {
        return internalCode;
    }

    public void setInternalCode(String internalCode)
    {
        this.internalCode = internalCode;
    }

    public NPostingModule getNPostingModule()
    {
        return nPostingModule;
    }

    public void setNPostingModule(NPostingModule postingModule)
    {
        nPostingModule = postingModule;
    }

    public int getDisplayedQuestionOrder()
    {
        return displayedQuestionOrder;
    }

    public void setDisplayedQuestionOrder(int displayedQuestionOrder)
    {
        this.displayedQuestionOrder = displayedQuestionOrder;
    }

    public String getValidationSample()
    {
        return validationSample;
    }

    public void setValidationSample(String validationSample)
    {
        this.validationSample = validationSample;
    }

    public int getQuestionOrder()
    {
        return questionOrder;
    }

    public void setQuestionOrder(int questionOrder)
    {
        this.questionOrder = questionOrder;
    }

    public int getQuestionType()
    {
        return questionType;
    }

    public void setQuestionType(int questionType)
    {
        this.questionType = questionType;
    }

    public String getQuestionText()
    {
        return questionText;
    }

    public void setQuestionText(String questionText)
    {
        this.questionText = questionText;
    }

    public String getQuestionText2()
    {
        return questionText2;
    }

    public void setQuestionText2(String questionText2)
    {
        this.questionText2 = questionText2;
    }

    public String getHeaderText()
    {
        return headerText;
    }

    public void setHeaderText(String headerText)
    {
        this.headerText = headerText;
    }

    public String getHeaderText2()
    {
        return headerText2;
    }

    public void setHeaderText2(String headerText2)
    {
        this.headerText2 = headerText2;
    }

    public String getFooterText()
    {
        return footerText;
    }

    public void setFooterText(String footerText)
    {
        this.footerText = footerText;
    }

    public String getFooterText2()
    {
        return footerText2;
    }

    public void setFooterText2(String footerText2)
    {
        this.footerText2 = footerText2;
    }

    public String getOptionChoices()
    {
        return optionChoices;
    }

    public void setOptionChoices(String optionChoices)
    {
        this.optionChoices = optionChoices;
    }

    public String getOptionDefault()
    {
        return optionDefault;
    }

    public void setOptionDefault(String optionDefault)
    {
        this.optionDefault = optionDefault;
    }

    public String getOptionDefault2()
    {
        return optionDefault2;
    }

    public void setOptionDefault2(String optionDefault2)
    {
        this.optionDefault2 = optionDefault2;
    }

    public boolean isOptionSortFlag()
    {
        return optionSortFlag;
    }

    public void setOptionSortFlag(boolean optionSortFlag)
    {
        this.optionSortFlag = optionSortFlag;
    }

    public boolean isOptionIncludeOtherFlag()
    {
        return optionIncludeOtherFlag;
    }

    public void setOptionIncludeOtherFlag(boolean optionIncludeOtherFlag)
    {
        this.optionIncludeOtherFlag = optionIncludeOtherFlag;
    }

    public String getRowLabels()
    {
        return rowLabels;
    }

    public void setRowLabels(String rowLabels)
    {
        this.rowLabels = rowLabels;
    }

    public String getRowLabels2()
    {
        return rowLabels2;
    }

    public void setRowLabels2(String rowLabels2)
    {
        this.rowLabels2 = rowLabels2;
    }

    public String getColumnLabels()
    {
        return columnLabels;
    }

    public void setColumnLabels(String columnLabels)
    {
        this.columnLabels = columnLabels;
    }

    public String getColumnLabels2()
    {
        return columnLabels2;
    }

    public void setColumnLabels2(String columnLabels2)
    {
        this.columnLabels2 = columnLabels2;
    }

    public boolean isRequiredFlag()
    {
        return requiredFlag;
    }

    public void setRequiredFlag(boolean requiredFlag)
    {
        this.requiredFlag = requiredFlag;
    }

    public int getCharacterMaximum()
    {
        return characterMaximum;
    }

    public void setCharacterMaximum(int characterMaximum)
    {
        this.characterMaximum = characterMaximum;
    }

    public int getDisplayFormatIndicator()
    {
        return displayFormatIndicator;
    }

    public void setDisplayFormatIndicator(int displayFormatIndicator)
    {
        this.displayFormatIndicator = displayFormatIndicator;
    }

    public long getMaxUploadSize()
    {
        return maxUploadSize;
    }

    public void setMaxUploadSize(long maxUploadSize)
    {
        this.maxUploadSize = maxUploadSize;
    }

    public String getFileUploadTypes()
    {
        return fileUploadTypes;
    }

    public void setFileUploadTypes(String fileUploadTypes)
    {
        this.fileUploadTypes = fileUploadTypes;
    }

    public String getValidationRegEx()
    {
        return validationRegEx;
    }

    public void setValidationRegEx(String validationRegEx)
    {
        this.validationRegEx = validationRegEx;
    }

    public SortedMap<Integer, String> getRowLabelMap()
    {
        return getRowLabelMapCommon(this.rowLabels);
    }

    public SortedMap<Integer, String> getRowLabelMap2()
    {
        return getRowLabelMapCommon(this.rowLabels2);
    }

    public SortedMap<Integer, String> getColumnLabelMap()
    {
        return getColumnLabelMapCommon(this.columnLabels);
    }

    public SortedMap<Integer, String> getColumnLabelMap2()
    {
        return getColumnLabelMapCommon(this.columnLabels2);
    }

    public SortedMap<Integer, String> getOptionChoiceMap()
    {
        return getOptionChoiceMapCommon(this.optionChoices);
    }

    public SortedMap<Integer, String> getOptionChoiceMap2()
    {
        return getOptionChoiceMapCommon(this.optionChoices);
    }

    public I18nMessageList validate()
    {
        I18nMessageList errors = new I18nMessageList();

        if (this.characterMaximum < 0)
            errors.add("i18n.NPostingQuestion.Themaximum8835094692692779");

        if (this.maxUploadSize <= 0)
        {
            this.maxUploadSize = 0;
        }

        if (this.questionType == QuestionType.CHOICE_MULTI
                || this.questionType == QuestionType.CHOICE_SINGLE)
        {
            validateMulti(errors, this.optionChoices, this.optionDefault);
        }

        if (this.questionType == QuestionType.MATRIX_MULTI
                || this.questionType == QuestionType.MATRIX_SINGLE)
        {
            validateMatrix(errors, this.rowLabels, this.columnLabels);
        }

        return errors;
    }

    public void cleanUpByType()
    {
        switch (this.getQuestionType())
        {
            case QuestionType.TEXT:
                this.setColumnLabels("");
                this.setColumnLabels2("");
                this.setMaxUploadSize(0);
                this.setFileUploadTypes("");
                this.setOptionChoices("");
                this.setOptionDefault("");
                this.setOptionDefault2("");
                this.setOptionIncludeOtherFlag(false);
                this.setOptionSortFlag(false);
                this.setRowLabels("");
                this.setRowLabels2("");
                if (this.getDisplayFormatIndicator() > 0)
                    this.setCharacterMaximum(0);
                break;
            case QuestionType.DATE_DEPRECATED:
                this.setCharacterMaximum(0);
                this.setColumnLabels("");
                this.setColumnLabels2("");
                this.setMaxUploadSize(0);
                this.setFileUploadTypes("");
                this.setOptionChoices("");
                this.setOptionDefault("");
                this.setOptionDefault2("");
                this.setOptionIncludeOtherFlag(false);
                this.setOptionSortFlag(false);
                this.setRowLabels("");
                this.setRowLabels2("");
                this.setValidationRegEx(
                        "^(([0]?[1-9]|1[0-2])(:|\\.)[0-5][0-9]((:|\\.)[0-5][0-9])?( )?(AM|am|aM|Am|PM|pm|pM|Pm))$");
                this.setValidationSample("4:30 pm");
                break;
            case QuestionType.DATE:
                this.setCharacterMaximum(0);
                this.setColumnLabels("");
                this.setColumnLabels2("");
                this.setMaxUploadSize(0);
                this.setFileUploadTypes("");
                this.setOptionChoices("");
                this.setOptionDefault("");
                this.setOptionDefault2("");
                this.setOptionIncludeOtherFlag(false);
                this.setOptionSortFlag(false);
                this.setRowLabels("");
                this.setRowLabels2("");
                this.setValidationRegEx("");
                this.setValidationSample("01/01/1999");
                break;
            case QuestionType.NUMBER:
                this.setCharacterMaximum(0);
                this.setColumnLabels("");
                this.setColumnLabels2("");
                this.setDisplayFormatIndicator(0);
                this.setMaxUploadSize(0);
                this.setFileUploadTypes("");
                this.setOptionChoices("");
                this.setOptionDefault("");
                this.setOptionDefault2("");
                this.setOptionIncludeOtherFlag(false);
                this.setOptionSortFlag(false);
                this.setRowLabels("");
                this.setRowLabels2("");
                break;
            case QuestionType.CHOICE_SINGLE:
                this.setCharacterMaximum(0);
                this.setColumnLabels("");
                this.setColumnLabels2("");
                this.setMaxUploadSize(0);
                this.setFileUploadTypes("");
                this.setRowLabels("");
                this.setRowLabels2("");
                break;
            case QuestionType.CHOICE_MULTI:
                this.setCharacterMaximum(0);
                this.setColumnLabels("");
                this.setColumnLabels2("");
                this.setMaxUploadSize(0);
                this.setFileUploadTypes("");
                this.setRowLabels("");
                this.setRowLabels2("");
                break;
            case QuestionType.MATRIX_SINGLE:
                this.setCharacterMaximum(0);
                this.setDisplayFormatIndicator(0);
                this.setMaxUploadSize(0);
                this.setFileUploadTypes("");
                this.setOptionChoices("");
                this.setOptionDefault("");
                this.setOptionDefault2("");
                this.setOptionIncludeOtherFlag(false);
                this.setOptionSortFlag(false);
                this.setValidationRegEx("");
                this.setValidationSample("");
                break;
            case QuestionType.MATRIX_MULTI:
                this.setCharacterMaximum(0);
                this.setDisplayFormatIndicator(0);
                this.setMaxUploadSize(0);
                this.setFileUploadTypes("");
                this.setOptionChoices("");
                this.setOptionDefault("");
                this.setOptionDefault2("");
                this.setOptionIncludeOtherFlag(false);
                this.setOptionSortFlag(false);
                this.setValidationRegEx("");
                this.setValidationSample("");
                break;
            case QuestionType.FILE_UPLOAD:
                this.setCharacterMaximum(0);
                this.setColumnLabels("");
                this.setColumnLabels2("");
                this.setDisplayFormatIndicator(0);
                this.setOptionChoices("");
                this.setOptionDefault("");
                this.setOptionDefault2("");
                this.setOptionIncludeOtherFlag(false);
                this.setOptionSortFlag(false);
                this.setRowLabels("");
                this.setRowLabels2("");
                this.setValidationRegEx("");
                this.setValidationSample("");
                break;
            case QuestionType.DESCRIPTIVE:
                this.setCharacterMaximum(0);
                this.setColumnLabels("");
                this.setColumnLabels2("");
                this.setDisplayFormatIndicator(0);
                this.setMaxUploadSize(0);
                this.setFileUploadTypes("");
                this.setFooterText("");
                this.setFooterText2("");
                this.setOptionChoices("");
                this.setOptionDefault("");
                this.setOptionDefault2("");
                this.setOptionIncludeOtherFlag(false);
                this.setOptionSortFlag(false);
                this.setQuestionText("");
                this.setQuestionText2("");
                this.setRequiredFlag(false);
                this.setRowLabels("");
                this.setRowLabels2("");
                this.setValidationRegEx("");
                this.setValidationSample("");
                break;
            default:
                break;
        }
    }

    public void setSurveyFormQuestion(HttpServletRequest request)
    {
        boolean optionOtherFlag = RequestUtils.getBooleanParameter(request,
                "optionIncludeOtherFlag", false);

        if (request.getParameter("mappingField") != null)
        {
            this.setInternalCode(request.getParameter("mappingField"));
        }

        if (request.getParameter("questionText") != null)
        {
            this.setQuestionText(trimText(request.getParameter("questionText")));
        }

        if (request.getParameter("questionText2") != null)
        {
            this.setQuestionText2(trimText(request.getParameter("questionText2")));
        }
        else if (StringUtils.isEmpty(this.getQuestionText2()))
        {
            this.setQuestionText2(this.getQuestionText());
        }

        if (request.getParameter("headerText") != null)
        {
            this.setHeaderText(request.getParameter("headerText"));
        }
        if (request.getParameter("headerText2") != null)
        {
            this.setHeaderText2(request.getParameter("headerText2"));
        }
        else if (StringUtils.isEmpty(this.getHeaderText2()))
        {
            this.setHeaderText2(this.getHeaderText());
        }

        if (request.getParameter("footerText") != null)
        {
            this.setFooterText(request.getParameter("footerText"));
        }
        if (request.getParameter("footerText2") != null)
        {
            this.setFooterText2(request.getParameter("footerText2"));
        }
        else if (StringUtils.isEmpty(this.getFooterText2()))
        {
            this.setFooterText2(this.getFooterText());
        }

        if (request.getParameter("optionChoices") != null)
        {
            ArrayList options = !StringUtils
                    .isEmpty(request.getParameter("optionChoices"))
                            ? new ArrayList(Arrays.asList(
                                    trimLines(request.getParameter("optionChoices"))
                                            .split("\r\n")))
                            : new ArrayList();

            optionOtherFlag |= options.removeAll(Collections.singleton("Other"));
            this.setOptionChoices(
                    !options.isEmpty() ? StringUtils.join(options, "\r\n") : "");
        }
        if (request.getParameter("optionDefault") != null)
        {
            this.setOptionDefault(trimText(request.getParameter("optionDefault")));
        }
        if (request.getParameter("optionDefault2") != null)
        {
            this.setOptionDefault2(
                    trimText(request.getParameter("optionDefault2")));
        }
        else if (StringUtils.isEmpty(this.getOptionDefault2()))
        {
            this.setOptionDefault2(this.getOptionDefault());
        }

        if (request.getParameter("rowLabels") != null)
        {
            this.setRowLabels(trimLines(request.getParameter("rowLabels")));
        }
        if (request.getParameter("rowLabels2") != null)
        {
            this.setRowLabels2(trimLines(request.getParameter("rowLabels2")));
        }
        else if (StringUtils.isEmpty(this.getRowLabels2()))
        {
            this.setRowLabels2(this.getRowLabels());
        }

        if (request.getParameter("columnLabels") != null)
        {
            this.setColumnLabels(trimLines(request.getParameter("columnLabels")));
        }
        if (request.getParameter("columnLabels2") != null)
        {
            this.setColumnLabels2(trimLines(request.getParameter("columnLabels2")));
        }
        else if (StringUtils.isEmpty(this.getColumnLabels2()))
        {
            this.setColumnLabels2(this.getColumnLabels());
        }

        this.setFileUploadTypes(request.getParameter("fileUploadTypes"));
        this.setOptionSortFlag(
                RequestUtils.getBooleanParameter(request, "optionSortFlag", false));
        this.setOptionIncludeOtherFlag(optionOtherFlag);
        this.setRequiredFlag(
                RequestUtils.getBooleanParameter(request, "requiredFlag", false));
        this.setQuestionType(
                RequestUtils.getIntParameter(request, "questionType", 0));
        this.setCharacterMaximum(
                RequestUtils.getIntParameter(request, "characterMaximum", 0));
        this.setDisplayFormatIndicator(
                RequestUtils.getIntParameter(request, "displayFormatIndicator", 0));
        this.setDisplayFormatMultiLine(
                RequestUtils.getIntParameter(request, "displayFormatMultiLine", 0));
        this.setMaxUploadSize(
                RequestUtils.getLongParameter(request, "maxUploadSize", 0));
        this.setValidationRegEx(request.getParameter("validationRegEx"));
        this.setValidationSample(
                trimText(request.getParameter("validationSample")));
        this.setActive(request.getParameter("active") != null
                && request.getParameter("active").equals("on"));
        this.setVisible(request.getParameter("visible") != null
                && request.getParameter("visible").equals("on"));
        this.setAdminOnly(request.getParameter("adminOnly") != null
                && request.getParameter("adminOnly").equals("on"));
        this.setSearchable(request.getParameter("searchable") != null
                && request.getParameter("searchable").equals("on"));
        this.setTooltip(request.getParameter("tooltip"));

        this.setVisibleEmployer(
                "on".equals(request.getParameter("visibleEmployer")));
    }

    public List<String> getAvailableChoices()
    {
        return getAvailableChoicesCommon(getOptionChoiceMap());
    }

    public List<String> getAvailableChoices2()
    {
        return getAvailableChoicesCommon(getOptionChoiceMap2());
    }

    public boolean isCustomQuestion()
    {
        return internalCode.startsWith("cf");
    }

    public static Map<String, NPostingQuestion> getQuestionDefaults()
    {
        initDefaultQuestions();
        return questionDefaults;
    }

    private boolean blankLinesExistIn(String list)
    {
        String[] temp = list.split("\r\n");

        for (int i = 0; i < temp.length; i++)
            if (temp[i].trim().length() == 0)
                return true;

        return false;
    }

    private String trimLines(String list)
    {
        if (list == null || list.trim().length() == 0)
            return null;

        String result = "";

        String[] temp = list.split("\r\n");

        for (int i = 0; i < temp.length; i++)
            if (temp[i].trim().length() != 0)
            {
                if (result.length() > 0)
                    result += "\r\n";
                result += temp[i].trim();
            }

        return result;
    }

    private String trimText(String inStr)
    {
        if (inStr == null || inStr.trim().length() == 0)
            return null;

        return inStr.trim();
    }

    private boolean duplicateLinesExistIn(String list)
    {
        String[] temp = list.split("\r\n");

        for (int i = 0; i < temp.length; i++)
            for (int j = 0; j < temp.length; j++)
                if (i != j && temp[i].trim().equals(temp[j].trim()))
                    return true;

        return false;
    }

    private static NPostingQuestion getCommonParameters(String questionText,
            String questionText2, String internalCode, Boolean visible,
            Boolean active, Integer questionOrder)
    {

        /* Swaps language if site is French First */
        if (LocaleUtils.getDefaultLocale() != Locale.ENGLISH)
        {
            String questionTextTemp = questionText2;
            questionText2 = questionText;
            questionText = questionTextTemp;

        }

        NPostingQuestion q = new NPostingQuestion();
        q.setQuestionOrder(questionOrder);
        q.setQuestionText(questionText);
        q.setQuestionText2(questionText2);
        q.setInternalCode(internalCode);
        q.setActive(active);
        q.setVisible(visible);
        /*
         * visibleEmployer is true by default if the question is active
         */
        q.setVisibleEmployer(active);
        q.setQuestionType(NPostingQuestion.QuestionType.TEXT);
        q.setOptionSortFlag(false);
        q.setOptionIncludeOtherFlag(false);
        q.setRequiredFlag(false);
        q.setCharacterMaximum(0);
        q.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.GENERAL);
        q.setDisplayFormatMultiLine(
                NPostingQuestion.DisplayFormatMultiLine.TEXTAREA);
        q.setMaxUploadSize(0);
        q.setAdminOnly(false);
        q.setSearchable(true);
        return q;
    }

    private static void initDefaultQuestions()
    {
        questionDefaults.clear();

        NPostingQuestion q1 = getCommonParameters("Organization", "Organisation",
                "Organization", Boolean.TRUE, Boolean.TRUE, 1);
        questionDefaults.put("Organization", q1);

        NPostingQuestion q2 = getCommonParameters("Division", "Division",
                "Division", Boolean.FALSE, Boolean.FALSE, 2);
        questionDefaults.put("Division", q2);

        NPostingQuestion q3 = getCommonParameters("Salutation", "Salutation",
                "Salutation", Boolean.TRUE, Boolean.TRUE, 3);
        q3.setQuestionType(NPostingQuestion.QuestionType.CHOICE_SINGLE);
        q3.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.COMBO);
        questionDefaults.put("Salutation", q3);

        NPostingQuestion q4 = getCommonParameters("Job Contact First Name",
                "Prénom du contact", "FirstName", Boolean.TRUE, Boolean.TRUE, 4);
        questionDefaults.put("FirstName", q4);

        NPostingQuestion q5 = getCommonParameters("Job Contact Last Name",
                "Nom de famille du contact", "LastName", Boolean.TRUE, Boolean.TRUE,
                5);
        questionDefaults.put("LastName", q5);

        NPostingQuestion q6 = getCommonParameters("Contact Title",
                "Titre du contact", "ContactTitle", Boolean.TRUE, Boolean.TRUE, 6);
        questionDefaults.put("ContactTitle", q6);

        NPostingQuestion q7 = getCommonParameters("Phone", "Télephone", "Phone",
                Boolean.TRUE, Boolean.TRUE, 7);
        questionDefaults.put("Phone", q7);

        NPostingQuestion q8 = getCommonParameters("Fax", "Fax", "Fax", Boolean.TRUE,
                Boolean.TRUE, 8);
        questionDefaults.put("Fax", q8);

        NPostingQuestion q9 = getCommonParameters("Website", "Site web", "Website",
                Boolean.TRUE, Boolean.TRUE, 9);
        questionDefaults.put("Website", q9);

        NPostingQuestion q10 = getCommonParameters("Email", "Courriel", "Email",
                Boolean.TRUE, Boolean.TRUE, 10);
        questionDefaults.put("Email", q10);

        NPostingQuestion q11 = getCommonParameters("Address Line One",
                "Adresse - Ligne 1", "Address1", Boolean.TRUE, Boolean.TRUE, 11);
        questionDefaults.put("Address1", q11);

        NPostingQuestion q12 = getCommonParameters("Address Line Two",
                "Adresse - Ligne 2", "Address2", Boolean.TRUE, Boolean.TRUE, 12);
        questionDefaults.put("Address2", q12);

        NPostingQuestion q13 = getCommonParameters("City", "Ville", "City",
                Boolean.TRUE, Boolean.TRUE, 13);
        questionDefaults.put("City", q13);

        NPostingQuestion q14 = getCommonParameters("Province / State",
                "Province ou état", "Province", Boolean.TRUE, Boolean.TRUE, 14);
        q14.setQuestionType(NPostingQuestion.QuestionType.CHOICE_SINGLE);
        q14.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.COMBO);
        questionDefaults.put("Province", q14);

        NPostingQuestion q15a = getCommonParameters("Postal Code / Zip Code",
                "Code postal ou Zip", "PostalCode", Boolean.TRUE, Boolean.TRUE, 15);
        questionDefaults.put("PostalCode", q15a);

        NPostingQuestion q15b = getCommonParameters("Region", "Région", "Region",
                Boolean.FALSE, Boolean.FALSE, 15);
        q15b.setQuestionType(NPostingQuestion.QuestionType.CHOICE_SINGLE);
        q15b.setSearchable(false);
        q15b.setOptionIncludeOtherFlag(true);
        q15b.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.COMBO);
        questionDefaults.put("Region", q15b);

        NPostingQuestion q16 = getCommonParameters("Country", "Pays", "Country",
                Boolean.TRUE, Boolean.TRUE, 16);
        q16.setQuestionType(NPostingQuestion.QuestionType.CHOICE_SINGLE);
        q16.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.COMBO);
        questionDefaults.put("Country", q16);

        NPostingQuestion q17 = getCommonParameters(
                "Are you a Third Party Recruiter?", "Recrutez-vous pour un tiers ?",
                "ThirdPartyRecruiter", Boolean.TRUE, Boolean.TRUE, 17);
        q17.setQuestionType(NPostingQuestion.QuestionType.THIRD_PARTY_RECRUITER);
        questionDefaults.put("ThirdPartyRecruiter", q17);

        NPostingQuestion q17a = getCommonParameters("Term Posted", "Trimestre",
                "Term", Boolean.FALSE, Boolean.FALSE, 100);
        questionDefaults.put("Term", q17a);

        NPostingQuestion q18 = getCommonParameters("Position Type", "Type de poste",
                "JobType", Boolean.TRUE, Boolean.TRUE, 101);
        q18.setQuestionType(NPostingQuestion.QuestionType.CHOICE_MULTI);
        q18.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.VERTICAL);
        questionDefaults.put("JobType", q18);

        NPostingQuestion q19 = getCommonParameters("Job Title", "Titre du poste",
                "Position", Boolean.TRUE, Boolean.TRUE, 102);
        questionDefaults.put("Position", q19);

        NPostingQuestion q20 = getCommonParameters("Job Location", "Lieu",
                "Location", Boolean.TRUE, Boolean.TRUE, 103);
        questionDefaults.put("Location", q20);

        NPostingQuestion q21 = getCommonParameters("Employment Category",
                "Catégorie d'emploi", "EmploymentSector", Boolean.FALSE,
                Boolean.FALSE, 104);
        q21.setQuestionType(NPostingQuestion.QuestionType.CHOICE_SINGLE);
        q21.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.COMBO);
        questionDefaults.put("EmploymentSector", q21);

        NPostingQuestion q22 = getCommonParameters("Job Description",
                "Description de l'offre", "JobDescription", Boolean.TRUE,
                Boolean.TRUE, 105);
        q22.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.MULTILINE);
        q22.setDisplayFormatMultiLine(
                NPostingQuestion.DisplayFormatMultiLine.FCK_SIMPLE);
        questionDefaults.put("JobDescription", q22);

        NPostingQuestion q23 = getCommonParameters("Job Requirements",
                "Qualifications", "Qualifications", Boolean.TRUE, Boolean.TRUE,
                106);
        q23.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.MULTILINE);
        q23.setDisplayFormatMultiLine(
                NPostingQuestion.DisplayFormatMultiLine.FCK_SIMPLE);
        questionDefaults.put("Qualifications", q23);

        NPostingQuestion q24 = getCommonParameters("Salary", "Salaire", "Salary",
                Boolean.TRUE, Boolean.TRUE, 107);
        questionDefaults.put("Salary", q24);

        NPostingQuestion q24a = getCommonParameters("Salary2", "Salaire 2",
                "Salary2", Boolean.TRUE, Boolean.TRUE, 110);
        q24a.setQuestionType(NPostingQuestion.QuestionType.SALARY);
        questionDefaults.put("Salary2", q24a);

        NPostingQuestion q25 = getCommonParameters("All Degrees and Disciplines",
                "Ouvert à tous les programmes", "OpenToAllMajors", Boolean.TRUE,
                Boolean.TRUE, 108);
        q25.setQuestionType(NPostingQuestion.QuestionType.CHOICE_SINGLE);
        q25.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.VERTICAL);
        questionDefaults.put("OpenToAllMajors", q25);

        NPostingQuestion q26a = getCommonParameters(
                "Targeted Degrees and Disciplines", "Programmes visés",
                "TargetedClusters", Boolean.TRUE, Boolean.TRUE, 109);
        questionDefaults.put("TargetedClusters", q26a);

        NPostingQuestion q26b = getCommonParameters("Start Date", "Date de début",
                "StartDate", Boolean.FALSE, Boolean.FALSE, 111);
        q26b.setQuestionType(NPostingQuestion.QuestionType.DATE);
        q26b.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.DATE);
        q26b.setSearchable(false);
        questionDefaults.put("StartDate", q26b);

        NPostingQuestion q26c = getCommonParameters("End Date", "Date de fin",
                "EndDate", Boolean.FALSE, Boolean.FALSE, 112);
        q26c.setQuestionType(NPostingQuestion.QuestionType.DATE);
        q26c.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.DATE);
        q26c.setSearchable(false);
        questionDefaults.put("EndDate", q26c);

        NPostingQuestion q26d = getCommonParameters("Hours Per Week",
                "Heures par semaine", "HoursPerWeek", Boolean.FALSE, Boolean.FALSE,
                113);
        q26d.setQuestionType(NPostingQuestion.QuestionType.NUMBER);
        q26d.setSearchable(false);
        // max 3 numbers infront of decimal, only two after the decimal (note:
        // 24x7=168.00)
        q26d.setValidationRegEx("^([0-9]{0,3}(.[0-9]{1,2})?)$");
        q26d.setValidationSample("37.50");
        questionDefaults.put("HoursPerWeek", q26d);

        NPostingQuestion idQuestion = getCommonParameters("Job Posting ID",
                "Numéro de l'offre", "postingId", false, false, 199);
        idQuestion.setSearchable(false);
        idQuestion.setQuestionType(QuestionType.NUMBER);
        questionDefaults.put(idQuestion.getInternalCode(), idQuestion);

        NPostingQuestion q27 = getCommonParameters("Application Procedure",
                "Procédure pour la candidature", "PreferredResponseMethod",
                Boolean.TRUE, Boolean.TRUE, 200);
        q27.setQuestionType(NPostingQuestion.QuestionType.CHOICE_SINGLE);
        q27.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.COMBO);
        questionDefaults.put("PreferredResponseMethod", q27);

        NPostingQuestion q28 = getCommonParameters("If by eMail, send to",
                "Si par courriel, envoyer à", "IfByEmail", Boolean.FALSE,
                Boolean.FALSE, 201);
        questionDefaults.put("IfByEmail", q28);

        NPostingQuestion q29 = getCommonParameters("If by Website, go to",
                "Si par site web, aller à", "IfByWebsite", Boolean.FALSE,
                Boolean.FALSE, 202);
        questionDefaults.put("IfByWebsite", q29);

        NPostingQuestion q30 = getCommonParameters("If by Fax, send to",
                "Si par fax, envoyer à", "IfByFax", Boolean.FALSE, Boolean.FALSE,
                203);
        questionDefaults.put("IfByFax", q30);

        NPostingQuestion q31 = getCommonParameters("If by Telephone, call",
                "Si par téléphone, appeler", "IfByPhone", Boolean.FALSE,
                Boolean.FALSE, 204);
        questionDefaults.put("IfByPhone", q31);

        NPostingQuestion q32 = getCommonParameters("Competition Number",
                "Numéro de compétition", "CompetitionNumber", Boolean.FALSE,
                Boolean.FALSE, 205);
        questionDefaults.put("CompetitionNumber", q32);

        NPostingQuestion q33 = getCommonParameters("Application Material Required",
                "Matériel requis", "ApplicationMaterialRequired", Boolean.TRUE,
                Boolean.TRUE, 206);
        q33.setQuestionType(NPostingQuestion.QuestionType.CHOICE_MULTI);
        q33.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.VERTICAL);
        questionDefaults.put("ApplicationMaterialRequired", q33);

        NPostingQuestion q34 = getCommonParameters(
                "Aditional Application Information",
                "Informations complémentaires de candidature",
                "ApplicationInstructions", Boolean.TRUE, Boolean.TRUE, 207);
        q34.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.MULTILINE);
        q34.setDisplayFormatMultiLine(
                NPostingQuestion.DisplayFormatMultiLine.FCK_SIMPLE);
        questionDefaults.put("ApplicationInstructions", q34);

        NPostingQuestion q34b = getCommonParameters(
                "Application Documents Required", "Documents requis",
                "ApplicationDocumentsRequired", Boolean.FALSE, Boolean.FALSE, 208);
        q34b.setQuestionType(NPostingQuestion.QuestionType.CHOICE_MULTI);
        q34b.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.VERTICAL);
        questionDefaults.put("ApplicationDocumentsRequired", q34b);

        NPostingQuestion q35 = getCommonParameters("NAIC Code", "Code CIAN",
                "NAICCode", Boolean.FALSE, Boolean.FALSE, 300);
        questionDefaults.put("NAICCode", q35);

        NPostingQuestion q36 = getCommonParameters("Open To Alumni",
                "Ouvert aux diplômés", "OpenToAlumni", Boolean.FALSE, Boolean.FALSE,
                301);
        q36.setQuestionType(NPostingQuestion.QuestionType.CHOICE_SINGLE);
        q36.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.HORIZONTAL);
        questionDefaults.put("OpenToAlumni", q36);

        NPostingQuestion q37 = getCommonParameters("Degree", "Diplôme", "Degree",
                Boolean.FALSE, Boolean.FALSE, 302);
        questionDefaults.put("Degree", q37);

        NPostingQuestion q38 = getCommonParameters("Number of Positions",
                "Nombre de postes", "NumberOfPositions", Boolean.FALSE,
                Boolean.FALSE, 303);
        q38.setQuestionType(NPostingQuestion.QuestionType.NUMBER);
        questionDefaults.put("NumberOfPositions", q38);

        NPostingQuestion q39 = getCommonParameters("Skills / Highlights",
                "Aptitudes", "SkillsHighlights", Boolean.FALSE, Boolean.FALSE, 304);
        questionDefaults.put("SkillsHighlights", q39);

        NPostingQuestion q41 = getCommonParameters("Duration", "Durée", "Duration",
                Boolean.FALSE, Boolean.FALSE, 306);
        q41.setQuestionType(NPostingQuestion.QuestionType.CHOICE_SINGLE);
        q41.setDisplayFormatIndicator(NPostingQuestion.DisplayFormat.COMBO);
        questionDefaults.put("Duration", q41);

        NPostingQuestion q42 = getCommonParameters("Custom Field 1",
                "Champ personnalisé 1", "cf1", Boolean.FALSE, Boolean.FALSE, 307);
        questionDefaults.put("cf1", q42);

        NPostingQuestion q43 = getCommonParameters("Custom Field 2",
                "Champ personnalisé 2", "cf2", Boolean.FALSE, Boolean.FALSE, 308);
        questionDefaults.put("cf2", q43);

        NPostingQuestion q44 = getCommonParameters("Custom Field 3",
                "Champ personnalisé 3", "cf3", Boolean.FALSE, Boolean.FALSE, 309);
        questionDefaults.put("cf3", q44);

        NPostingQuestion q45 = getCommonParameters("Custom Field 4",
                "Champ personnalisé 4", "cf4", Boolean.FALSE, Boolean.FALSE, 310);
        questionDefaults.put("cf4", q45);

        NPostingQuestion q46 = getCommonParameters("Custom Field 5",
                "Champ personnalisé 5", "cf5", Boolean.FALSE, Boolean.FALSE, 311);
        questionDefaults.put("cf5", q46);

        NPostingQuestion q47 = getCommonParameters("Custom Field 6",
                "Champ personnalisé 6", "cf6", Boolean.FALSE, Boolean.FALSE, 312);
        questionDefaults.put("cf6", q47);

        NPostingQuestion q48 = getCommonParameters("Custom Field 7",
                "Champ personnalisé 7", "cf7", Boolean.FALSE, Boolean.FALSE, 313);
        questionDefaults.put("cf7", q48);

        NPostingQuestion q49 = getCommonParameters("Custom Field 8",
                "Champ personnalisé 8", "cf8", Boolean.FALSE, Boolean.FALSE, 314);
        questionDefaults.put("cf8", q49);

        NPostingQuestion q50 = getCommonParameters("Custom Field 9",
                "Champ personnalisé 9", "cf9", Boolean.FALSE, Boolean.FALSE, 315);
        questionDefaults.put("cf9", q50);

        NPostingQuestion q51 = getCommonParameters("Custom Field 10",
                "Champ personnalisé 10", "cf10", Boolean.FALSE, Boolean.FALSE, 316);
        questionDefaults.put("cf10", q51);

        NPostingQuestion q52 = getCommonParameters("Custom Field 11",
                "Champ personnalisé 11", "cf11", Boolean.FALSE, Boolean.FALSE, 317);
        questionDefaults.put("cf11", q52);

        NPostingQuestion q53 = getCommonParameters("Custom Field 12",
                "Champ personnalisé 12", "cf12", Boolean.FALSE, Boolean.FALSE, 318);
        questionDefaults.put("cf12", q53);

        NPostingQuestion q54 = getCommonParameters("Custom Field 13",
                "Champ personnalisé 13", "cf13", Boolean.FALSE, Boolean.FALSE, 319);
        questionDefaults.put("cf13", q54);

        NPostingQuestion q55 = getCommonParameters("Custom Field 14",
                "Champ personnalisé 14", "cf14", Boolean.FALSE, Boolean.FALSE, 320);
        questionDefaults.put("cf14", q55);

    }

    private SortedMap<Integer, String> getRowLabelMapCommon(String rls)
    {
        SortedMap<Integer, String> map = new TreeMap<Integer, String>();

        String[] temp = rls.split("\r\n");

        for (int i = 1; i <= temp.length; i += 1)
        {
            if (temp[i - 1].trim().length() == 0)
                continue;
            map.put(new Integer(i), temp[i - 1]);
        }

        return map;
    }

    private SortedMap<Integer, String> getColumnLabelMapCommon(String rls)
    {
        SortedMap<Integer, String> map = new TreeMap<Integer, String>();

        String[] temp = rls.split("\r\n");

        for (int i = 1; i <= temp.length; i += 1)
        {
            if (temp[i - 1].trim().length() == 0)
                continue;
            map.put(new Integer(i), temp[i - 1]);
        }

        return map;
    }

    private SortedMap<Integer, String> getOptionChoiceMapCommon(String rls)
    {
        SortedMap<Integer, String> map = new TreeMap<Integer, String>();

        if (rls != null)
        {
            if (this.optionSortFlag)
            {
                String[] temp = rls.split("\r\n");

                SortedMap<String, String> map2 = new TreeMap<String, String>();

                for (int i = 1; i <= temp.length; i += 1)
                {
                    if (temp[i - 1].trim().length() == 0)
                        continue;
                    map2.put(temp[i - 1], temp[i - 1]);
                }

                java.util.Collection<String> sorted = map2.values();
                int i = 0;

                for (Iterator<String> iter = sorted.iterator(); iter.hasNext();)
                {
                    i += 1;
                    map.put(new Integer(i), iter.next());
                }
            }
            else
            {
                String[] temp = rls.split("\r\n");

                for (int i = 1; i <= temp.length; i += 1)
                {
                    if (temp[i - 1].trim().length() == 0)
                        continue;
                    map.put(new Integer(i), temp[i - 1]);
                }
            }
        }

        return map;
    }

    private List<String> getAvailableChoicesCommon(Map<Integer, String> map)
    {
        List<String> ret = new LinkedList<String>();
        for (String choice : map.values())
        {
            ret.add(choice);
        }

        return ret;
    }

    private void validateMulti(I18nMessageList errors, String ocs, String od)
    {
        if (ocs == null || ocs.length() == 0)
        {
            errors.add("i18n.ccrm_registrationQuestionEdit.error3");
        }
        else
        {
            if (blankLinesExistIn(ocs))
                errors.add("i18n.ccrm_registrationQuestionEdit.error4");
            if (duplicateLinesExistIn(ocs))
                errors.add("i18n.ccrm_registrationQuestionEdit.error5");
        }

        if (od != null && od.length() > 0)
        {
            if (optionListDoesNotContain(ocs, od))
            {
                errors.add("i18n.ccrm_registrationQuestionEdit.error6");
            }
        }
    }

    private boolean optionListDoesNotContain(String ocs, String option)
    {
        if (ocs == null)
            return true;

        String[] temp = ocs.split("\r\n");

        for (int i = 0; i < temp.length; i++)
            if (option.equals(temp[i].trim()))
                return false;

        return true;
    }

    private void validateMatrix(I18nMessageList errors, String rls, String cls)
    {
        if (rls == null || rls.length() == 0)
        {
            errors.add("i18n.ccrm_registrationQuestionEdit.error7");
        }
        else
        {
            if (blankLinesExistIn(rls))
            {
                errors.add("i18n.ccrm_registrationQuestionEdit.error8");
            }
            if (duplicateLinesExistIn(rls))
            {
                errors.add("i18n.ccrm_registrationQuestionEdit.error9");
            }
        }

        if (cls == null || cls.length() == 0)
        {
            errors.add("i18n.ccrm_registrationQuestionEdit.error10");
        }
        else
        {
            if (blankLinesExistIn(cls))
            {
                errors.add("i18n.ccrm_registrationQuestionEdit.error11");
            }
            if (duplicateLinesExistIn(cls))
            {
                errors.add("i18n.ccrm_registrationQuestionEdit.error12");
            }
        }
    }

    public Map<String, List<String>> getChoices()
    {
        Map<String, List<String>> choices = new HashMap<String, List<String>>();

        return choices;
    }

    public boolean isBiLingualField()
    {
        return NPostingQuestionHelper.isBiLingualField(this.internalCode);
    }

    public boolean isFieldTextType()
    {
        return NPostingQuestionHelper.isFieldTextType(this.internalCode);
    }

    @Override
    public String toString()
    {
        // TODO Auto-generated method stub
        return questionText;
    }

    public void setInResultFilter(boolean inResultFilter)
    {
        this.inResultFilter = inResultFilter;
    }

    public boolean isInResultFilter()
    {
        return inResultFilter;
    }

    public void setSearchableAdmin(boolean searchableAdmin)
    {
        this.searchableAdmin = searchableAdmin;
    }

    public boolean isSearchableAdmin()
    {
        return searchableAdmin;
    }

    public void setInAdminSearchResults(boolean inAdminSearchResults)
    {
        this.inAdminSearchResults = inAdminSearchResults;
    }

    public boolean isInAdminSearchResults()
    {
        return inAdminSearchResults;
    }

    public boolean isShowInStudentsJobHeader()
    {
        return showInStudentsJobHeader;
    }

    public void setShowInStudentsJobHeader(boolean showInStudentsJobHeader)
    {
        this.showInStudentsJobHeader = showInStudentsJobHeader;
    }

    public boolean isShowInEmployerJobHeader()
    {
        return showInEmployerJobHeader;
    }

    public void setShowInEmployerJobHeader(boolean showInEmployerJobHeader)
    {
        this.showInEmployerJobHeader = showInEmployerJobHeader;
    }

    public boolean isShowInAdminJobHeader()
    {
        return showInAdminJobHeader;
    }

    public void setShowInAdminJobHeader(boolean showInAdminJobHeader)
    {
        this.showInAdminJobHeader = showInAdminJobHeader;
    }

    public int getJobHeaderOrder()
    {
        return jobHeaderOrder;
    }

    public void setJobHeaderOrder(int jobHeaderOrder)
    {
        this.jobHeaderOrder = jobHeaderOrder;
    }

    @Override
    public Class getExportDependencyClass()
    {
        return NPostingModule.class;
    }

    @Override
    public String getExportDependencyFieldName()
    {
        return "nPostingModule";
    }

    @Override
    public boolean isExportAsArray()
    {
        return true;
    }

    public List<String> getNaics()
    {
        return naics;
    }

    public void setNaics(List<String> naics)
    {
        this.naics = naics;
    }

    public List<String> getNocs()
    {
        return nocs;
    }

    public void setNocs(List<String> nocs)
    {
        this.nocs = nocs;
    }

    public boolean isVisibleEmployer()
    {
        return visibleEmployer;
    }

    public void setVisibleEmployer(boolean visibleEmployer)
    {
        this.visibleEmployer = visibleEmployer;
    }

    public boolean isDisabledSalaryNotAvailableOption()
    {
        return disabledSalaryNotAvailableOption;
    }

    public void setDisabledSalaryNotAvailableOption(
            boolean disabledSalaryNotAvailableOption)
    {
        this.disabledSalaryNotAvailableOption = disabledSalaryNotAvailableOption;
    }
}
