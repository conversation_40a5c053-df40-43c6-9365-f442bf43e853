<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>

<c:if test="${empty staffOwnerForEvent}">
	<c:set var="staffOwnerForEvent" value="${not empty currentUser.assignedTypes['Manage Individual Events']}"/>
</c:if>

<orbis:addComponent component="momentjs" version="bootstrap" />
<orbis:addComponent component="jquery_timePickerAddon_${orbisLocale}" />
<orbis:addComponent component="jqueryValidate" version="1.11.1" />
<script type="text/javascript">
	var controllerPath = "${siteElement.fullPath}.htm";
	var eventSubCategory = "${event.subCategory.id}";
	
	$(document).ready(function() 
	{
		
		maxReg = ($('#maxRegistrations').val());
		
		$("#category").change(function(){
			displaySubCategories($("#category").val());
		});
		displaySubCategories('${event.category.id}');
		
		$("#swipeValidate").change(function(){
			if (this.checked)
			{
				$(".registrantsOnlyConfig").removeClass("display--none");	
			}
			else
			{
				$(".registrantsOnlyConfig").addClass("display--none");	
				$("#swipeRegistrantsOnly").prop("checked", false);
			}
		});
		
		$("#enableRegistration").change(function(){
			if (this.checked)
			{
				$(".regFields").removeClass("display--none");	
			}
			else
			{
				$(".regFields").addClass("display--none");	
			}
		});
		
		if ($("#enableRegistration").is(":checked"))
		{
			$(".regFields").removeClass("display--none");	
		}
		else
		{
			$(".regFields").addClass("display--none");	
		}
		
		$("#allDay").change(function() {
			var checked = $(this).prop("checked");
			if(checked) {
				$('.evtTimeDiv').css('display', 'none');
			} else {
				$('.evtTimeDiv').css('display', 'inline');
			}
			
			$("#startTime, #endTime").parent().toggleClass("hidden", checked).toggleClass("required", !checked);
			$("#startTimeLabel, #endTimeLabel").toggleClass("hidden", checked);
		});
	});
	
	function displaySubCategories(id)
	{
		var hasSub = false;
		if(id != null && id != '')
		{
			$("select[name=subCategory]").each(function()
		{
			if($(this).attr("parent")==id)
			{
				hasSub = true;
				$(this).removeClass("display--none");
				$(this).removeAttr("hidden");
				//$(this).addClass("required");
				
				$("#subCatLabel").attr("for",$(this).attr("id"));
				
				$(this).find("option").each(function()
				{
					if(eventSubCategory != '' && $(this).val()==eventSubCategory)
					{
						$(this).prop("selected","selected");
					}
					else
					{
						$(this).removeAttr("selected");
					}
				});
			}
			else
			{
				$(this).find("option").each(function()
				{
					$(this).removeAttr("selected");
				});
				$(this).removeClass("required");
				$(this).addClass("display--none");	
			}
		});
		}
		if(!hasSub)
		{
			$("#subCatRow").addClass("display--none");
			
		}
		else
		{
			$("#subCatRow").removeClass("display--none");
		}
		
	}
	
	function autoFillEndTime(){
		autoFillEndTime = function() { };
		<c:if test="${empty event.id}">
			$("#endDate").val($("#startDate").val());
			$("#endTime").val($("#startTime").val());
		</c:if>
		var $expireDate = $("#expireDate");
		if (!$expireDate.val()) {
			$expireDate.val($("#startDate").val());
			$("#expireTime").val($("#startTime").val());
		}
		var $registrationDeadline = $("#registrationDeadline");
		if (!$registrationDeadline.val()) {
			$registrationDeadline.val($("#startDate").val());
			$("#registrationDeadlineTime").val($("#startTime").val());
		}
	}
	
	function enableWaitingListClick(){
		$("#waitingListCutOff").toggle();
	}
	
	function waitingListDateSelected(){
		if($("#waitingListCutOffDate").val() == "" ){
			$("#waitingListCutOffTime").removeClass("required");
		}
		else if(!$("#waitingListCutOffDate").hasClass("required")){
			$("#waitingListCutOffTime").addClass("required");
		}
	}
	
	function generateCode() {
		var code = Math.floor(Math.random() * (99999 - 10000 + 1)) + 10000;
		$("#swipeOverrideCode").val(code);
	}
	
	function saveEvent()
	{
		for(var i in CKEDITOR.instances) CKEDITOR.instances[i].updateElement();
		if (parseInt($('#maxRegistrations').val()) < maxReg)
		{
			orbisAppSr.showConfirmModal('<orbis:message code="i18n.global_eventEdit.Youarecurr9621536611907663" javaScriptEscape="true"/>',
					function(){
				$("#saveEventForm").submit(); 
			});
		}
		else 
		{
			$("#saveEventForm").submit();
		}
	}   

    
</script>

<ui:form id="deleteForm" action="deleteEvent" eventId="${event.id}">
</ui:form>
<ui:form id="approveForm" action="approveGlobalEvent" eventId="${event.id}">
</ui:form>
<ui:form id="deleteImage" action="deleteImage" eventId="${event.id}">
</ui:form>

<%@ include file="/WEB-INF/spiralRobot/jsp/event/global/global_eventDetailsTitle.jsp"%>
<c:if test="${not empty event.id && (not empty currentUser.assignedTypes['Manage Events'] || not empty currentUser.assignedTypes['Event Calendar - Registration'] || staffOwnerForEvent)}">
<%@ include file="/WEB-INF/spiralRobot/jsp/event/global/global_eventDetailsBar.jsp"%>
</c:if>
<c:if test="${not empty event.id}">
<%@ include file="/WEB-INF/spiralRobot/jsp/event/global/global_eventDetailsNav.jsp"%>
</c:if>
<script type="text/javascript">
	$.validator.addMethod("nonZeroReg", function(value) {
		return !$("#chkEnableRegistration").is(":checked") || value > 0;
	}, "<orbis:message code='i18n.global_eventEdit.Registrati9634089612583259' />");

	$.validator.addMethod("vcMaxReg", function(value) {
	    if ($("#enableRegistration").is(":checked"))
	    {
			if((parseInt($('#chooseLocation').val())) == 2 && (parseInt($('#meetingMethod').val())) == 4 
				&& parseInt($('#maxRegistrations').val()) > 100)
	    	{
	    		return false;
	    	}
	    	else
	    	{
	    		return true;
	    	}
	    }
	    else
	    {
	    	return true;
	    }
	}, "<orbis:message code='i18n.global_eventEdit.BuildinVid5526149938958588' />");

	$.validator.addMethod("regRequired", function(value, element) {
		return !$("#chkEnableRegistration").is(":checked") || $.validator.methods.required.call(this, value, element);
	}, $.validator.messages.required);
	
	$.validator.addMethod("endTimeRequired", function(value) {
		return validateDateTime($("#endDate").val(), $("#endTime").val());
	}, "<orbis:message code='i18n.global_eventEdit.EndTimemus1354068280534573' />");
  		
	$.validator.addMethod("liveEndTimeRequired", function(value) {
		return validateDateTime($("#liveDate").val(), $("#liveTime").val());
	}, "<orbis:message code='i18n.global_eventEdit.LiveTimemu9406350357231056' />");
	
	$.validator.addMethod("expireEndTimeRequired", function(value) {
		return validateDateTime($("#expireDate").val(), $("#expireTime").val());
	}, "<orbis:message code='i18n.global_eventEdit.ExpireOnTi2380348610894287' />");
	
	$.validator.addMethod("waitingEndTimeRequired", function(value) {
		return !$("#enableWaitingList").prop("checked") || validateDateTime($("#waitingListCutOffDate").val(), $("#waitingListCutOffTime").val());
	}, "<orbis:message code='i18n.global_eventEdit.ThisTimemu8424751882693264' />");
	
	$.validator.addMethod("regOpenEndTimeRequired", function(value) {
		return !$("#chkEnableRegistration").is(":checked") || validateDateTime($("#openDate").val(), $("#openTime").val());
	}, "<orbis:message code='i18n.global_eventEdit.ThisTimemu8424751882693264' />");
	
	$.validator.addMethod("orderPrefix", function(value) {
		var patt = new RegExp("^[a-zA-Z0-9]*$");
		return $("#orderNumberPrefix").val().length >= 4 && $("#orderNumberPrefix").val().length <= 12 && patt.test($("#orderNumberPrefix").val());
	}, "<orbis:message code='i18n.global_eventEdit.Theordernu4269030207402817' />");
	
	$.validator.addMethod("evtTime", function(value) {
		return $('#allDay').is(':checked') || value !== '';
	}, $.validator.messages.required);
	
	$.validator.addMethod("endDateTimeValid", function(value) {
		if ($("#endDate").val() != '' && !document.getElementById("allDay").checked)
	    {
	    	if($('#endTime').val() != '')
	    	{
	    		var dateFormat = "";
	    		if(${orbisLocale=='en'}){
	    			dateFormat="MM/DD/YYYY hh:mm a"
	    		}
	    		else{
	    			dateFormat="DD-MM-YYYY hh:mm";
	    		}
	    		
	    		var start = moment($('#startDate').val() + " " + $('#startTime').val(),dateFormat).toDate();
	    		var end = moment($('#endDate').val() + " " + $('#endTime').val(),dateFormat).toDate();
	    		return end > start;
	    	}
	    	else
	    	{
	    		return true;
	    	}
	    }
	    else
	    {
	    	return true;
	    }
	}, "<orbis:message code='i18n.global_eventEdit.EndDateand4049216794910235' />");
	
	function validateDateTime(date,time){
		if (date)
	    {
	    	if(time)
	    	{
	    		return true;
	    	}
	    	else
	    	{
	    		return false;
	    	}
	    }
	    else
	    {
	    	return true;
	    }	
	}
</script>
<ui:formPage formId="saveEventForm" action="saveEvent" eventId="${event.id}" cCPositionId="">
	<c:if test="${advancedEvent || event.advancedEvent}">
		<input type="hidden" name="advancedEvent" value="${advancedEvent || event.advancedEvent}">
	</c:if>
	<ui:section key="formButtons">
		<ui:button type="success" size="large" onclick="saveEvent()"><orbis:message code="i18n.global_eventEdit.saveEvent" /></ui:button>
		<c:if test="${not empty event.id}">
			<ui:button type="info" size="large" action="displayGlobalEventDetails" eventId="${event.id}"><orbis:message code="i18n.global_eventEdit.Cancel" /></ui:button>
		</c:if>
		<c:if test="${empty event.id}">
			<ui:button type="info" size="large" action="displayHome"><orbis:message code="i18n.global_eventEdit.Cancel" /></ui:button>
		</c:if>
		<c:if test="${not empty event.id && (not empty currentUser.assignedTypes['Manage Events'] || staffOwnerForEvent)}">
			<c:set var="confirmModalTitle"><orbis:message code="i18n.common.confirmDeleteDependencyItem" javaScriptEscape="true"/></c:set>
			<ui:button size="large" type="error" onclick="$('#deleteForm').submit();" i18n_confirmOnclick="${confirmModalTitle}"><orbis:message code="i18n.common.delete" /></ui:button>
		</c:if>
	</ui:section>
	<%@ include file="/WEB-INF/spiralRobot/jsp/event/global/global_eventEdit_details.jsp"%>
</ui:formPage>
