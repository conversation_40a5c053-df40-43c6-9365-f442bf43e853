<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%@ taglib prefix="w" uri="/WEB-INF/tlds/wizard.tld"%>

<ui:navBack>
	<ui:navBackItem action="displayHome">
		<orbis:message code="i18n.global_cloneEventWizard.BacktoCale7742931810820975" />
	</ui:navBackItem>
	<ui:navBackItem action="displayGlobalEventDetails" eventId="${event.id}">
		<orbis:message code="i18n.global_cloneEventWizard.BacktoEven9992771988255248" />
	</ui:navBackItem>
</ui:navBack>

<ui:header i18n_subtitle="${isL1 ? event.title : event.l2Title}">
	<orbis:message code="i18n.global_cloneEventWizard.CloneEvent9253114835399069" />
</ui:header>

<script type="text/javascript">
	var emailConfigParams = { };
	var emailConfigsChanged = true;
	
	$(document).ready(function() {
		$("#cloneEventForm").submit(function() {
			orbisAppSr.showLoadingOverlay("<orbis:message code="i18n.global_cloneEventWizard.Cloningeve4645115500369041" javaScriptEscape="true" />");
		});
	});

	function validateStep(stepContainerSelector) {
		var ret = true;
		var validator = $("#cloneEventForm").validate();
		var inputs = $(stepContainerSelector + " :input:not(:hidden):not(:disabled)");
		for (var i = 0; i < inputs.length; i++) { 
			if (!validator.element(inputs[i]))
				ret = false;
		}
		return ret;
	}
	
	function loadEmailConfigs() {
		if (emailConfigsChanged) {
			updateEmailConfigParams();
			if (!orbisAppSr.ajax.systemEmailConfigs.loaded)
				orbisAppSr.ajax.systemEmailConfigs.load();
			else
				orbisAppSr.ajax.systemEmailConfigs.reset();
		}
	}
	
	function updateEmailConfigParams() {
		emailConfigParams.enableRegistrationConfirmationEmail = $("#enableRegistrationConfirmationEmail").prop("checked");
		emailConfigParams.enableWaitListConfirmationEmail = $("#enableWaitingList").prop("checked")
			&& $("#enableWaitListConfirmationEmail").prop("checked");
		emailConfigParams.enableWaitListPromotionEmail = $("#enableWaitingList").prop("checked")
			&& $("#enableWaitListPromotionEmail").prop("checked");
		emailConfigParams.enableCancellationConfirmationEmail = $("#enableCancelation").prop("checked")
			&& $("#enableCancellationConfirmationEmail").prop("checked");
		emailConfigParams.enableUncancellationConfirmationEmail  = $("#enableCancelation").prop("checked")
			&& $("#enableUncancellationConfirmationEmail").prop("checked");
		emailConfigsChanged = false;
	}
</script>

		
<div>
	<w:wizard nextButton=".js--btn--next" previousButton=".js--btn--prev" submitButton=".js--btn--submit" step="0">
		<w:step title="i18n.global_cloneEventWizard.EventDetai2606868785952640" target="#eventDetails" materialIcon="create" clickable="true" validate="validateStep('#eventDetails')" />
		<w:step title="i18n.global_cloneEventWizard.CloningOpt0162463864882259" target="#cloningOptions" materialIcon="file_copy" clickable="true" validate="validateStep('#cloningOptions')" />
		<w:step title="i18n.global_cloneEventWizard.SystemEmai8204628294463687" target="#systemEmails" materialIcon="email" clickable="true" onclick="loadEmailConfigs()" />
		<w:step title="i18n.global_cloneEventWizard.Other3189342500106217" target="#other" materialIcon="more_horiz" clickable="true" />
	</w:wizard>
</div>

<ui:gridCol width="8">
	<ui:panel>
		<orbis:validate formId="cloneEventForm" />
		<ui:form id="cloneEventForm" class="form-horizontal">
			<o:encrypt input="true" action="cloneEvent" />
			<input type="hidden" name="eventId" value="${event.id}" />
			
			<div id="eventDetails" class="margin--b--m display--none">
				<%@ include file="global_cloneEventWizard_eventDetailsStep.jsp"%>
			</div>
			<div id="cloningOptions" class="display--none">
				<%@ include file="global_cloneEventWizard_cloningOptionsStep.jsp"%>
			</div>
			
			<div id="systemEmails" class="display--none">
				<ui:ajax id="systemEmailConfigs" action="ajaxLoadSystemEmailConfigs" eventId="${event.id}" jsParams="emailConfigParams" />
			</div>
			
			<div id="other" class="display--none">
				<%@ include file="global_cloneEventWizard_otherStep.jsp"%>
			</div>
			
			<ui:button classes="js--btn--prev"   size="large" type="info"><orbis:message code="i18n.global_cloneEventWizard.Previous5562806979798756" /></ui:button>
			<ui:button classes="js--btn--next"   size="large" type="success"><orbis:message code="i18n.global_cloneEventWizard.Next2735324935814877" /></ui:button>
			<ui:button classes="js--btn--submit" size="large" type="success" onclick="$('#cloneEventForm').submit();"><orbis:message code="i18n.global_cloneEventWizard.Submit0706074248293862" /></ui:button>
		</ui:form>
	</ui:panel>
</ui:gridCol>