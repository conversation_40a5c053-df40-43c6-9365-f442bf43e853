<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>

<c:set var="staffOwner" value="${not empty currentUser.assignedTypes['Manage Individual Events'] }" />

<c:if test="${staffOwner || not empty currentUser.assignedTypes['Events - Can add event'] || not empty currentUser.assignedTypes['Manage Events'] || (not empty currentUser && siteElement.contentItem.addEventFlag)}">
	<ui:actionsGroup i18n_title="i18n.common.actions" id="global_interactions" >
		<c:if test="${staffOwner || not empty currentUser.assignedTypes['Manage Events'] || not empty currentUser.assignedTypes['Events - Can add event'] || (not empty currentUser && siteElement.contentItem.addEventFlag)}">
			<c:if test="${!module.enableAdvancedEvents || !(staffOwner || not empty currentUser.assignedTypes['Manage Events'] || not empty currentUser.assignedTypes['Events - Can add event'])}">
				<ui:actionsGroupItem action="displayEventEdit">
					<spring:message code="i18n.common.event.addEvent" />
				</ui:actionsGroupItem>
			</c:if>
			<c:if test="${module.enableAdvancedEvents && (staffOwner || not empty currentUser.assignedTypes['Manage Events'] || not empty currentUser.assignedTypes['Events - Can add event'])}">
				<ui:dropdown id="add-event" i18n_title="i18n.common.event.addEvent">
					<ui:dropdownItem action="displayEventEdit">
						<orbis:message code="i18n.global_actionBar.AddaSimpleEvent" />
					</ui:dropdownItem>
					<ui:dropdownItem action="displayEventEdit" advancedEvent="true">
						<orbis:message code="i18n.global_actionBar.AddanAdvancedEvent" />
					</ui:dropdownItem>
				</ui:dropdown>
			</c:if>
		</c:if>

		<c:if test="${not empty currentUser.assignedTypes['Manage Events']}">
			<ui:actionsGroupItem action="displayPendingEvents">
				<spring:message code="i18n.global_home.pendingApproval" />(${numberOfPending})
			</ui:actionsGroupItem>
			<c:if test="${currentUser.canPerformAdvancedSearches}">
				<ui:button show="searchModal">
					<spring:message code="i18n.global_home.searchEvents" />
				</ui:button>
				<ui:actionsGroupItem action="search" subAction="search" searchType="registrations">
					<spring:message code="i18n.global_home.searchRegistrations" />
				</ui:actionsGroupItem>
			</c:if>
		</c:if>
	</ui:actionsGroup>

	
	<c:if test="${staffOwner || not empty currentUser.assignedTypes['Manage Events'] || not empty currentUser.assignedTypes['Events - Can add event'] || (not empty currentUser && siteElement.contentItem.addEventFlag)}">
		<c:if test="${!module.enableAdvancedEvents || !(staffOwner || not empty currentUser.assignedTypes['Manage Events'] || not empty currentUser.assignedTypes['Events - Can add event'])}">
			<ui:button action="displayEventEdit">
				<spring:message code="i18n.common.event.addEvent" />
			</ui:button>
		</c:if>
		<c:if test="${module.enableAdvancedEvents && (staffOwner || not empty currentUser.assignedTypes['Manage Events'] || not empty currentUser.assignedTypes['Events - Can add event'])}">
			<ui:dropdown id="add-event" i18n_title="i18n.common.event.addEvent">
				<ui:dropdownItem action="displayEventEdit">
					<orbis:message code="i18n.global_actionBar.AddaSimpleEvent" />
				</ui:dropdownItem>
				<ui:dropdownItem action="displayEventEdit" advancedEvent="true">
					<orbis:message code="i18n.global_actionBar.AddanAdvancedEvent" />
				</ui:dropdownItem>
			</ui:dropdown>
		</c:if>
	</c:if>

	<c:if test="${not empty currentUser.assignedTypes['Manage Events']}">
		<ui:button action="displayPendingEvents">
			<spring:message code="i18n.global_home.pendingApproval" />(${numberOfPending})
		</ui:button>
	</c:if>
	

	<c:if test="${currentUser.canPerformAdvancedSearches}">
		<ui:modal id="searchModal" i18n_title="i18n.global_actionBar.SearchOptions">
			<orbis:message code="i18n.global_actionBar.Pleasesele5473637525624286" />
			:
			<ul class="nav nav-tabs nav-stacked">
				<li>
					<o:nav anchor="true" action="search" subAction="search" searchType="events">
						<orbis:message code="i18n.global_actionBar.RegularSearch" />
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="searchWithNoteCategories">
						<orbis:message code="i18n.global_actionBar.SearchEven10181322342511068" />
					</o:nav>
				</li>
			</ul>
		</ui:modal>
	</c:if>
</c:if>