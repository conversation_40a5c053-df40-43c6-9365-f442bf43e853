<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="partial" />   --%>

<c:if test="${maxBookingsReached}">
	<script type="text/javascript">
		$(document).ready(function(){
			<c:if test="${isL1}">
				orbisAppSr.alertDialog("${not empty siteElement.contentItem.maxFutureAttendingMessage ? siteElement.contentItem.maxFutureAttendingMessage : "<orbis:message code='i18n.global_eventDetailsStandard.Youhaverea2750043592304581' javaScriptEscape='true' />"}", true);
			</c:if>
			<c:if test="${isL2}">
				orbisAppSr.alertDialog("${not empty siteElement.contentItem.maxFutureAttendingMessageL2 ? siteElement.contentItem.maxFutureAttendingMessageL2 : "<orbis:message code='i18n.global_eventDetailsStandard.Youhaverea2750043592304581' javaScriptEscape='true' />"}", true);
			</c:if>
		});
	</script>
</c:if>
<c:if test="${not empty conflictingRegistrations}">
	<script type="text/javascript">
	
		$(document).ready(function(){
			orbisAppSr.setUpRegularDialog("#registrationConflictDialog", {
				<c:if test = "${ siteElement.contentItem.enableSkipRegistrationConflicts }">
					"<orbis:message code = "i18n.global_eventDetailsStandard.Register" javaScriptEscape='true' />" : function(){
						<o:nav action="register" eventId="${event.id}" regTypeId = "${ regTypeId }" cancelConflicts="false" skipConflicts = "true" />
					},
				</c:if>
				"<orbis:message code = "i18n.global_eventDetailsStandard.CancelRegistrationAndRegister" javaScriptEscape='true' />" : function(){
					<o:nav action="register" eventId="${event.id}" regTypeId = "${ regTypeId }" cancelConflicts="true" />
				},
				Close : function(){
					$(this).modal("hide");
				}
			}, {
				width : "700px"
			});
			
			$("div#registrationConflictDialog").modal("show");
		});
	
	</script>
	<div id="registrationConflictDialog" class="hide" title="<orbis:message code='i18n.global_eventDetailsStandard.RegistrationConflict' />">
		<c:if test="${ siteElement.contentItem.enableSkipRegistrationConflicts }">
			<orbis:message code="i18n.global_eventDetailsStandard.ThisRegistrationConflicts" />
		</c:if>

		<c:if test="${ !siteElement.contentItem.enableSkipRegistrationConflicts }">
			<orbis:message code="i18n.global_eventDetailsStandard.Thisregist9014922485842387" />
		</c:if>

		<table class="table zebra">
			<thead class="table__header">
				<tr class="table__row--header">
					<th style="width: 25%;" class="table__heading">
						<orbis:message code="i18n.global_eventDetailsStandard.EventModule" />
					</th>
					<th style="width: 25%;" class="table__heading">
						<orbis:message code="i18n.global_eventDetailsStandard.Event" />
					</th>
					<th style="width: 25%;" class="table__heading">
						<orbis:message code="i18n.global_eventDetailsStandard.Start" />
					</th>
					<th style="width: 25%;" class="table__heading">
						<orbis:message code="i18n.global_eventDetailsStandard.End" />
					</th>
				</tr>
			</thead>
			<tbody>
				<c:forEach var="r" items="${conflictingRegistrations}">
					<tr data-item-id="">
						<td class="table__value">${(isL1) ? r.globalEvent.module.name : r.globalEvent.module.name2}</td>
						<td class="table__value">${(isL1) ? r.globalEvent.title : r.globalEvent.l2Title}</td>
						<td class="table__value">
							<orbis:formatDate value="${r.globalEvent.startDate}" pattern="${orbisDateLong} @ ${orbisTimeShort2}" />
						</td>
						<td class="table__value">
							<orbis:formatDate value="${r.globalEvent.endDate}" pattern="${orbisDateLong} @ ${orbisTimeShort2}" />
						</td>
					</tr>
				</c:forEach>
			</tbody>
		</table>
	</div>
</c:if>

<c:if test="${not empty successMessage}">
	<ui:notification type="success">
		${successMessage}
	</ui:notification>
</c:if>

<c:if test="${saveSuccessful}">
	<ui:notification type="success">
		<orbis:message code="i18n.global_eventDetailsStandard.Eventsaved22110846472431833" />
	</ui:notification>
</c:if>

<c:if test="${not empty sent && not empty total}">
	<ui:notification type="success">
		<orbis:message code="i18n.global_eventDetailsStandard.sentemails6512115846012954" arguments="${sent}, ${total}" />
	</ui:notification>
</c:if>

<ui:form id="displayClonedEventsForm" start="${eventDate}" action="displayHome"></ui:form>

<c:if test="${numOfEvents > 0}">
	<ui:notification duration="10000" type="success">
		<orbis:message code="i18n.global_eventEditAdmin.numberEventsCreated" />: ${numOfEvents} - 
		<a style="color:blue" href="javascript:void(0)" onclick="$('#displayClonedEventsForm').submit();">
			<orbis:message code="i18n.common.clickHereToView" />
		</a>
	</ui:notification>
</c:if>
<c:if test="${numOfEvents == 0}">
	<ui:notification type="error">
		<orbis:message code="i18n.global_eventEditAdmin.noEventsCreatedCheckDates" />.
	</ui:notification>
</c:if>

<orbis:addComponent component="zrssfeed" />

<script type="text/javascript">
	$(document).ready(function () {
		$('#rssDiv').rssfeed('${event.rssFeed}', {
			limit: 5
		});
	});
</script>
<%@ include file="/WEB-INF/spiralRobot/jsp/event/global/global_eventDetailsTitle.jsp"%>
<c:if test="${registration.attended == true && showSatisfactionSurvey == true && siteElement.contentItem.enableSatSurvey == true && event.excludeSatSurvey == false}">
	<div class="orbis-posting-actions">
		<center>
			<ui:button size="small" action="displaySatisfactionSurvey" eventId="${event.id}"><orbis:message code="i18n.global_eventDetailsStandard.ViewSatisf440125518313929" /></ui:button>
		</center>
	</div>
</c:if>
<c:if test="${not empty event.id && (not empty currentUser.assignedTypes['Manage Events'] || not empty currentUser.assignedTypes['Event Calendar - Registration'] || staffOwnerForEvent)}">
	<%@ include file="/WEB-INF/spiralRobot/jsp/event/global/global_eventDetailsBar.jsp"%>
</c:if>
<c:if test="${empty publicView}">
	<%@ include file="/WEB-INF/spiralRobot/jsp/event/global/global_eventDetailsNav.jsp"%>
</c:if>
<%@ include file="/WEB-INF/spiralRobot/jsp/event/global/global_eventMessages_maxLatesNoShowsCancels.jsp"%>
<ui:grid>
	<ui:gridCol width="8">
 		<c:if test="${not empty event.description}">
			<ui:panel>
				<ui:panelTitle><orbis:message code="i18n.global_eventDetailsStandard.EventDescr1417044299861226" /></ui:panelTitle>
				${isL1 ? event.description : event.l2Description}
			</ui:panel>
		</c:if>
		<ui:panel>
			<ui:panelTitle>
				<orbis:message code="i18n.global_eventDetailsStandard.RegistrationDetails" />
			</ui:panelTitle>
			<ui:keyValueList>
				<ui:keyValueListItem>
					<ui:key>
						<orbis:message code="i18n.global_eventDetailsStandard.RegistrationRequired" />:
					</ui:key>
					<ui:value>
						<c:set var="itsANew68">
							<orbis:message code="i18n.global_eventDetailsStandard.No" />
						</c:set>
						<c:if test="${event.enableRegistration}">
							<c:set var="itsANew68">
								<orbis:message code="i18n.global_eventDetailsStandard.Yes" />
							</c:set>
						</c:if>
						<orbis:message code="i18n.global_eventDetailsStandard.eventenabl5578146757225748" arguments="${itsANew68}" />
					</ui:value>
				</ui:keyValueListItem>

				<c:if test="${event.module.enablePrereqs}">
					<ui:keyValueListItem>
						<ui:key>
							<orbis:message code="i18n.global_eventDetailsStandard.PrerequisiteRequired" />:
						</ui:key>
						<ui:value>
							<c:set var="itsANew83">
								<orbis:message code="i18n.global_eventDetailsStandard.No" />
							</c:set>
							<c:if test="${not empty event.tagPre}">
								<c:set var="itsANew83">
									<orbis:message code="i18n.global_eventDetailsStandard.Yes" />
								</c:set>
							</c:if>
							<orbis:message code="i18n.global_eventDetailsStandard.notemptyev7785262543602933" arguments="${itsANew83}" />
						</ui:value>
					</ui:keyValueListItem>
				</c:if>
				<c:if test="${not empty currentUser.assignedTypes['Manage Events'] || staffOwnerForEvent}">
					<c:if test="${event.enableRegistration}">
						<c:if test="${event.advancedEvent}">
							<ui:keyValueListItem>
								<ui:key>
									<orbis:message code="i18n.global_eventDetailsStandard.EcommerceOrderPrefix" />:
								</ui:key>
								<ui:value>${event.orderNumberPrefix}</ui:value>
							</ui:keyValueListItem>
						</c:if>
						<ui:keyValueListItem>
							<ui:key>
								<orbis:message code="i18n.global_eventDetailsStandard.MaxNumbero32073029784187324" />:
							</ui:key>
							<ui:value>${event.maxRegistrations}</ui:value>
						</ui:keyValueListItem>
						<ui:keyValueListItem>
							<ui:key>
								<orbis:message code="i18n.global_eventDetailsStandard.TotalSpacesAvailable" />:
							</ui:key>
							<ui:value>${event.maxRegistrations - regCount}</ui:value>
						</ui:keyValueListItem>
						<ui:keyValueListItem>
							<ui:key>
								<orbis:message code="i18n.global_eventDetailsStandard.RegistrationOpen" />:
							</ui:key>
							<ui:value>
								<c:if test="${not empty event.openDate}">
									<fmt:formatDate value="${event.openDate}" pattern="${orbisDateMedium2TimeShort}" />
								</c:if>
							</ui:value>
						</ui:keyValueListItem>
						<ui:keyValueListItem>
							<ui:key>
								<orbis:message code="i18n.global_eventDetails.registrationDeadline" />:
							</ui:key>
							<ui:value>
								<c:if test="${not empty event.registrationDeadline}">
									<fmt:formatDate value="${event.registrationDeadline}" pattern="${orbisDateMedium2TimeShort}" />
								</c:if>
							</ui:value>
						</ui:keyValueListItem>
						<c:if test="${event.enableWaitingList}">
							<ui:keyValueListItem>
								<ui:key>
									<orbis:message code="i18n.global_eventDetailsStandard.WaitingLis468229196898032" />
								</ui:key>
								<ui:value>
									<c:if test="${not empty event.waitingListCutOffDate}">
										<fmt:formatDate value="${event.waitingListCutOffDate}" pattern="${orbisDateMedium2TimeShort}" />
									</c:if>
								</ui:value>
							</ui:keyValueListItem>
						</c:if>
					</c:if>
					<ui:keyValueListItem>
						<ui:key>
							<orbis:message code="i18n.global_eventDetailsStandard.SendRegist9207048111120547" />:
						</ui:key>
						<ui:value>
							<c:if test="${event.enableRegistrationConfirmationEmail}">
								<orbis:message code="i18n.global_eventDetailsStandard.Yes" />
							</c:if>
							<c:if test="${!event.enableRegistrationConfirmationEmail}">
								<orbis:message code="i18n.global_eventDetailsStandard.No" />
							</c:if>
						</ui:value>
					</ui:keyValueListItem>
					<c:if test="${event.enableWaitingList}">
						<ui:keyValueListItem>
							<ui:key>
								<orbis:message code="i18n.global_eventDetailsStandard.SendWaitin24288523579714016" />:
							</ui:key>
							<ui:value>
								<c:if test="${event.enableWaitListConfirmationEmail}">
									<orbis:message code="i18n.global_eventDetailsStandard.Yes" />
								</c:if>
								<c:if test="${!event.enableWaitListConfirmationEmail}">
									<orbis:message code="i18n.global_eventDetailsStandard.No" />
								</c:if>
							</ui:value>
						</ui:keyValueListItem>
						<ui:keyValueListItem>
							<ui:key>
								<orbis:message code="i18n.global_eventDetailsStandard.SendWaitin46314966052893025" />:
							</ui:key>
							<ui:value>
								<c:if test="${event.enableWaitListPromotionEmail}">
									<orbis:message code="i18n.global_eventDetailsStandard.Yes" />
								</c:if>
								<c:if test="${!event.enableWaitListPromotionEmail}">
									<orbis:message code="i18n.global_eventDetailsStandard.No" />
								</c:if>
							</ui:value>
						</ui:keyValueListItem>
					</c:if>
					<ui:keyValueListItem>
						<ui:key>
							<orbis:message code="i18n.global_eventDetailsStandard.SendCancel30281020348220566" />:
						</ui:key>
						<ui:value>
							<c:if test="${event.enableCancellationConfirmationEmail}">
								<orbis:message code="i18n.global_eventDetailsStandard.Yes" />
							</c:if>
							<c:if test="${!event.enableCancellationConfirmationEmail}">
								<orbis:message code="i18n.global_eventDetailsStandard.No" />
							</c:if>
						</ui:value>
					</ui:keyValueListItem>
					<ui:keyValueListItem>
						<ui:key>
							<orbis:message code="i18n.global_eventDetailsStandard.SendUncancellationConfirmationEmail" />:
						</ui:key>
						<ui:value>
							<c:if test="${event.enableUncancellationConfirmationEmail}">
								<orbis:message code="i18n.global_eventDetailsStandard.Yes" />
							</c:if>
							<c:if test="${!event.enableUncancellationConfirmationEmail}">
								<orbis:message code="i18n.global_eventDetailsStandard.No" />
							</c:if>
						</ui:value>
					</ui:keyValueListItem>
				</c:if>
			</ui:keyValueList>

			<!--  if on/off campus show presenter -->
			<c:if test="${currentUser.eventAdmin || staffOwnerForEvent || staffOwnerForModule || userIsPresenter}">
				<c:if test="${event.locationType==0 || event.locationType==1}">
					<ui:key><orbis:message code="i18n.global_eventDetailsStandard.Presenter" />:</ui:key>
					<ui:value>${event.presenter}</ui:value>
				</c:if>
			</c:if>

			<c:if test="${'waterloo' == siteCode && event.module.showEventOwnerDetails}">
				<c:if test="${currentUser.eventAdmin || staffOwnerForEvent || staffOwnerForModule}">
					<ui:key><orbis:message code="i18n.global_eventEdit_details.EventOwner6307681910760523" />:</ui:key>
					<ui:value>${event.eventOwner}</ui:value>

					<ui:key><orbis:message code="i18n.global_eventEdit_details.EventOwner3551392526826140" />:</ui:key>
					<ui:value>${event.eventOwnerEmail}</ui:value>
				</c:if>
			</c:if>
		<!--  if registered and event is online show login information -->
			<ui:key>
				<orbis:message code="i18n.common.location" />:
			</ui:key>
			<ui:value>
				<c:if test="${event.locationType==0}">
					<orbis:message code="i18n.global_eventEdit_details.OnCampus4715285977447542"/>
					<c:if test="${not empty registration && empty registration.dateCanceled || siteCode == 'waterloo'}">
						<strong>&nbsp;-&nbsp;${event.location}</strong>
					</c:if>
				</c:if>
				<c:if test="${event.locationType==1}">
					<orbis:message code="i18n.global_eventEdit_details.OffCampus4415670070268395" />
				</c:if>
				<c:if test="${event.locationType==2}">
					<orbis:message code="i18n.global_eventEdit_details.Online0305783660213823" />
				</c:if>
			</ui:value>
			<c:if test="${event.locationType==1 && siteCode == 'waterloo'}">
				<ui:key>
					<orbis:message code="i18n.is_newRegWizard_dates.Address3655906713884068"/>:
				</ui:key>
				<ui:value>
					${event.offCampusLocation.postalCode}
				</ui:value>
			</c:if>
			<c:if test="${not empty registration && empty registration.dateCanceled}">
				<c:if test="${event.locationType==1 && siteCode != 'waterloo'}">
					<ui:key>
						<orbis:message code="i18n.is_newRegWizard_dates.Address3655906713884068"/>:
					</ui:key>
					<ui:value>
						${event.offCampusLocation.postalCode}
					</ui:value>
				</c:if>
				<c:if test="${event.locationType==2}">
					<ui:key>
						<orbis:message code="i18n.global_eventEdit_details.MeetingMet7073971811124691" />:
					</ui:key>
					<ui:value>
						<c:if test="${event.onlineLocation.meetingMethod==0}">
							<orbis:message code="i18n.global_eventEdit_details.Teams2866030487557084" /></c:if>
						<c:if test="${event.onlineLocation.meetingMethod==1}">
							<orbis:message code="i18n.global_eventEdit_details.Zoom0972747094598574" /></c:if>
						<c:if test="${event.onlineLocation.meetingMethod==2}">
							<orbis:message code="i18n.global_eventEdit_details.GoToMeetin2621631682528995" /></c:if>
						<c:if test="${event.onlineLocation.meetingMethod==3}">
							<orbis:message code="i18n.global_eventEdit_details.Cisco5750542802571232" /></c:if>
						<c:if test="${event.onlineLocation.meetingMethod==4}">
							<c:set var="vcTitle">${isL1 ? event.module.builtInVideoTitle : event.module.l2BuiltInVideoTitle}</c:set>
							<c:if test="${empty vcTitle}">
								<orbis:message code="i18n.global_eventEdit_details.TokBox5173707590413392" />
							</c:if>
							<c:if test="${not empty vcTitle}">${vcTitle}</c:if>
						</c:if>
						<c:if test="${event.onlineLocation.meetingMethod==5}">
							<orbis:message code="i18n.global_eventDetailsStandard.Other0487261454985506" /></c:if>		
						<c:if test="${event.onlineLocation.meetingMethod==6}">
							<orbis:message code="i18n.global_eventDetailsStandard.GoogleMeet1059023898207983" /></c:if>		
						<c:if test="${event.onlineLocation.meetingMethod==7}">
							<orbis:message code="i18n.global_eventDetailsStandard.AdobeConne2365225605694894" /></c:if>		
						<c:if test="${event.onlineLocation.meetingMethod==8}">
							<orbis:message code="i18n.global_eventDetailsStandard.Kaltura9529713852030273" /></c:if>		
					</ui:value>
					<c:if test="${event.onlineLocation.meetingMethod!=4}">
						<ui:key><orbis:message code="i18n.global_eventEdit.Presenter" />:</ui:key>
						<ui:value>${event.onlineLocation.virtualPresenter}</ui:value>
						<ui:key><orbis:message code="i18n.global_eventEdit_details.MeetingLin7691850159103716" />:</ui:key>
						<ui:value>${event.onlineLocation.meetingLink}</ui:value>
						<ui:key><orbis:message code="i18n.global_eventEdit_details.MeetingPas8236166809225804" />:</ui:key>
						<ui:value>${event.onlineLocation.meetingPassword}</ui:value>
						<ui:key><orbis:message code="i18n.global_eventEdit_details.Additional3225867636388070" />:</ui:key>
						<ui:value>${event.onlineLocation.additionalInstructions}</ui:value>
					</c:if>
				</c:if>
			</c:if>
		</ui:panel>	
		<c:if test="${(not empty currentUser.assignedTypes['Manage Events'] || staffOwnerForEvent || staffOwnerForModule) && not empty staffOwners && o:isVideoConferencingEnabled() }">
			<c:forEach var="presenter" items="${presenterNames}">
				<ui:panel>
					<ui:panelTitle>
						<orbis:message code="i18n.global_eventEdit.Presenter" />
					</ui:panelTitle>
					<ui:key><orbis:message code="i18n.global_eventDetailsStandard.PresenterN5193977101917421" />:</ui:key>
					<ui:value>${presenter.fullName}</ui:value>

					<ui:key><orbis:message code="i18n.global_eventDetailsStandard.Email0232373523085794" />:</ui:key>
					<ui:value>${presenter.username}</ui:value>
					
					<ui:key><orbis:message code="i18n.global_eventDetailsStandard.LinktoOnli8004777398618123" />:</ui:key>
					<ui:value><a href="<orbis:message text="${presenter.onlineLink}" htmlEscape="true" />">${presenter.onlineLink} </a></ui:value>
				</ui:panel>
			</c:forEach>
							</c:if>		
		
		<c:if test="${fn:length(eventPostings)>0}">
			<ui:panel>
				<ui:panelTitle>
					<orbis:message code="i18n.global_eventDetailsStandard.strongPost8813798723620994" />
				</ui:panelTitle>
				<ui:keyValueList>
					<c:forEach var="ep" items="${eventPostings}" varStatus="status">
						<ui:key>
							<c:if test="${not empty postingSeMap[ep[4].id]}">
								<o:nav anchor="true" action="displayPosting" postingId="${ep[5]}" siteElement="${postingSeMap[ep[4].id]}">
									<orbis:message code="i18n.global_eventDetailsStandard.ep5ep12323965120436268" arguments="${ep[5]},${ep[1]}" />
								</o:nav>
							</c:if>
							<c:if test="${empty postingSeMap[ep[4].id]}">
								<orbis:message code="i18n.global_eventDetailsStandard.ep5ep12323965120436268" arguments="${ep[5]},${ep[1]}" />
							</c:if>
						</ui:key>
						<ui:keyValueListItem>
							<ui:key>
								<orbis:message code="i18n.global_eventDetailsStandard.Organization" />:
							</ui:key>
							<ui:value>${ep[0]}</ui:value>
						</ui:keyValueListItem>
						<ui:keyValueListItem>
							<ui:key>
								<orbis:message code="i18n.global_eventDetailsStandard.Division" />:
							</ui:key>
							<ui:value>${ep[2]}</ui:value>
						</ui:keyValueListItem>
						<ui:keyValueListItem>
							<ui:key>
								<orbis:message code="i18n.global_eventDetailsStandard.DatePosted" />:
							</ui:key>
							<ui:value>
								<orbis:formatDate value="${ep[3]}" pattern="${orbisDateLong} @ ${orbisTimeShort2}" />
							</ui:value>
						</ui:keyValueListItem>
						<ui:keyValueListItem>
							<ui:key>
								<orbis:message code="i18n.global_eventDetailsStandard.Postedin" />:
							</ui:key>
							<ui:value>${isL1 ? ep[4].name : ep[4].name2}</ui:value>
						</ui:keyValueListItem>
					</c:forEach>
				</ui:keyValueList>
			</ui:panel>
		</c:if>
		<c:if test="${not empty programTasks}">
			<ui:panel>
				<ui:panelTitle>
					<orbis:message code="i18n.global_eventDetailsStandard.Programact8122382969957751" />
				</ui:panelTitle>
				<ui:keyValueList>
					<c:forEach var="pt" items="${programTasks}">
						<c:set var="isEnrolled" value="${not empty enrollmentMap[pt.id]}" />
						<ui:key>
							<c:if test="${not empty ptModuleMap[pt.program.module.id]}">
								<c:if test="${isEnrolled}">
									<o:nav anchor="true" action="displayEnrollment" enrollmentId="${enrollmentMap[pt.id][1]}" siteElement="${ptModuleMap[pt.program.module.id]}">
										${pt.program.name}
									</o:nav>
									-
									<o:nav anchor="true" action="displayEnrollmentTaskDetails" enrollmentTaskId="${enrollmentMap[pt.id][0]}" siteElement="${ptModuleMap[pt.program.module.id]}">
										${pt.name}
									</o:nav>
								</c:if>
								<c:if test="${!isEnrolled}">
									<o:nav anchor="true" action="displayProgramDetails" programId="${pt.program.id}" siteElement="${ptModuleMap[pt.program.module.id]}">
										${pt.program.name}
									</o:nav>
									-
									<o:nav anchor="true" action="displayTaskDetails" taskId="${pt.id}" siteElement="${ptModuleMap[pt.program.module.id]}">
										${pt.name}
									</o:nav>
								</c:if>
								
							</c:if>
							<c:if test="${empty ptModuleMap[pt.program.module.id]}">
								${pt.program.name} - ${pt.name}
							</c:if>
						</ui:key>
						<c:if test="${not empty pt.program.description}">
							<ui:key>
								<orbis:message code="i18n.global_eventDetailsStandard.Descriptio7107259205531749" />:
							</ui:key>
							<ui:value>${pt.program.description}</ui:value>
						</c:if>
					</c:forEach>
				</ui:keyValueList>
			</ui:panel>
		</c:if>
		<c:if test="${(not empty currentUser.assignedTypes['Manage Events'] || staffOwnerForEvent) && not empty staffOwners}">
			<ui:panel>
				<ui:panelTitle>
					<orbis:message code="i18n.global_eventDetailsStandard.StaffOwners" />
				</ui:panelTitle>
				<c:forEach var="o" items="${staffOwners}">
					<ui:key>
						${o.fullName}
					</ui:key>
				</c:forEach>
			</ui:panel>
		</c:if>
		<c:if test="${not empty event.cCPosition}">
			<ui:panel>
				<ui:panelTitle>
					<orbis:message code="i18n.global_eventDetailsStandard.CocurricularRecord" />
				</ui:panelTitle>
				<ui:keyValueList>
					<ui:keyValueListItem>
						<ui:key><orbis:message code="i18n.global_eventDetailsStandard.CCRActivityPosition" /></ui:key>
						<ui:value>
							<c:if test="${not empty event.cCPosition}">
								${event.cCPosition.title}
								<c:if test="${not empty event.cCPosition.activity}">
									/ ${event.cCPosition.activity.activity}
								</c:if>
							</c:if>
						</ui:value>
					</ui:keyValueListItem>
				</ui:keyValueList>
			</ui:panel>
		</c:if>
		<%@ include file="global_eventDetailsStandard_restrictions.jsp"%>
	</ui:gridCol>
	<ui:gridCol width="4">
		<c:if test="${canViewAttendanceChart}">
			<ui:ajax action="ajaxLoadEventAttendanceChart" id="eventAttendanceChart" css="min-height: 400px;" eventId="${event.id}" />
		</c:if>
		<c:if test="${not empty event.id && (not empty currentUser.assignedTypes['Manage Events'] || staffOwnerForEvent) || not empty currentUser.assignedTypes['Event Calendar - Registration']}">
			<ui:panel classes="plain">
				<ui:panelTitle><orbis:message code="i18n.global_eventDetailsStandard.EventAdmin0236397069102232" /></ui:panelTitle>
				<ui:grid>
					<ui:gridCol width="12">
						<ul class="list--plain">
							<c:if test="${(not empty currentUser.assignedTypes['Manage Events'] || staffOwnerForEvent)}">
								<li><span class="label"><orbis:message code="i18n.global_eventDetailsStandard.EventStatus" />:</span></li>
								<li>
									<c:if test="${event.status == 0}">
										<span class="tag-label info">
											<orbis:message code="i18n.global_eventDetailsStandard.Pending" />
										</span>
									</c:if>
									<c:if test="${event.status == 1}">
										<span class="tag-label success">
											<orbis:message code="i18n.global_eventDetailsStandard.Approved" />
										</span>
									</c:if>
									<c:if test="${event.status == 2}">
										<span class="tag-label error">
											<orbis:message code="i18n.global_eventDetailsStandard.Expired" />
										</span>
									</c:if>
									<c:if test="${event.status == 3}">
										<span class="tag-label warning">
											<orbis:message code="i18n.global_eventDetailsStandard.Cancelled" />
										</span>
									</c:if>
								</li>
							</c:if>
						</ul>
						<c:if test="${!event.live}">
							<ui:note type="error">
								<orbis:message code="i18n.global_eventDetailsStandard.Eventisnot28304278964173624" />
							</ui:note>
						</c:if>
						<c:if test="${not empty event.shortUrl}">
							<ui:textbox name="" readOnly="true" onfocus="$(this).select();" value="${event.shortUrl}"><orbis:message code="i18n.global_eventDetailsStandard.EventLink3203483046297091" />:</ui:textbox>
						</c:if>
						<c:if test="${not empty publicLink}">
							<ui:textbox name="" value="${publicLink}" readOnly="true"  onfocus="$(this).select();"><orbis:message code="i18n.global_eventDetailsStandard.PublicLink8109452403489327" />:</ui:textbox>
						</c:if>
						<c:if test="${event.enableSwipeCheckin && not empty swipeLink}">
							<ui:textbox name="" value="${swipeLink}" readOnly="true"  onfocus="$(this).select();"><orbis:message code="i18n.global_eventDetailsStandard.SwipeLink" /></ui:textbox>
							<ui:grid>
								<ui:gridCol width="7">
									<ui:button size="small"><orbis:message code="i18n.global_eventDetailsStandard.SwipeCheckIn" /></ui:button>
								</ui:gridCol>
								<ui:gridCol width="5">
									<c:if test="${event.swipeValidate}">
										<span class="label label-inverse">
											<orbis:message code="i18n.global_eventDetailsStandard.OverrideCode" />: ${event.swipeOverrideCode}
										</span>
									</c:if>
								</ui:gridCol>
							</ui:grid>
						</c:if>
						<c:if test="${event.invitationOnly}">
							<ui:note type="error">
								<orbis:message code="i18n.global_eventDetailsStandard.Accesslimi6252812881746528" />
							</ui:note>
						</c:if>
					</ui:gridCol>
				</ui:grid>
			</ui:panel>
		</c:if>
		<c:if test="${event.enableRegistration && not empty currentUser && event.status == 1 && empty currentUser.assignedTypes['Manage Events'] &&  !staffOwnerForEvent && !staffOwnerForModule &&!userIsPresenter}">
			<div class="eventRegisterActions">
				<%@ include file="global_eventRegister.jsp"%>
			</div>
		</c:if>
		<c:if test="${event.enableRegistration && empty currentUser && event.status == 1}">
			<div class="orbis-posting-actions text-center">
				<ui:note type="info">
					<orbis:message code="i18n.global_eventDetails.eventOpenToRegistrations" />
				</ui:note>
				<ui:button size="small" eventId="${event.id}" action="displayGlobalEventDetails"><orbis:message code="i18n.global_eventDetails.clickToLogInUPPERCASE" /></ui:button>
			</div>
		</c:if>
		<c:if test="${not empty event.id && (not empty currentUser.assignedTypes['Manage Events'] || staffOwnerForEvent) || not empty currentUser.assignedTypes['Event Calendar - Registration']}">
			<c:if test="${(event.status != 2 && event.status != 3 ) && empty limitByGroup && empty restrictLevel && empty activeRestrictTagGroupingss && empty limitByProgram && empty limitByCoopProgram && !event.invitationOnly}">
				<ui:note type="info">
					<orbis:message code="i18n.global_eventDetailsStandard.Thiseventi5666237534413959" />
				</ui:note>
			</c:if>
			<%@ include file="global_eventRegistrationCounts.jsp"%>
		</c:if>
		<c:if test="${!currentUser.eventAdmin && not empty event.presenter && !staffOwnerForEvent && !staffOwnerForModule && !userIsPresenter}">
			<ui:panel classes="margin--t--s">
				<ui:panelTitle><orbis:message code="i18n.global_eventDetailsStandard.Presenter" /></ui:panelTitle>
					<c:if test="${event.locationType==0 || event.locationType==1 || event.locationType==3}">
						${event.presenter}<br/>
					</c:if>
					<c:if test="${event.locationType==2}">
						<c:forEach var="presenter" items="${presenterNames}">
							${presenter.fullName}<br/>
						</c:forEach>
					</c:if>
			</ui:panel>
		</c:if>
	</ui:gridCol>
</ui:grid>
