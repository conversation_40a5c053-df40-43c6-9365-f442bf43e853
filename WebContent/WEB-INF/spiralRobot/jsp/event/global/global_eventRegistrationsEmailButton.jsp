<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>
<c:if test="${not empty emailSent}">
	<ui:notification type="success">
		<orbis:message code="i18n.global_eventRegistrationsEmailButton.Emailshave6231157096775053" />
	</ui:notification>
</c:if>

<ui:form id="displayEmailAttendees" eventId="${event.id}" comingFrom="${comingFrom}" emailTo="" action="displayEmailRegistrants">
	<input type="hidden" name="regIds" id="emailRegIds" value="" />
	<input type="hidden" name="wlIds" id="emailWlIds" value="" />
	<input type="hidden" name="userIds" id="emailUserIds" value="" />
</ui:form>
	
<ui:dropdown id="email-users" i18n_title="i18n.global_eventRegistrationsEmailButton.EmailUsers9349636548621682" classes="margin--b--m">
	<c:if test="${emailStats['registered'] > 0}">
		<ui:dropdownItem>
			<a href="javascript:void(0)" onclick="$('#emailTo').val('registered');$('#displayEmailAttendees').submit();">
				<orbis:message code="i18n.global_eventEditAdminEmailAttendees.registeredDidNotCancelUsers" arguments="${emailStats['registered']}" />
			</a>
		</ui:dropdownItem>
	</c:if>
	<c:if test="${emailStats['registeredAndCanceled'] > 0}">
		<ui:dropdownItem>
			<a href="javascript:void(0)" onclick="$('#emailTo').val('registeredAndCanceled');$('#displayEmailAttendees').submit();">
				<orbis:message code="i18n.global_eventEditAdminEmailAttendees.registeredCancelledUsers" arguments="${emailStats['registeredAndCanceled']}" />
			</a>
		</ui:dropdownItem>
	</c:if>
	<c:if test="${emailStats['registeredAndAttended'] > 0}">
		<ui:dropdownItem>
			<a href="javascript:void(0)" onclick="$('#emailTo').val('registeredAndAttended');$('#displayEmailAttendees').submit();">
				<orbis:message code="i18n.global_eventEditAdminEmailAttendees.registeredAttendedUsers" arguments="${emailStats['registeredAndAttended']}" />
			</a>
		</ui:dropdownItem>
	</c:if>
	<c:if test="${emailStats['registeredAndNotAttended'] > 0}">
		<ui:dropdownItem>
			<a href="javascript:void(0)" onclick="$('#emailTo').val('registeredAndNotAttended');$('#displayEmailAttendees').submit();">
				<orbis:message code="i18n.global_eventEditAdminEmailAttendees.registeredDidNotAttendUsers" arguments="${emailStats['registeredAndNotAttended']}" />
			</a>
		</ui:dropdownItem>
	</c:if>
	<c:if test="${event.enableWaitingList && waitCount > 0}">
		<ui:dropdownItem>
			<a href="javascript:void(0)" onclick="$('#emailTo').val('waitingList');$('#displayEmailAttendees').submit();">
				<orbis:message code="i18n.global_eventRegistrationsEmailButton.WaitingLis0078280506364989" arguments="${waitCount}" />
			</a>
		</ui:dropdownItem>
	</c:if>
	<ui:dropdownItem>
		<a href="javascript:void(0)" onclick="$('#emailTo').val('selected');$('#emailRegIds').val(getSelectedRegistrantIds());$('#emailWlIds').val(getSelectedWaitingListIds());$('#emailUserIds').val(getSelectedUserIds());$('#displayEmailAttendees').submit();">
			<orbis:message code="i18n.global_eventEditAdminEmailAttendees.selectedUsers" arguments="0" />
		</a>
	</ui:dropdownItem>
</ui:dropdown>