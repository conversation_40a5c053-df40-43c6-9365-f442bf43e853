<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>
<ui:grid>
	<ui:gridCol width="12">
		<ul class="nav nav-pills">
			<li <c:if test="${'overview'==currentTab}">class="active"</c:if>>
				<a href="#"
				onclick="<o:nav action="displayEventAdminOverview" eventId="${event.id}" />">
					<orbis:message code="i18n.global_eventDetailsSubNav.Overview" />
				</a>
			</li>
			<li <c:if test="${'staffOwners'==currentTab}">class="active"</c:if>>
				<a href="#" onclick="<o:nav action="displayAssignStaffOwner" eventId="${event.id}" />">
					<orbis:message code="i18n.global_eventDetailsNav.StaffOwners" /> <c:if
						test="${navStaffCount>0}">
						<span class="badge badge-info">${navStaffCount}</span>
					</c:if>
				</a>
			</li>
			<c:if test="${siteElement.contentItem.enableEventTags}">
				<li <c:if test="${'tags'==currentTab}">class="active"</c:if>>
					<a href="#" onclick="<o:nav action="displayEventTags" eventId="${event.id}" />">
						<orbis:message code="i18n.global_eventDetailsNav.Tags" />
						<c:if test="${tagsCount>0}">
							<span class="badge badge-info">${tagsCount}</span>
						</c:if>
					</a>
				</li>
			</c:if>
			<c:if test="${event.module.enablePrereqs}">
				<li <c:if test="${'prereqs'==currentTab}">class="active"</c:if>>
					<o:nav anchor="true" action="displayGlobalEventPrereqs" eventId="${event.id}">
						<orbis:message code="i18n.global_eventDetailsNav.Prerequisites" />
						<c:if test="${not empty event.tagPre || not empty event.tagPost}">
							<span class="badge badge-info">1</span>
						</c:if>
					</o:nav>
				</li>
			</c:if>	
			<li <c:if test="${'emails'==currentTab}">class="active"</c:if>>
				<a href="#" onclick="<o:nav action="displayEventEmails" eventId="${event.id}" />">
					<orbis:message code="i18n.global_eventAdminSubNav.AutomaticEmails" />
				</a>
			</li>
			<li <c:if test="${'emailLog'==currentTab}">class="active"</c:if>>
				<a href="#" onclick="<o:nav action="displayEventEmailLog" eventId="${event.id}" />">
					<orbis:message code="i18n.global_eventAdminSubNav.EmailLog" />
					<c:if test="${eventEmailCount>0}">
						<span class="badge badge-info">${eventEmailCount}</span>
					</c:if>
				</a>
			</li>

			<li <c:if test="${'audit'==currentTab}">class="active"</c:if>>
				<o:nav anchor="true" action="displayEventAudit" eventId="${event.id}" resetFilters="true">
					<orbis:message code="i18n.global_eventAdminSubNav.Audit" />
				</o:nav>
			</li>
			
			<c:if test="${interactionModule.engagementsEnabled}">
				<li <c:if test="${'engagements'==currentTab}">class="active"</c:if>>
					<o:nav anchor="true" action="displayManageEngagementActivities" eventId="${event.id}" resetFilters="true">
						<orbis:message code="i18n.global_eventAdminSubNav.Engagement" />
					</o:nav>
				</li>
			</c:if>
		</ul>
	</ui:gridCol>
</ui:grid>