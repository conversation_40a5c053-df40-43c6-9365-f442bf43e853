<ui:dashboardStats>
	<ui:dashboardStatsItem eventId="${event.id}" value="${regCount}" backgroundColor="${'registrants' == registrantType ? 'success' : ''}" data-registrant-type="registrants"><orbis:message code="i18n.global_registrants.Registrants" /></ui:dashboardStatsItem>
	<c:if test="${event.invitationOnly}">
		<ui:dashboardStatsItem eventId="${event.id}" value="${inviteCount}" backgroundColor="${'invitations' == registrantType ? 'success' : ''}" data-registrant-type="invitations"><orbis:message code="i18n.global_registrantsHeader.Invitation1691525136865756" /></ui:dashboardStatsItem>
	</c:if>
	<c:if test="${event.enableWaitingList}">
		<ui:dashboardStatsItem eventId="${event.id}" value="${waitCount}" backgroundColor="${'waitingList' == registrantType ? 'success' : ''}" data-registrant-type="waitingList"><orbis:message code="i18n.global_registrants.WaitingList" /></ui:dashboardStatsItem>
	</c:if>
	<ui:dashboardStatsItem eventId="${event.id}" value="${cancelCount}" backgroundColor="${'cancelled' == registrantType ? 'success' : ''}" data-registrant-type="cancelled"><orbis:message code="i18n.global_registrantsHeader.Cancelled7917475938921784" /></ui:dashboardStatsItem>
</ui:dashboardStats>