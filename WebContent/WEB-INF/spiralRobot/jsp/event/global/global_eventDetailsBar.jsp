<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>

<script>
	var controllerPath = "${siteElement.fullPath}.htm";
	
	var uids = [];
	
	function printNameBadges(userGroup, avery, eventId){
		var request = {};
		request.action = '<o:encrypt action="downloadPrintableNameBadges" />';
		request.userGroup = userGroup;
		request.avery = avery;
		request.eventId = eventId;
		request.uids = uids;
		orbisAppSr.buildForm(request).submit();
	}
	
	$(document).ready(function() {
		$("#acrmUserName").autocomplete({
			source: controllerPath + "?action=<o:encrypt action="lookupNewUsers" />&r=" + (Math.random() * 10000),
			minLength: 3,
			select: function( event, ui ) {
				$("#userSearchForm #acrmUserId").val(ui.item.id);
				<c:if test="${event.advancedEvent}">
					disableRegisterButtons();
					var request = {
						action: '<o:encrypt action="loadValidRegTypes" />', 
						eventId: '${event.id}',
						acrmUserId: ui.item.id
					};
					
					$("#advancedRegisterPlaceholder").load(controllerPath, request, function(data, status, xhr){
						if (orbisAppSr.checkAjaxResponse(xhr))
						{
							$("#registerPublicButton, #hidePublicButton").addClass("display--none");
							$("#publicButton, #registerButton").removeClass("display--none");
						}			
					}, "html");
				</c:if>
			}
		}).keyup(function(e) {
			$("#acrmUserId").val("");
		});
		
		$("#acrmUserNameWaitingList").autocomplete({
			source: controllerPath + "?action=<o:encrypt action="lookupNewUsers" />&r=" + (Math.random() * 10000),
			minLength: 3,
			select: function( event, ui ) {
				$("#userSearchFormWaitingList #acrmUserIdWaitingList").val(ui.item.id);
				<c:if test="${event.advancedEvent}">
					disableWaitingListRegisterButtons();
					var request = {
						action: '<o:encrypt action="loadValidRegTypes" />', 
						eventId: '${event.id}',
						acrmUserId: ui.item.id,
						waitList : true
					};
					
					$("#advancedWaitingListRegisterPlaceholder").load(controllerPath, request, function(data, status, xhr){
						if (orbisAppSr.checkAjaxResponse(xhr))
						{
							$("#waitingListRegisterPublicButton, #waitingListHidePublicButton").addClass("display--none");
							$("#waitingListPublicButton, #waitingListRegisterButton").removeClass("display--none");
						}			
					}, "html");
				</c:if>
			}
		}).keyup(function(e) {
			if ($("#acrmUserNameWaitingList").val()==="") {
				$("#acrmUserIdWaitingList").val("");
			}
		});
		
		<c:if test="${event.invitationOnly}">
			$("#acrmUserNameInvitation").autocomplete({
				source: controllerPath + "?action=<o:encrypt action="lookupNewUsers" />&r=" + (Math.random() * 10000),
				minLength: 3,
				select: function( event, ui ) {
					$("#userSearchFormInvitation #acrmUserIdInvitation").val(ui.item.id);
				}
			}).keyup(function(e) {
				if ($("#acrmUserNameInvitation").val()==="") {
					$("#acrmUserIdInvitation").val("");
				}
			});
		</c:if>
	});
	
	<c:if test="${not empty hasPublicRegTypes || not empty hasWaitingListPublicRegTypes}">
		function loadPublicRegTypes(waitList) {
			var request = {
				action: '<o:encrypt action="loadValidRegTypes" />', 
				eventId: '${event.id}',
				waitList: waitList
			};
			
			if (waitList)
				disableWaitingListRegisterButtons();
			else
				disableRegisterButtons();
			
			$(waitList ? "#advancedWaitingListRegisterPlaceholder" : "#advancedRegisterPlaceholder")
				.load(controllerPath, request, function(data, status, xhr){
				if (orbisAppSr.checkAjaxResponse(xhr))
				{
					if (waitList) {
						$("#acrmUserNameWaitingList, #acrmUserIdWaitingList").val("");
						$("#waitingListPublicButton, #waitingListRegisterButton").addClass("display--none");
						$("#waitingListRegisterPublicButton, #waitingHideListPublicButton").removeClass("display--none");
					} else {
						$("#acrmUserName, #acrmUserId").val("");
						$("#publicButton, #registerButton").addClass("display--none");
						$("#registerPublicButton, #hidePublicButton").removeClass("display--none");
					}
				}			
			}, "html");
		}
		
		function hidePublicRegTypes(waitList) {
			if (waitList) {
				if ($("#waitingHideListPublicButton").is(":visible")) {
					$("#waitingListRegisterPublicButton, #waitingListHidePublicButton").addClass("display--none");
					$("#waitingListPublicButton, #waitingListRegisterButton").removeClass("display--none");
					$("#advancedWaitingListRegisterPlaceholder").html("");
					disableWaitingListRegisterButtons();
				}
			} else {
				if ($("#hidePublicButton").is(":visible")) {
					$("#registerPublicButton, #hidePublicButton").addClass("display--none");
					$("#publicButton, #registerButton").removeClass("display--none");
					$("#advancedRegisterPlaceholder").html("");
					disableRegisterButtons();
				}
			}
		}
	</c:if>
	
	<c:if test="${!event.waitlistOnly}">
		function checkRegistrationType() {
			if ($("#addRegistrantModal input[name=regTypeId]").val()) {
				$("#registerButton, #registerPublicButton").prop("disabled", false);
			}
		}
		
		function disableRegisterButtons() {
			$("#registerButton, #registerPublicButton").prop("disabled", true);
		}
		
		function attachRegistrant() {	
			if ($("#acrmUserName", "#userSearchForm").val().trim() != ""
				|| $("#bulkUsernames").val().trim())
			{
				$("#userSearchForm").submit();
			}
			else
			{
				$("#errMsg").removeClass("display--none");
				$("#acrmUserName").focus();
			}
		}
		
		function registerPublicRegistrant() {
			$("#userSearchForm > input[name=action]").val("<o:encrypt action = "addPublicRegistrant" />");
			$("#userSearchForm").submit();
		}		
	
		function handleJobSearch(event, data, formatted) {
			$("#userSearchForm #acrmUserId").val(data[1]);
		}
		
		function handleWaitingListSearch(event, data, formatted) {
			$("#userSearchFormWaitingList #acrmUserIdWaitingList").val(data[1]);
		}
	</c:if>
	
	<c:if test="${event.enableWaitingList}">
		function attachWaitingListRegistrant() {	
			if ($("#acrmUserNameWaitingList", "#userSearchFormWaitingList").val().trim() != ""
				|| $("#bulkUsernamesWaitingList").val().trim())
			{
				$("#userSearchFormWaitingList").submit();
			}
			else
			{
				$("#errMsgWaitingList").removeClass("display--none");
				$("#acrmUserNameWaitingList").focus();
			}
		}
		
		function addWaitingListPublicRegistrant() {
			$("#userSearchFormWaitingList > input[name=action]").val("<o:encrypt action = "addPublicRegistrantToWaitingList" />");
			$("#userSearchFormWaitingList").submit();
		}
		
		function checkWaitingListRegistrationType() {
			if ($("#addRegistrantToWaitingListModal input[name=regTypeId]").val()) {
				$("#waitingListRegisterButton, #waitingListRegisterPublicButton").prop("disabled", false);
			}
		}
		
		function disableWaitingListRegisterButtons() {
			$("#waitingListRegisterButton, #waitingListRegisterPublicButton").prop("disabled", true);
		}
	
		function handleJobSearchWaitingList(event, data, formatted) {
			$("#userSearchFormWaitingList #acrmUserIdWaitingList").val(data[1]);
		}
	</c:if>
	
	function getSelectedRegistrantIds() {
		var selectedIdsList = "";
		
		$(".js--reg-selected-check:checked").each(function() {
			selectedIdsList += "|" + $(this).data("regId");
		});
		
		return selectedIdsList.substr(1);
	}
	
	function getSelectedWaitingListIds() {
		var selectedIdsList = "";
		
		$(".js--wl-selected-check:checked").each(function() {
			selectedIdsList += "|" + $(this).data("wlId");
		});
		
		return selectedIdsList.substr(1);
	}
	
	function getSelectedUserIds() {
		var selectedIdsList = "";
		
		$(".js--user-selected-check:checked").each(function() {
			selectedIdsList += "|" + $(this).data("uid");
		});
		
		return selectedIdsList.substr(1);
	}
</script>

<ui:form id="displayLimitAccessByGroupForm" eventId="${event.id}" action="displayLimitAccessByGroup">
</ui:form>
<ui:form id="displayLimitAccessByProgramForm" eventId="${event.id}" action="displayLimitAccessByProgram">
</ui:form>
<ui:form id="displayLimitAccessByLevelForm" eventId="${event.id}" action="displayLimitAccessByLevel">
</ui:form>
<ui:form id="displayLimitAccessByTagForm" eventId="${event.id}" action="displayLimitAccessByTag">
</ui:form>
<ui:form id="displayLinkToJobForm" eventId="${event.id}" action="displayLinkToJob">
</ui:form>
<ui:form id="displayLinkToPersonForm" eventId="${event.id}" action="displayLinkToPerson">
</ui:form>
<ui:form id="deleteForm" eventId="${event.id}" action="deleteEvent">
</ui:form>
<ui:form id="displayEventCompetenciesConfig" eventId="${event.id}" action="displayEventCompetenciesConfig">
</ui:form>

<ui:sidebar i18n_title="i18n.global_eventRegistrationReports.viewAllRegistrants" id="registrantsSidebar">
	<ul class="list--plain">
		<c:if test="${event.advancedEvent}">
			<li>
				<o:nav anchor="true" action="displayEventRegTypeEdit" eventId="${event.id}">
					<orbis:message code="i18n.global_eventRegTypes.CreateNewR5832375613967171" />
				</o:nav>
			</li>
		</c:if>
		<li>
			<a href="javascript:void(0)" onclick="$('#addRegistrantModal').uiShow()">
				<orbis:message code="i18n.global_registrants.RegisterUser" />
			</a>
		</li>
		
		<c:if test="${event.enableWaitingList}">
			<li>
				<a href="javascript:void(0)" onclick="$('#addRegistrantToWaitingListModal').uiShow()"> 
					<orbis:message code="i18n.beta_global_eventRegister.6310716428692248" /> 
				</a>
			</li>
		</c:if>
		
		<c:if test="${event.invitationOnly}">
			<li>
				<a href="javascript:void(0)" onclick="$('#addInvitationModal').uiShow()"> 
					<orbis:message code="i18n.global_eventDetailsBar.AddInvitat7892673365902033" />
				</a>
			</li>	
		</c:if>
		
		<c:if test="${!event.advancedEvent}">
			<o:nav anchor="true"
				action="displayEventRegistrationBulkUpload"
				eventId="${event.id}" js:regIds="getSelectedRegistrantIds()">
				<orbis:message code="i18n.global_registrants.BulkUpload4437804396240901" />
			</o:nav>
		</c:if>
	</ul>
	
	
	<h2 class="h2"><orbis:message code="i18n.global_registrants.PrintNameB008281021999582028" /></h2>
	<ul class="list--plain">
		<li>
			<h4><orbis:message code="i18n.global_registrants.Student" /></h4>
			<ul class="list--plain margin--l--l">
				<li class="margin--b--m">
					<a href="javascript:void(0);" onclick="printNameBadges('student', 5384, ${event.id});">
						<orbis:message code="i18n.global_registrants.Avery53846persheet" />
					</a>
				</li>
				<li class="margin--b--m">
					<a href="javascript:void(0);" onclick="printNameBadges('student', 5390, ${event.id});" class="margin--b--m">
						<orbis:message code="i18n.global_registrants.Avery53908persheet" />
					</a>
				</li>
				<li class="margin--b--m">
					<a href="javascript:void(0);" onclick="printNameBadges('student', 74558, ${event.id});" class="margin--b--m">
						<orbis:message code="i18n.global_registrants.Avery7455810persheet" />
					</a>
				</li>
			</ul>
		</li>
	
		<li>
			<h4><orbis:message code="i18n.global_registrants.Employer" /></h4>
			<ul class="list--plain  margin--l--l">
				<li class="margin--b--m">
					<a href="javascript:void(0);" onclick="printNameBadges('employer', 5384, ${event.id});" class="margin--b--m">
						<orbis:message code="i18n.global_registrants.Avery53846persheet" />
					</a>
				</li>
				<li class="margin--b--m">
					<a href="javascript:void(0);" onclick="printNameBadges('employer', 5390, ${event.id});" class="margin--b--m">
						<orbis:message code="i18n.global_registrants.Avery53908persheet" />
					</a>
				</li>
				<li class="margin--b--m">
					<a href="javascript:void(0);" onclick="printNameBadges('employer', 74558, ${event.id});" class="margin--b--m">
						<orbis:message code="i18n.global_registrants.Avery7455810persheet" />
					</a>
				</li>
			</ul>
		</li>
	</ul>
	<c:set var="registrantsTab" value="true" />
	<c:set var="comingFrom" value="viewRegistrants" />
	 
	<%@ include file="global_eventRegistrationsEmailButton.jsp"%>
	<c:if test="${!event.advancedEvent && emailText == 'waitingList'}">
		<ui:button id="bulkWaitingList" action="displayEventWaitingListBulkUpload" eventId="${event.id}" classes="margin--b--m"><orbis:message code="i18n.global_waitingList.BulkUpload08690921285934783" /></ui:button>
	</c:if>
	<c:if test="${canSendSurvey}">
		<ui:button id="emailSurvey" action="displayEmailRegistrants" eventId="${event.id}" param-type="survey" emailTo="registeredAndAttended" classes="margin--b--m"><orbis:message code="i18n.global_registrants.EmailSurvey" /></ui:button>
	</c:if>
	<ui:form id="exportRegistrantsForm" eventId="${event.id}" action="exportRegistrants">
		<ui:button type="info" buttonType="submit" classes="margin--b--m"><orbis:message code="i18n.global_eventRegistrationCounts.Export" /></ui:button>
	</ui:form>
</ui:sidebar> 
<ui:actionsGroup i18n_title="i18n.common.actions" id="global_manage_event" >
	<c:if test="${not empty event.id && (not empty currentUser.assignedTypes['Manage Events']|| staffOwnerForEvent)}">
		<ui:dropdown id="event-options" i18n_title="i18n.global_eventDetailsBar.EventOptions">
			<ui:dropdownItem action="displayEventEdit" eventId="${event.id}">
					<orbis:message code="i18n.global_eventDetails.editEvent" />
			</ui:dropdownItem> 
			<ui:dropdownItem action="displayAssignStaffOwner" eventId="${event.id}">
					<orbis:message code = "i18n.global_eventDetailsBar.EditStaffOwners" /> 
					<c:if test="${navStaffCount>0}">
						(${navStaffCount})
					</c:if>
			</ui:dropdownItem> 
			<c:if test="${siteElement.contentItem.enableEventTags}">
				<ui:dropdownItem action="displayEventTags" eventId="${event.id}">
						<orbis:message code = "i18n.global_eventDetailsBar.EditTags" />
						<c:if test="${tagsCount>0}">
							(${tagsCount})
						</c:if>
				</ui:dropdownItem>
			</c:if>
			<ui:dropdownItem action="displayEventEmails" eventId="${event.id}">
					<orbis:message code = "i18n.global_eventDetailsBar.EditEventEmails" />
			</ui:dropdownItem>
			<ui:dropdownItem action="displayCloneEventWizard" eventId="${event.id}">
					<orbis:message code="i18n.global_eventEditAdmin.Cloneevent" />
			</ui:dropdownItem>
			<c:if test="${displayAssignStaffOwners}">
				<ui:dropdownItem onclick="$('#displayAssignStaffOwnerForm').submit();">
						<orbis:message code="i18n.global_eventEditAdmin.assignStaffOwner" />
				</ui:dropdownItem>
			</c:if>
			<ui:dropdownItem onclick="$('#displayLinkToPersonForm').submit();">
				<orbis:message code="i18n.global_eventEditAdmin.linkToUserAccount" />
				<c:if test="${linkedUsersCount>0}">
					(${linkedUsersCount})
				</c:if>
			</ui:dropdownItem>
			<ui:dropdownItem onclick="$('#displayLinkToJobForm').submit();">
				<orbis:message code="i18n.global_eventEditAdmin.linkToJobPosting" />
				<c:if test="${linkedJobsCount>0}">
					(${linkedJobsCount})
				</c:if>
			</ui:dropdownItem>  
			<c:if test="${not empty siteElement.contentItem.copModule || not empty siteElement.contentItem.postingModule}">
				<ui:dropdownItem onclick="$('#displayLimitAccessByProgramForm').submit();">
					<orbis:message code="i18n.global_eventEditAdmin.limitAccessByProgram" />
				</ui:dropdownItem>
			</c:if>
			<ui:dropdownItem onclick="$('#displayLimitAccessByGroupForm').submit();">
				<orbis:message code="i18n.global_eventEditAdmin.limitAccessByUserGroup" />
			</ui:dropdownItem>
			<c:if test="${siteElement.contentItem.limitByLevels}">
				<ui:dropdownItem onclick="$('#displayLimitAccessByLevelForm').submit();">
					<orbis:message code="i18n.global_eventEditAdmin.Limitaccessbylevel" />
				</ui:dropdownItem>
			</c:if>
			<c:if test="${siteElement.contentItem.limitByTags}">
				<ui:dropdownItem onclick="$('#displayLimitAccessByTagForm').submit();">
					 <orbis:message code = "i18n.global_eventEditAdmin.LimitAccessByTag" />
				</ui:dropdownItem>
			</c:if>
			<c:if test="${event.enableRegistration && !competenciesDisabled}">
				<ui:dropdownItem onclick="$('#displayEventCompetenciesConfig').submit();">
					 <orbis:message code="i18n.global_competenciesConfig.ConfigureC7037014676642354"/>
				</ui:dropdownItem>
			</c:if>
						
		</ui:dropdown>
	</c:if>
	
	<c:if test="${not empty currentUser.assignedTypes['Manage Events'] || not empty currentUser.assignedTypes['Event Calendar - Registration'] || staffOwnerForEvent}">
		<c:if test="${event.enableRegistration}">
			<ui:actionsGroupItem show="registrantsSidebar"><orbis:message code="i18n.global_eventRegistrationReports.viewAllRegistrants" /></ui:actionsGroupItem>
		</c:if>
	</c:if>
	
	<c:if test="${not empty event.id && (not empty currentUser.assignedTypes['Manage Events'] ||staffOwnerForEvent)}">
		<c:set var="entityName" value="eventId" />
		<c:set var="entityId" value="${event.id}" />
		<c:set var="type" value="EVENT" />
		<%@ include file="/WEB-INF/spiralRobot/jsp/interaction/interaction_commonActions.jsp"%>
		<c:if test="${regCount==0 && cancelCount==0}">
			<c:set var="confirmShowModal"><orbis:message code="i18n.common.confirmDeleteDependencyItem" javaScriptEscape="true"/></c:set>
			<ui:actionsGroupItem onclick="$('#deleteForm').submit();" i18n_confirmOnclick="${confirmShowModal}"><orbis:message code="i18n.common.delete" /></ui:actionsGroupItem>
		</c:if>
	</c:if>
</ui:actionsGroup> 

<ui:modal i18n_title="i18n.global_registrants.RegisterUser" id="addRegistrantModal" includeClose="true">
	<ui:form id="userSearchForm" action="addRegistrants" eventId="${event.id}" quickLookup="true">
		<input type="hidden" id="acrmUserId" name="acrmUserId" value="">
		<ui:panel>
			<div id="errMsg" class="display--none" style="color: red;">
				<c:if test="${event.advancedEvent}">
					<orbis:message code="i18n.global_eventDetailsBar.Pleaselook4468010560897863" />
				</c:if>
				<c:if test="${!event.advancedEvent}">
					<orbis:message code="i18n.global_eventDetailsBar.Pleaselook5593939741579018" />
				</c:if>
			</div>
			<p><orbis:message code="i18n.global_eventDetailsBar.Searchfora6370457347757329" /></p>
			<ui:textbox name="acrmUserName" id="acrmUserName" i18n_helpText="i18n.global_eventDetailsBar.Searchfora3562373282930907" aria-haspopup="true" aria-autocomplete="list"><orbis:message code="i18n.global_eventDetailsBar.Usertoregi3350315049991825" /></ui:textbox>

			<c:if test="${!event.advancedEvent}">
				<orbis:message code="i18n.common.orUPPERCASE" />
				<c:set var="bulkUsernamesTitle"><orbis:message code="i18n.global_eventDetailsBar.EnteraBUse0148453851828860" /></c:set>
				<ui:textarea name="bulkUsernames" id="bulkUsernames" i18n_title="${bulkUsernamesTitle}"></ui:textarea>
			</c:if>
			
			<c:if test="${event.advancedEvent}">
				<c:if test="${hasPublicRegTypes}">
					<ui:button id="publicButton" onclick="loadPublicRegTypes(false);">
						<orbis:message code="i18n.global_registrants.DisplayPub525134663824054" />
					</ui:button>
					<ui:button id="hidePublicButton" onclick="hidePublicRegTypes(false);" classes="display--none">
						<orbis:message code="i18n.global_eventDetailsBar.HidePublic3539498692812458" />
					</ui:button>
				</c:if>
				<div id="advancedRegisterPlaceholder"></div>
			</c:if>
		</ui:panel>
		<ui:button id="registerButton" disabled="${event.advancedEvent}" onclick="attachRegistrant();">
			<orbis:message code="i18n.global_registrants.Register" />
		</ui:button>
		<c:if test="${event.advancedEvent && hasPublicRegTypes}">
			<ui:button id="registerPublicButton" disabled="true" onclick="registerPublicRegistrant();">
				<orbis:message code="i18n.global_eventDetailsBar.RegisterPu5911518630623307" />
			</ui:button>
		</c:if>
	</ui:form>
</ui:modal>

<c:if test="${event.enableWaitingList}">
	<ui:modal i18n_title="i18n.global_waitingList.AddUserToWaitingList" id="addRegistrantToWaitingListModal" includeClose="true">
		<ui:form id="userSearchFormWaitingList" action="addRegistrantsToWaitingList" eventId="${event.id}" quickLookup="true">
			<input type="hidden" id="acrmUserIdWaitingList" name="acrmUserId" value="">
			<ui:panel>
				<div id="errMsgWaitingList" class="display--none" style="color: red;">
					<c:if test="${event.advancedEvent}">
						<orbis:message code="i18n.global_eventDetailsBar.Pleaselook4468010560897863" />
					</c:if>
					<c:if test="${!event.advancedEvent}">
						<orbis:message code="i18n.global_eventDetailsBar.Pleaselook5593939741579018" />
					</c:if>
				</div>
				<p><orbis:message code="i18n.global_eventDetailsBar.Searchfora9831608019198946" /></p>
				<ui:textbox name="acrmUserName" id="acrmUserNameWaitingList" i18n_helpText="i18n.global_eventDetailsBar.Searchfora3562373282930907" aria-haspopup="true" aria-autocomplete="list"><orbis:message code="i18n.global_eventDetailsBar.Usertoregi3350315049991825" /></ui:textbox>
	
				<c:if test="${!event.advancedEvent}">
					<orbis:message code="i18n.common.orUPPERCASE" />
					<c:set var="bulkUsernamesTitle"><orbis:message code="i18n.global_eventDetailsBar.EnteraBUse0148453851828860" /></c:set>
					<ui:textarea name="bulkUsernames" id="bulkUsernamesWaitingList" i18n_title="${bulkUsernamesTitle}"></ui:textarea>
				</c:if>
				
				<c:if test="${event.advancedEvent}">
					<c:if test="${hasWaitingListPublicRegTypes}">
						<ui:button id="waitingListPublicButton" onclick="loadPublicRegTypes(true);">
							<orbis:message code="i18n.global_waitingList.DisplayPub9342780637736792" />
						</ui:button>
						<ui:button id="waitingListHidePublicButton" onclick="hidePublicRegTypes(true);" classes="display--none">
							<orbis:message code="i18n.global_eventDetailsBar.HidePublic3539498692812458" />
						</ui:button>
					</c:if>
					<div id="advancedWaitingListRegisterPlaceholder"></div>
				</c:if>
			</ui:panel>
			<ui:button id="waitingListRegisterButton" disabled="${event.advancedEvent}" onclick="attachWaitingListRegistrant();">
				<orbis:message code="i18n.global_registrants.Register" />
			</ui:button>
			<c:if test="${event.advancedEvent && hasPublicRegTypes}">
				<ui:button id="waitingListRegisterPublicButton" classes="display--none" disabled="true" onclick="addWaitingListPublicRegistrant();">
					<orbis:message code="i18n.global_eventDetailsBar.RegisterPu5911518630623307" />
				</ui:button>
			</c:if>
		</ui:form>
	</ui:modal>
</c:if>

<c:if test="${event.invitationOnly}">
	<ui:modal i18n_title="i18n.global_eventDetailsBar.SendInvita6586446453899627" id="addInvitationModal" includeClose="true">
		<ui:form id="userSearchFormInvitation" action="addInvitations" eventId="${event.id}" quickLookup="true">
			<input type="hidden" id="acrmUserIdInvitation" name="acrmUserId" value="">
			<ui:panel>
				<div id="errMsgWaitingList" class="display--none" style="color: red;">
					<orbis:message code="i18n.global_eventDetailsBar.Pleaselook0894266792556610" />
				</div>
				<p><orbis:message code="i18n.global_eventDetailsBar.Searchfora2475820523273468" /></p>
				<ui:textbox name="acrmUserName" id="acrmUserNameInvitation" i18n_helpText="i18n.global_eventDetailsBar.Searchfora3562373282930907" aria-haspopup="true" aria-autocomplete="list"><orbis:message code="i18n.global_eventDetailsBar.Usertoinvi7926559275478423" /></ui:textbox>
	
				<c:if test="${!event.advancedEvent}">
					<orbis:message code="i18n.common.orUPPERCASE" />
					<c:set var="bulkUsernamesTitle"><orbis:message code="i18n.global_eventDetailsBar.EnteraBUse8559768616782256" /></c:set>
					<ui:textarea name="bulkUsernames" id="bulkUsernamesWaitingList" i18n_title="${bulkUsernamesTitle}"></ui:textarea>
				</c:if>
			</ui:panel>
			<ui:button id="sendInvitationsButton">
				<orbis:message code="i18n.global_eventDetailsBar.SendInvita0833056843428903" />
			</ui:button>
		</ui:form>
	</ui:modal>
</c:if>