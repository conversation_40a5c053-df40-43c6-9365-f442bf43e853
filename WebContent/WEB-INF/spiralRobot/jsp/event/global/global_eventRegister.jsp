<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted toolConversion="true" type="partial" />   --%>

<div>
	<c:if test="${not empty hasTenMinutesLeft}">
		<ui:panel>
			<strong>
				<span style="color: red"><orbis:message code="i18n.global_eventRegister.WARNING" />:</span> <orbis:message code="i18n.global_eventRegister.RegistrationClosesLessThanTen" />. 
			</strong>
		</ui:panel>
	</c:if>
	<c:if test="${not empty event.id}">
		<c:if test="${not empty registerError && !maxBookingsReached && empty conflictingRegistrations}">
			<ui:panel>
				<orbis:message code="i18n.global_eventRegister.strongWear4816193012746809" />
			</ui:panel>
		</c:if>
		<c:if test="${not empty registerError && deadlineReached}">
			<ui:panel>
				<orbis:message code="i18n.global_eventRegister.strongThed42004520908048804" />
			</ui:panel>
		</c:if>
		<c:if test="${not empty registration && empty registration.dateCanceled}">
			<ui:form id="cancelForm" action="cancel" gerId="${registration.id}" eventId="${event.id}"></ui:form>
			<div class="text-center">	
				<ui:note type="info">
					<orbis:message code="i18n.global_eventRegister.strongYouh3145157602181915" /><br><orbis:message code="i18n.global_eventRegister.DateRegistered" />: <fmt:formatDate value="${registration.dateRegistered}" pattern="${orbisDateAndTime}" />
				</ui:note>
			</div>
			<c:if test="${event.advancedEvent && not empty regTypeMap}">
				<ui:button size="large" action="displayViewAdvancedRegister" regId="${registration.id}"><orbis:message code="i18n.global_eventRegister.ViewAdvanc07214868747850167" /></ui:button>
				<c:if test="${registration.regType.status == 0}">
					<ui:button size="large" action="displayAdvancedRegister" regId="${registration.id}"><orbis:message code="i18n.global_eventRegister.EditAdvanc6078991103400531" /></ui:button>
				</c:if>
			</c:if>
			<c:if test="${event.advancedEvent && registration.regType.status == 0}">
				<ui:button size="large" action="displayAdvancedRegister" regId="${registration.id}"><orbis:message code="i18n.global_eventRegister.FinishRegistration" /></ui:button>
			</c:if>
			<c:if test="${canCancel && event.enableCancelation}">
				<ui:button size="large" onclick="$('#cancelForm').submit();"><orbis:message code="i18n.global_eventRegister.CancelRegistration" /></ui:button>
			</c:if>
		</c:if>
		<c:if test="${empty registration || not empty registration.dateCanceled}">
			<c:if test="${!event.advancedEvent}">
				<c:if test="${canRegister && empty limitReached && !event.waitlistOnly}">
					<c:if test="${isPreReg}">
						<script type="text/javascript">
						$(document).ready(function() {
							var ws = new WebSocket(
							"wss://${FQDN}/orbissockets/eventWaitlist/${event.id}/${currentUser.id}");
							
							$('.waitlistPendingLoading').loading();
						
							ws.onmessage = function(evt) {
								var msg = JSON.parse(evt.data);
								
								$('.waitlistPendingLoading').remove();
								
								orbisAppSr.buildForm({
									action:'<o:encrypt action="displayGlobalEventDetails" />', 
									eventId:'${event.id}'
								}).submit();
								
								ws.close();
							};
							
							window.onbeforeunload = function(e) {
							  ws.close();
							};
						});
						</script>
						<div class="text-center waitlistPendingDiv">
							<ui:note type="warning">
								<strong><orbis:message code="i18n.global_eventRegister.ThanksRegistrationBeingProcessed" />
								</strong>
							</ui:note>
							<div class="waitlistPendingLoading"></div>
						</div>
					</c:if>
					<c:if test="${!isPreReg}">
						<c:if test="${empty event.tagPre || tagCount > 0}">
							<ui:button size="large" action="register" classes="clickGuard" eventId="${event.id}"><orbis:message code="i18n.global_eventRegister.registerForEvent" /></ui:button>
						</c:if>
						<c:if test="${not empty event.tagPre && tagCount == 0 }">
							<orbis:message code="i18n.global_eventRegister.Thiseventr8013454498050062" />
						</c:if>
					</c:if>
				</c:if>
				<c:if test="${!canRegister || event.waitlistOnly}">
					<c:if test="${!event.enableWaitingList || not empty limitReached || (!canWaitingList && !hasWaitingListRecord)}">
						<ui:note type="warning">
							<c:if test="${empty limitReached}">
								<orbis:message code="i18n.global_eventRegister.strongNosp6751179962472509" />
							</c:if>
							<c:if test="${not empty limitReached}">
								<orbis:message code="i18n.global_eventRegister.Youhaverea4606876287271060" />
							</c:if>
						</ui:note>
					</c:if>
					<c:if test="${event.enableWaitingList}">
						<c:if test="${hasWaitingListRecord}">
							<div class="i18nMsg">
								<orbis:message code="i18n.global_eventRegister.8192806985468737" />
							</div>
							<ui:button size="large" action="removeFromWaitingList" classes="clickGuard" eventId="${event.id}"><orbis:message code="i18n.global_eventRegister.Removemefr9681211186376195" /></ui:button>
						</c:if>
						<c:if test="${event.enableWaitingList && canWaitingList}">
							<ui:button size="large" action="addToWaitingList" classes="clickGuard" eventId="${event.id}"><orbis:message code="i18n.global_eventRegister.Registeron21730350563961454" /></ui:button>
						</c:if>
					</c:if>
				</c:if>
			</c:if>
			<c:if test="${event.advancedEvent}">
				<c:if test="${!canRegister && !event.enableWaitingList}">
					<ui:note type="warning">
						<orbis:message code="i18n.global_eventRegister.noSpacesOrRegistrationClosed" />
					</ui:note>
				</c:if>
				<c:if test="${canRegister || event.enableWaitingList}">
					<c:choose>
						<c:when test="${isPreReg}">
							<div class="text-center">
								<ui:note type="warning">
									<strong><orbis:message code="i18n.global_eventRegister.ThanksRegisteringWillReceiveEmail" /> </strong>
								</ui:note>
							</div>
						</c:when>
						<c:otherwise>
							<c:if test="${hasWaitingListRecord}">
								<div class="i18nMsg">
									<orbis:message code="i18n.global_eventRegister.8192806985468737" />
								</div>
								<o:nav anchor="true" anchorClass="btn btn-primary btn-block btn-large clickGuard" action="removeFromWaitingList" eventId="${event.id}">
									<orbis:message code="i18n.global_eventRegister.Removemefr9681211186376195" />
								</o:nav>
							</c:if>
							<c:if test="${!hasWaitingListRecord}">
								<c:if test="${empty event.tagPre || (not empty event.tagPre && tagCount > 0) }">
									<%@ include file="/WEB-INF/spiralRobot/jsp/event/event_advancedRegistrationTypesModal.jsp"%>
								</c:if>
								<c:if test="${not empty event.tagPre && tagCount == 0 }">
									<orbis:message code="i18n.global_eventRegister.Thiseventr8013454498050062" />
								</c:if>
							</c:if>
						</c:otherwise>
					</c:choose>
				</c:if>
			</c:if>
		</c:if>
	</c:if>
	<c:if test="${empty event.id}">
		<orbis:message code="i18n.global_eventRegister.pleaseSaveEventFirst" />
	</c:if>
</div>