<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%-- <ui:isConverted type="complete" />   --%>
<orbis:addComponent component="orbisNotePopups" />

<script type="text/javascript">
	var controllerPath = "${siteElement.fullPath}.htm";
	
	$(document).ready(function(){
		if($("textarea[name='wlBody']").length > 0)
		{
			//$("textarea[name='wlBody']").orbisEditor(false, "richNoUpload");
			$("textarea[name='wlBody']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.wlBody}`));
		}
		if($("textarea[name='wlBodyL2']").length > 0)
		{
			//$("textarea[name='wlBodyL2']").orbisEditor(false, "richNoUpload");
			$("textarea[name='wlBodyL2']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.wlBodyL2}`));
		}
		if($("textarea[name='eventRemBodyL2']").length > 0)
		{
			//$("textarea[name='eventRemBodyL2']").orbisEditor(false, "richNoUpload");
			$("textarea[name='eventRemBodyL2']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.eventRemBodyL2}`));
		}
		if($("textarea[name='eventRemBody']").length > 0)
		{
			//$("textarea[name='eventRemBody']").orbisEditor(false, "richNoUpload");
			$("textarea[name='eventRemBody']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.eventRemBody}`));
		}
		if($("textarea[name='prBody']").length > 0)
		{
			//$("textarea[name='prBody']").orbisEditor(false, "richNoUpload");
			$("textarea[name='prBody']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.prBody}`));
		}
		if($("textarea[name='prBodyL2']").length > 0)
		{
			//$("textarea[name='prBodyL2']").orbisEditor(false, "richNoUpload");
			$("textarea[name='prBodyL2']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.prBodyL2}`));
		}
		if($("textarea[name='cancelConfirmEmailBodyL2']").length > 0)
		{
			//$("textarea[name='cancelConfirmEmailBodyL2']").orbisEditor(false, "richNoUpload");
			$("textarea[name='cancelConfirmEmailBodyL2']").val(`${siteElement.contentItem.cancelConfirmEmailBodyL2}`);
		}
		if($("textarea[name='cancelConfirmEmailBody']").length > 0)
		{
			//$("textarea[name='cancelConfirmEmailBody']").orbisEditor(false, "richNoUpload");
			$("textarea[name='cancelConfirmEmailBody']").val(`${siteElement.contentItem.cancelConfirmEmailBody}`);
		}
		if($("textarea[name='registrationConfirmEmailBodyL2']").length > 0)
		{
			//$("textarea[name='registrationConfirmEmailBodyL2']").orbisEditor(false, "richNoUpload");
			$("textarea[name='registrationConfirmEmailBodyL2']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.registrationConfirmEmailBodyL2}`));
		}
		if($("textarea[name='registrationConfirmEmailBody']").length > 0)
		{
			//$("textarea[name='registrationConfirmEmailBody']").orbisEditor(false, "richNoUpload");
			$("textarea[name='registrationConfirmEmailBody']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.registrationConfirmEmailBody}`));
		}
		if($("textarea[name='waitListConfirmEmailBodyL2']").length > 0)
		{
			//$("textarea[name='waitListConfirmEmailBodyL2']").orbisEditor(false, "richNoUpload");
			$("textarea[name='waitListConfirmEmailBodyL2']").val(`${siteElement.contentItem.waitListConfirmEmailBodyL2}`);
		}
		if($("textarea[name='waitListConfirmEmailBody']").length > 0)
		{
			//$("textarea[name='waitListConfirmEmailBody']").orbisEditor(false, "richNoUpload");
			$("textarea[name='waitListConfirmEmailBody']").val(`${siteElement.contentItem.waitListConfirmEmailBody}`);
		}
		if($("textarea[name='uncancelConfirmEmailBody']").length > 0)
		{
			//$("textarea[name='uncancelConfirmEmailBody']").orbisEditor(false, "richNoUpload");
			$("textarea[name='uncancelConfirmEmailBody']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.uncancelConfirmEmailBody}`));
		}
		if($("textarea[name='uncancelConfirmEmailBodyL2']").length > 0)
		{
			//$("textarea[name='uncancelConfirmEmailBodyL2']").orbisEditor(false, "richNoUpload");
			$("textarea[name='uncancelConfirmEmailBodyL2']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.uncancelConfirmEmailBodyL2}`));
		}
		if($("textarea[name='failedRegistrationEmailBody']").length > 0)
		{
			//$("textarea[name='failedRegistrationEmailBody']").orbisEditor(false, "richNoUpload");
			$("textarea[name='failedRegistrationEmailBody']").val(`${siteElement.contentItem.failedRegistrationEmailBody}`);
		}
		if($("textarea[name='failedRegistrationEmailBodyL2']").length > 0)
		{
			//$("textarea[name='failedRegistrationEmailBodyL2']").orbisEditor(false, "richNoUpload");
			$("textarea[name='failedRegistrationEmailBodyL2']").val(`${siteElement.contentItem.failedRegistrationEmailBodyL2}`);
		}	
		if($("textarea[name='videoChatEmailBody']").length > 0)
		{
			//$("textarea[name='videoChatEmailSubject']").orbisEditor(false, "richNoUpload");
			$("textarea[name='videoChatEmailBody']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.videoChatEmailBody}`));
		}
		if($("textarea[name='videoChatEmailBody']").length > 0)
		{
			//$("textarea[name='videoChatEmailSubjectL2']").orbisEditor(false, "richNoUpload");
			$("textarea[name='videoChatEmailBodyL2']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.videoChatEmailBodyL2}`));
		}	
		if($("textarea[name='registrationConfirmEmailBodyToAdmin']").length > 0)
		{
			//$("textarea[name='registrationConfirmEmailBodyToAdmin']").orbisEditor(false, "richNoUpload");
			$("textarea[name='registrationConfirmEmailBodyToAdmin']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.registrationConfirmEmailBodyToAdmin}`));
		}
		if($("textarea[name='registrationConfirmEmailBodyToAdminL2']").length > 0)
		{
			//$("textarea[name='registrationConfirmEmailBodyToAdminL2']").orbisEditor(false, "richNoUpload");
			$("textarea[name='registrationConfirmEmailBodyToAdminL2']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.registrationConfirmEmailBodyToAdminL2}`));
		}
		
		if(${siteCode == 'waterloo'})
		{	
			if($("textarea[name='eventOwnerConfirmBody']").length > 0)
			{
	// 			$("textarea[name='eventOwnerConfirmBody']").orbisEditor(false, "richNoUpload");
				$("textarea[name='eventOwnerConfirmBody']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.eventOwnerConfirmBody}`));
			}
			if($("textarea[name='eventOwnerConfirmBodyL2']").length > 0)
			{
	// 			$("textarea[name='eventOwnerConfirmBodyL2']").orbisEditor(false, "richNoUpload");
				$("textarea[name='eventOwnerConfirmBodyL2']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.eventOwnerConfirmBodyL2}`));
			}
			if($("textarea[name='eventOwnerReminderBody']").length > 0)
			{
	// 			$("textarea[name='eventOwnerReminderBody']").orbisEditor(false, toolbar: "richNoUpload");
				$("textarea[name='eventOwnerReminderBody']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.eventOwnerReminderBody}`));
			}
			if($("textarea[name='eventOwnerReminderBodyL2']").length > 0)
			{
	// 			$("textarea[name='eventOwnerReminderBodyL2']").orbisEditor(false, toolbar: "richNoUpload");
				$("textarea[name='eventOwnerReminderBodyL2']").val(replaceTokensWithOppositeLocale(`${siteElement.contentItem.eventOwnerReminderBodyL2}`));
			}
		}

		$("a.deleteEmailAttachment").click(deleteEmailAttachment);

		setIds();
	});
		
		$("a.deleteEmailAttachment").click(deleteEmailAttachment);

		setIds();
	});

	function setIds() {
		var tokensPlacements = $(".tokensSpace");
		$.each(tokensPlacements, function (index) {
			tokensPlacements[index].id = "tokens_list_" + index;
		});

		var emailBodies = $(".emailBodyPlace");
		$.each(emailBodies, function (index) {
			emailBodies[index].id = "email_body_" + index;
		});
	}

	function replaceTokensWithOppositeLocale(text) {
		var allowedTokens = ${emailTokens};
		var oppositeLocaleTokens = ${oppositeLocaleEmailTokens};
		if (allowedTokens && oppositeLocaleTokens) {
			$.each(oppositeLocaleTokens, function (index) {
				text = text.replaceAll("&#39;", "'");
				if (text.includes(this)) {
					text = text.replaceAll(this, allowedTokens[index]);
				}
			});
		}
		return text;
	}

	function deleteEmailAttachment()
	{
		var $this = $(this);
		var request = {
			action : '<o:encrypt action="deleteDefaultAttachment" />',
			email : $(this).data("email"),
			rand : Math.floor(Math.random() * 100000)
		};

		$.post(controllerPath, request, function(data, status, xhr) {
			if (orbisAppSr.checkAjaxResponse(xhr)) {
				$this.parents(".currentDefaultEmailAttachment").remove();
			}
		}, "json");
	}

</script>

<ui:navBack>
	<ui:navBackItem action="displayHome"><orbis:message code="i18n.global_emailOptionsConfig.Home7985004774905317" /></ui:navBackItem>
	<ui:navBackItem action="displayHome_moduleConfig"><orbis:message code="i18n.global_emailOptionsConfig.ModuleConf9555040099945124" /></ui:navBackItem>
</ui:navBack>

<ui:header><orbis:message code="i18n.global_emailOptionsConfig.ModuleEmailOptions" /></ui:header>

<ui:formPage formId="emailOptionsConfigForm" action="saveEmailOptionsConfig" cancelAction="displayHome_moduleConfig">
	<c:set var="uploadDirectory" value="/content/documents/globalEvents/" />
	<ui:formGroup id="eventReminder" i18n_title="i18n.global_moduleEdit.eventReminderEmail24Hour">
		<ui:textbox classes="margin--t--xl" id="eventRemEmailFrom" name="eventRemEmailFrom" value="${siteElement.contentItem.eventRemEmailFrom}"><orbis:message code="i18n.common.email.fromEmail" /></ui:textbox>

		<div class="margin--b--l label"><orbis:message code="i18n.common.email.subject" /></div>
		<ui:textbox id="eventRemSubject" name="eventRemSubject" value="${siteElement.contentItem.eventRemSubject}">${langLabelL1}</ui:textbox>
		<ui:textbox id="eventRemSubjectL2" name="eventRemSubjectL2" value="${siteElement.contentItem.eventRemSubjectL2}" classes="${isBilingualModeOn ? '' : 'display--none'}">${langLabelL2}</ui:textbox>

		<div class="margin--b--l label"><orbis:message code="i18n.global_emailOptionsConfig.Tokensforo9836840510335002" /></div>
		<ui:note classes="tokensSpace"></ui:note>

		<div class="label"><orbis:message code="i18n.common.email.body" /></div>
		<ui:textarea type="rich" i18n_title="${langLabelL1}" id="eventRemBody" name="eventRemBody" classes="emailBodyPlace"></ui:textarea>
		<c:if test="${isBilingualModeOn}">
			<ui:textarea type="rich" i18n_title="${langLabelL2}" id="eventRemBodyL2" name="eventRemBodyL2" classes="emailBodyPlace"></ui:textarea>
		</c:if>
		<ui:note><orbis:message code="i18n.global_moduleEdit.eventDetailsAppendedAutomatically" /></ui:note>
		<c:set var="filePath" value="${siteElement.contentItem.eventReminderAttachment.existingFilePath}" />
		<c:set var="fileName" value="${not empty filePath ? o:getReadableFileName(filePath) : ''}" />
		<ui:checkbox name="includeCancelLink" checked="${siteElement.contentItem.includeCancellationLink}"><spring:message code="i18n.global_emailOptionsConfig.Includecan6842398267435987" /></ui:checkbox>
	</ui:formGroup>
	<c:set var="waitListRegistrationTitle"><orbis:message code="i18n.global_moduleEdit.WaitingListPromotedEmail" />
			<ui:button size="small" classes="plain position--relative protip" data-pt-classes="tip--default" data-pt-title="i18n.global_emailOptionsConfig.Thisemaili06366285160177654"><i class="material-icons">help</i></ui:button>
	</c:set>
	<ui:formGroup id="waitListRegistration" i18n_title="${waitListRegistrationTitle}">
		<ui:textbox classes="margin--t--xl" id="wlFrom" name="wlFrom" value="${siteElement.contentItem.wlFrom}"><orbis:message code="i18n.common.email.fromEmail" /></ui:textbox>

		<div class="margin--b--l label"><orbis:message code="i18n.common.email.subject" /></div>
		<ui:textbox id="wlSubject" name="wlSubject" value="${siteElement.contentItem.wlSubject}">${langLabelL1}</ui:textbox>
		<ui:textbox id="wlSubjectL2" name="wlSubjectL2" value="${siteElement.contentItem.wlSubjectL2}" classes="${isBilingualModeOn ? '' : 'display--none'}">${langLabelL2}</ui:textbox>

		<div class="margin--b--l label"><orbis:message code="i18n.global_emailOptionsConfig.Tokensforo9836840510335002" /></div>
		<ui:note classes="tokensSpace"></ui:note>

		<div class="label"><orbis:message code="i18n.common.email.body" /></div>
		<ui:textarea type="rich" i18n_title="${langLabelL1}" id="wlBody" name="wlBody" classes="emailBodyPlace"></ui:textarea>
		<c:if test="${isBilingualModeOn}">
			<ui:textarea type="rich" i18n_title="${langLabelL2}" id="wlBodyL2" name="wlBodyL2" classes="emailBodyPlace"></ui:textarea>
		</c:if>
		<span class="label margin--b--s"><orbis:message code="i18n.global_moduleEdit.eventDetailsAppendedAutomatically" /></span>	
	</ui:formGroup>
	<c:if test="${siteElement.contentItem.enableAdvancedEvents}">
	<ui:formGroup id="publicRegistrationConfirmation" i18n_title="i18n.global_emailOptionsConfig.PublicRegi11920834315274864">
		<ui:textbox classes="margin--t--xl" id="prFrom" name="prFrom" value="${siteElement.contentItem.prFrom}"><orbis:message code="i18n.common.email.fromEmail" /></ui:textbox>

		<div class="margin--b--l label"><orbis:message code="i18n.common.email.subject" /></div>
		<ui:textbox id="prSubject" name="prSubject" value="${siteElement.contentItem.prSubject}">${langLabelL1}</ui:textbox>
		<ui:textbox id="prSubjectL2" name="prSubjectL2" value="${siteElement.contentItem.prSubjectL2}" classes="${isBilingualModeOn ? '' : 'display--none'}">${langLabelL2}</ui:textbox>

		<div class="margin--b--l label"><orbis:message code="i18n.global_emailOptionsConfig.Tokensforo9836840510335002" /></div>
		<ui:note classes="tokensSpace"></ui:note>

		<div class="label"><orbis:message code="i18n.common.email.body" /></div>
		<ui:textarea type="rich" i18n_title="${langLabelL1}" id="prBody" name="prBody" classes="emailBodyPlace"></ui:textarea>
		<c:if test="${isBilingualModeOn}">
			<ui:textarea type="rich" i18n_title="${langLabelL2}" id="prBodyL2" name="prBodyL2" classes="emailBodyPlace"></ui:textarea>
		</c:if>
		<span class="label margin--b--s"><orbis:message code="i18n.global_moduleEdit.eventDetailsAppendedAutomatically" /></span>
	</ui:formGroup>
	</c:if>
	<ui:formGroup id="registrationConfirmation" i18n_title="i18n.global_emailOptionsConfig.Registrati16728092513785653">
		<ui:textbox classes="margin--t--xl" id="registrationConfirmEmailFrom" name="registrationConfirmEmailFrom" value="${siteElement.contentItem.registrationConfirmEmailFrom}"><orbis:message code="i18n.common.email.fromEmail" /></ui:textbox>

		<div class="margin--b--l label"><orbis:message code="i18n.common.email.subject" /></div>
		<ui:textbox id="registrationConfirmEmailSubject" name="registrationConfirmEmailSubject" value="${siteElement.contentItem.registrationConfirmEmailSubject}">${langLabelL1}</ui:textbox>
		<ui:textbox id="registrationConfirmEmailSubjectL2" name="registrationConfirmEmailSubjectL2" value="${siteElement.contentItem.registrationConfirmEmailSubjectL2}" classes="${isBilingualModeOn ? '' : 'display--none'}">${langLabelL2}</ui:textbox>

		<div class="margin--b--l label"><orbis:message code="i18n.global_emailOptionsConfig.Tokensforo9836840510335002" /></div>
		<ui:note classes="tokensSpace"></ui:note>

		<div class="label"><orbis:message code="i18n.common.email.body" /></div>
		<ui:textarea type="rich" i18n_title="${langLabelL1}" id="registrationConfirmEmailBody" name="registrationConfirmEmailBody" classes="emailBodyPlace"></ui:textarea>
		<c:if test="${isBilingualModeOn}">
			<ui:textarea type="rich" i18n_title="${langLabelL2}" id="registrationConfirmEmailBodyL2" name="registrationConfirmEmailBodyL2" classes="emailBodyPlace"></ui:textarea>
		</c:if>
		<span class="label margin--b--s"><orbis:message code="i18n.global_moduleEdit.eventDetailsAppendedAutomatically" /></span>
	</ui:formGroup>
	
	<ui:formGroup id="registrationEmailToAdminForm" i18n_title="i18n.global_emailOptionsConfig.Registrati0157530345859478">

		<ui:textbox classes="margin--t--xl" id="registrationConfirmEmailFromToAdmin" name="registrationConfirmEmailFromToAdmin" value="${siteElement.contentItem.registrationConfirmEmailFromToAdmin}"><orbis:message code="i18n.common.email.fromEmail" /></ui:textbox>
		
		<div class="margin--b--l label"><orbis:message code="i18n.common.email.subject" /></div>
		<ui:textbox id="registrationConfirmEmailSubjectToAdmin" name="registrationConfirmEmailSubjectToAdmin" value="${siteElement.contentItem.registrationConfirmEmailSubjectToAdmin}">${langLabelL1}</ui:textbox>
		<ui:textbox id="registrationConfirmEmailSubjectToAdminL2" name="registrationConfirmEmailSubjectToAdminL2" value="${siteElement.contentItem.registrationConfirmEmailSubjectToAdminL2}" classes="${isBilingualModeOn ? '' : 'display--none'}">${langLabelL2}</ui:textbox>

		<div class="margin--b--l label"><orbis:message code="i18n.global_emailOptionsConfig.Tokensforo9836840510335002" /></div>
		<ui:note classes="tokensSpace"></ui:note>

		<div class="label"><orbis:message code="i18n.common.email.body" /></div>
		<ui:textarea type="rich" i18n_title="${langLabelL1}" id="registrationConfirmEmailBodyToAdmin" name="registrationConfirmEmailBodyToAdmin" classes="emailBodyPlace"></ui:textarea>
		<c:if test="${isBilingualModeOn}">
			<ui:textarea type="rich" i18n_title="${langLabelL2}" id="registrationConfirmEmailBodyToAdminL2" name="registrationConfirmEmailBodyToAdminL2" classes="emailBodyPlace"></ui:textarea>
		</c:if>
		<span class="label margin--b--s"><orbis:message code="i18n.global_moduleEdit.eventDetailsAppendedAutomatically" /></span>
	</ui:formGroup>
	
	<ui:formGroup id="cancelConfirmation" i18n_title="i18n.global_emailOptionsConfig.Cancellati4967520263033537">
		<ui:textbox classes="margin--t--xl" id="cancelConfirmEmailFrom" name="cancelConfirmEmailFrom" value="${siteElement.contentItem.cancelConfirmEmailFrom}"><orbis:message code="i18n.common.email.fromEmail" /></ui:textbox>
		<div class="margin--b--l label"><orbis:message code="i18n.common.email.subject" /></div>
		<ui:textbox id="cancelConfirmEmailSubject" name="cancelConfirmEmailSubject" value="${siteElement.contentItem.cancelConfirmEmailSubject}">${langLabelL1}</ui:textbox>
		<ui:textbox id="cancelConfirmEmailSubjectL2" name="cancelConfirmEmailSubjectL2" value="${siteElement.contentItem.cancelConfirmEmailSubjectL2}" classes="${isBilingualModeOn ? '' : 'display--none'}">${langLabelL2}</ui:textbox>

		<div class="margin--b--l label" style="display: none"><orbis:message code="i18n.global_emailOptionsConfig.Tokensforo9836840510335002" /></div>
		<ui:note classes="tokensSpace" css="display: none !important"></ui:note>

		<div class="label"><orbis:message code="i18n.common.email.body" /></div>
		<ui:textarea type="rich" i18n_title="${langLabelL1}" id="cancelConfirmEmailBody" name="cancelConfirmEmailBody" classes="emailBodyPlace"></ui:textarea>
		<c:if test="${isBilingualModeOn}">
			<ui:textarea type="rich" i18n_title="${langLabelL2}" id="cancelConfirmEmailBodyL2" name="cancelConfirmEmailBodyL2" classes="emailBodyPlace"></ui:textarea>
		</c:if>
		<span class="label margin--b--s"><orbis:message code="i18n.global_moduleEdit.eventDetailsAppendedAutomatically" /></span>
	</ui:formGroup>
	
	<ui:formGroup id="uncancelConfirmEmail" i18n_title="i18n.global_emailOptionsConfig.UncancellationConfirmationEmail">
		<ui:textbox classes="margin--t--xl" id="uncancelConfirmEmailFrom" name="uncancelConfirmEmailFrom" value="${siteElement.contentItem.uncancelConfirmEmailFrom}"><orbis:message code="i18n.common.email.fromEmail" /></ui:textbox>
		
		<div class="margin--b--l label"><orbis:message code="i18n.common.email.subject" /></div>
		<ui:textbox id="uncancelConfirmEmailSubject" name="uncancelConfirmEmailSubject" value="${siteElement.contentItem.uncancelConfirmEmailSubject}">${langLabelL1}</ui:textbox>
		<ui:textbox id="uncancelConfirmEmailSubjectL2" name="uncancelConfirmEmailSubjectL2" value="${siteElement.contentItem.uncancelConfirmEmailSubjectL2}" classes="${isBilingualModeOn ? '' : 'display--none'}">${langLabelL2}</ui:textbox>

		<div class="margin--b--l label"><orbis:message code="i18n.global_emailOptionsConfig.Tokensforo9836840510335002" /></div>
		<ui:note classes="tokensSpace"></ui:note>

		<div class="label"><orbis:message code="i18n.common.email.body" /></div>
		<ui:textarea type="rich" i18n_title="${langLabelL1}" id="uncancelConfirmEmailBody" name="uncancelConfirmEmailBody" classes="emailBodyPlace"></ui:textarea>
		<c:if test="${isBilingualModeOn}">
			<ui:textarea type="rich" i18n_title="${langLabelL2}" id="uncancelConfirmEmailBodyL2" name="uncancelConfirmEmailBodyL2" classes="emailBodyPlace"></ui:textarea>
		</c:if>
		<span class="label margin--b--s"><orbis:message code="i18n.global_moduleEdit.eventDetailsAppendedAutomatically" /></span>
	</ui:formGroup>
	
	<ui:formGroup id="waitListConfirmation" i18n_title="i18n.global_emailOptionsConfig.WaitListCo1677837192030346">

		<ui:textbox classes="margin--t--xl" id="waitListConfirmEmailFrom" name="waitListConfirmEmailFrom" value="${siteElement.contentItem.waitListConfirmEmailFrom}"><orbis:message code="i18n.common.email.fromEmail" /></ui:textbox>
		
		<div class="margin--b--l label"><orbis:message code="i18n.common.email.subject" /></div>
		<ui:textbox id="waitListConfirmEmailSubject" name="waitListConfirmEmailSubject" value="${siteElement.contentItem.waitListConfirmEmailSubject}">${langLabelL1}</ui:textbox>
		<ui:textbox id="waitListConfirmEmailSubjectL2" name="waitListConfirmEmailSubjectL2" value="${siteElement.contentItem.waitListConfirmEmailSubjectL2}" classes="${isBilingualModeOn ? '' : 'display--none'}">${langLabelL2}</ui:textbox>

		<div class="margin--b--l label" style="display: none"><orbis:message code="i18n.global_emailOptionsConfig.Tokensforo9836840510335002" /></div>
		<ui:note classes="tokensSpace" css="display: none !important"></ui:note>

		<div class="label"><orbis:message code="i18n.common.email.body" /></div>
		<ui:textarea type="rich" i18n_title="${langLabelL1}" id="waitListConfirmEmailBody" name="waitListConfirmEmailBody" classes="emailBodyPlace"></ui:textarea>
		<c:if test="${isBilingualModeOn}">
			<ui:textarea type="rich" i18n_title="${langLabelL2}" id="waitListConfirmEmailBodyL2" name="waitListConfirmEmailBodyL2" classes="emailBodyPlace"></ui:textarea>
		</c:if>
		<span class="label margin--b--s"><orbis:message code="i18n.global_moduleEdit.eventDetailsAppendedAutomatically" /></span>
	</ui:formGroup>
	
	<ui:formGroup id="failedRegistration" i18n_title="i18n.global_emailOptionsConfig.FailedRegi6180296801106615">

		<ui:textbox classes="margin--t--xl" id="failedRegistrationEmailFrom" name="failedRegistrationEmailFrom" value="${siteElement.contentItem.failedRegistrationEmailFrom}"><orbis:message code="i18n.common.email.fromEmail" /></ui:textbox>
		
		<div class="margin--b--l label"><orbis:message code="i18n.common.email.subject" /></div>
		<ui:textbox id="failedRegistrationEmailSubject" name="failedRegistrationEmailSubject" value="${siteElement.contentItem.failedRegistrationEmailSubject}">${langLabelL1}</ui:textbox>
		<ui:textbox id="failedRegistrationEmailSubjectL2" name="failedRegistrationEmailSubjectL2" value="${siteElement.contentItem.failedRegistrationEmailSubjectL2}" classes="${isBilingualModeOn ? '' : 'display--none'}">${langLabelL2}</ui:textbox>

		<div class="margin--b--l label" style="display: none"><orbis:message code="i18n.global_emailOptionsConfig.Tokensforo9836840510335002" /></div>
		<ui:note classes="tokensSpace" css="display: none !important"></ui:note>

		<div class="label"><orbis:message code="i18n.common.email.body" /></div>
		<ui:textarea type="rich" i18n_title="${langLabelL1}" id="failedRegistrationEmailBody" name="failedRegistrationEmailBody" classes="emailBodyPlace"></ui:textarea>
		<c:if test="${isBilingualModeOn}">
			<ui:textarea type="rich" i18n_title="${langLabelL2}" id="failedRegistrationEmailBodyL2" name="failedRegistrationEmailBodyL2" classes="emailBodyPlace"></ui:textarea>
		</c:if>
		
		<ui:note><orbis:message code="i18n.global_moduleEdit.eventDetailsAppendedAutomatically" /></ui:note>
		<c:set var="filePath" value="${siteElement.contentItem.failedRegistrationEmailAttachment.existingFilePath}" />
		<c:set var="fileName" value="${not empty filePath ? o:getReadableFileName(filePath) : ''}" />
	</ui:formGroup>
				
	<ui:formGroup id="builtInVideoChat" i18n_title="i18n.global_emailOptionsConfig.BuiltinVid3418713829462942">

		<ui:textbox classes="margin--t--xl" id="videoChatEmailFrom" name="videoChatEmailFrom" value="${siteElement.contentItem.videoChatEmailFrom}"><orbis:message code="i18n.common.email.fromEmail" /></ui:textbox>
		
		<div class="margin--b--l label"><orbis:message code="i18n.common.email.subject" /></div>
		<ui:textbox id="videoChatEmailSubject" name="videoChatEmailSubject" value="${siteElement.contentItem.videoChatEmailSubject}">${langLabelL1}</ui:textbox>
		<ui:textbox id="videoChatEmailSubjectL2" name="videoChatEmailSubjectL2" value="${siteElement.contentItem.videoChatEmailSubjectL2}" classes="${isBilingualModeOn ? '' : 'display--none'}">${langLabelL2}</ui:textbox>

		<div class="margin--b--l label"><orbis:message code="i18n.global_emailOptionsConfig.Tokensforo9836840510335002" /></div>
		<ui:note classes="tokensSpace"></ui:note>

		<div class="label"><orbis:message code="i18n.common.email.body" /></div>
		<ui:textarea type="rich" i18n_title="${langLabelL1}" id="videoChatEmailBody" name="videoChatEmailBody" classes="emailBodyPlace"></ui:textarea>
		<c:if test="${isBilingualModeOn}">
			<ui:textarea type="rich" i18n_title="${langLabelL2}" id="videoChatEmailBodyL2" name="videoChatEmailBodyL2" classes="emailBodyPlace"></ui:textarea>
		</c:if>
		<span class="label margin--b--s"><orbis:message code="i18n.global_moduleEdit.eventDetailsAppendedAutomatically" /></span>
	</ui:formGroup>

	<c:if test="${'waterloo' == siteCode}">
		<ui:formGroup id="eventShowEventOwnerOpts" i18n_title="i18n.global_emailOptionsConfig.EventOwner1139181350696879">
			<ui:checkbox name="showEventOwnerOpts" checked="${siteElement.contentItem.showEventOwnerDetails}">
				<orbis:message code="i18n.global_emailOptionsConfig.Hideemailt9866509375651826" />
			</ui:checkbox>
			<ui:checkbox name="eventOwnerDetailsRequired" checked="${siteElement.contentItem.eventOwnerDetailsRequired}">
				<orbis:message code="i18n.global_emailOptionsConfig.EventDetai4631727432756361" />
			</ui:checkbox>
			
			<div class="label"><orbis:message code="i18n.global_emailOptionsConfig.Daystosend2925858503783883" /></div>
			<ui:textbox id="eventReminderDaysToEmailEventOwner" name="eventReminderDaysToEmailEventOwner" class="required number input-small" data-min="1" data-max="30"
				value="${siteElement.contentItem.eventReminderDaysToEmailEventOwner}"/>
			</ui:textbox>
		</ui:formGroup>

		<ui:formGroup id="eventOwnerConfirm" i18n_title="i18n.global_emailOptionsConfig.EventOwner0730984391197142">

			<ui:textbox classes="margin--t--xl" id="eventOwnerConfirmEmailFrom" name="eventOwnerConfirmEmailFrom" value="${siteElement.contentItem.eventOwnerConfirmEmailFrom}"><orbis:message code="i18n.common.email.fromEmail" /></ui:textbox>
		
			<div class="margin--b--l label"><orbis:message code="i18n.common.email.subject" /></div>
			<ui:textbox id="eventOwnerConfirmSubject" name="eventOwnerConfirmSubject" value="${siteElement.contentItem.eventOwnerConfirmSubject}">${langLabelL1}</ui:textbox>
			<ui:textbox id="eventOwnerConfirmSubjectL2" name="eventOwnerConfirmSubjectL2" value="${siteElement.contentItem.eventOwnerConfirmSubjectL2}" classes="${isBilingualModeOn ? '' : 'display--none'}">${langLabelL2}</ui:textbox>

			<div class="label"><orbis:message code="i18n.common.email.body" /></div>
			<ui:textarea type="rich" i18n_title="${langLabelL1}" id="eventOwnerConfirmBody" name="eventOwnerConfirmBody" classes="emailBodyPlace"></ui:textarea>
			<c:if test="${isBilingualModeOn}">
				<ui:textarea type="rich" i18n_title="${langLabelL2}" id="eventOwnerConfirmBodyBodyL2" name="eventOwnerConfirmBodyL2" classes="emailBodyPlace"></ui:textarea>
			</c:if>
			<ui:note><orbis:message code="i18n.global_moduleEdit.eventDetailsAppendedAutomatically" /></ui:note>
		</ui:formGroup>	
		
		<ui:formGroup id="eventOwnerReminder" i18n_title="i18n.global_emailOptionsConfig.EventOwner8128264705001891">
			<ui:textbox classes="margin--t--xl" id="eventOwnerReminderEmailFrom" name="eventOwnerReminderEmailFrom" value="${siteElement.contentItem.eventOwnerReminderEmailFrom}"><orbis:message code="i18n.common.email.fromEmail" /></ui:textbox>
		
			<div class="margin--b--l label"><orbis:message code="i18n.common.email.subject" /></div>
			<ui:textbox id="eventOwnerReminderSubject" name="eventOwnerReminderSubject" value="${siteElement.contentItem.eventOwnerReminderSubject}">${langLabelL1}</ui:textbox>
			<ui:textbox id="eventOwnerReminderSubjectL2" name="eventOwnerReminderSubjectL2" value="${siteElement.contentItem.eventOwnerReminderSubjectL2}" classes="${isBilingualModeOn ? '' : 'display--none'}">${langLabelL2}</ui:textbox>

			<div class="label"><orbis:message code="i18n.common.email.body" /></div>
			<ui:textarea type="rich" i18n_title="${langLabelL1}" id="eventOwnerReminderBody" name="eventOwnerReminderBody" classes="emailBodyPlace"></ui:textarea>
			<c:if test="${isBilingualModeOn}">
				<ui:textarea type="rich" i18n_title="${langLabelL2}" id="eventOwnerReminderBodyBodyL2" name="eventOwnerReminderBodyL2" classes="emailBodyPlace"></ui:textarea>
			</c:if>
			<ui:note><orbis:message code="i18n.global_moduleEdit.eventDetailsAppendedAutomatically" /></ui:note>
		</ui:formGroup>			
	</c:if>
	
</ui:formPage>

<%@ include file="global_emailOptionsConfigTokenInfo.jsp"%>