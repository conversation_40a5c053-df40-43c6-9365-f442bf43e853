<%-- <ui:isConverted toolConversion="true" type="partial" />   --%>
<ui:form id="eventRegistrationReportsForm" eventId="${event.id}" report="" action="eventRegistrationReports"></ui:form>

<c:if test="${event.enableRegistration}">
	<ui:panel classes="plain">
		<ui:panelTitle><orbis:message code="i18n.global_eventRegistrationCounts.EventRegistrations" /></ui:panelTitle>
		<ui:grid>
			<ui:gridCol width="12">
				<ui:card type="card01" size="xxs">
					<ui:cardBody>
						<orbis:message code="i18n.global_eventRegistrationCounts.numRegistered" />
					</ui:cardBody>
					<ui:cardStat onclick="$('#eventRegistrationReportsForm').find('input[name=report]').val('registered');$('#eventRegistrationReportsForm').submit();">${regCount + cancelCount}</ui:cardStat>
				</ui:card>
				<ui:card type="card01" size="xxs">
					<ui:cardBody>
						<orbis:message code="i18n.global_eventRegistrationCounts.numAttending" />
					</ui:cardBody>
					<ui:cardStat onclick="$('#eventRegistrationReportsForm').find('input[name=report]').val('attending');$('#eventRegistrationReportsForm').submit();">${attendingCount}</ui:cardStat>
				</ui:card>
				<ui:card type="card01" size="xxs">
					<ui:cardBody>
						<orbis:message code="i18n.global_eventRegistrationCounts.numAttending" />
					</ui:cardBody>
					<ui:cardStat onclick="$('#eventRegistrationReportsForm').find('input[name=report]').val('attended');$('#eventRegistrationReportsForm').submit();">${attendedCount}</ui:cardStat>
				</ui:card>
				<ui:card type="card01" size="xxs">
					<ui:cardBody>
						<orbis:message code="i18n.global_eventRegistrationCounts.late" />
					</ui:cardBody>
					<ui:cardStat onclick="$('#eventRegistrationReportsForm').find('input[name=report]').val('late');$('#eventRegistrationReportsForm').submit();">${lateCount}</ui:cardStat>
				</ui:card>
				<ui:card type="card01" size="xxs">
					<ui:cardBody>
						<orbis:message code="i18n.global_eventRegistrationCounts.noShow" />
					</ui:cardBody>
					<ui:cardStat onclick="$('#eventRegistrationReportsForm').find('input[name=report]').val('noShow');$('#eventRegistrationReportsForm').submit();">${noShowCount}</ui:cardStat>
				</ui:card>
				<ui:card type="card01" size="xxs">
					<ui:cardBody>
						<orbis:message code="i18n.global_eventRegistrationCounts.numCancelled" />
					</ui:cardBody>
					<ui:cardStat onclick="$('#eventRegistrationReportsForm').find('input[name=report]').val('cancel');$('#eventRegistrationReportsForm').submit();">${cancelCount}</ui:cardStat>
				</ui:card>
				<c:if test="${event.enableWaitingList}">
					<ui:card type="card01" size="xxs">
						<ui:cardBody>
							<orbis:message code="i18n.global_eventRegistrationCounts.numWaitingList" />
						</ui:cardBody>
						<ui:cardStat onclick="$('#eventRegistrationReportsForm').find('input[name=report]').val('waiting');$('#eventRegistrationReportsForm').submit();">${waitingListCount}</ui:cardStat>
					</ui:card>
				</c:if>
			</ui:gridCol>
		</ui:grid>
	</ui:panel>
</c:if>
<c:if test="${!event.enableRegistration}">
	<ui:note type="info">
		<orbis:message code="i18n.global_eventRegistrationCounts.Thiseventd4699411364623385" />
	</ui:note>
</c:if>