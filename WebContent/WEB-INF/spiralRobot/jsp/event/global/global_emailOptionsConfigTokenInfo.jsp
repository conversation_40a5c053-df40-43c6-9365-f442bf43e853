<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%-- <ui:isConverted type="complete"/>   --%>

<c:if test="${not empty emailTokens}">
    <style>
        .email_token {
            cursor: pointer;
            display: inline-block;
            margin-right: 5px;
        }
    </style>

    <orbis:message var="tokenTooltip"
                   code="i18n.eventEmailTokenInfo.Clicktoadd0208001830392508"
                   javaScriptEscape="true"/>
    <script>
        $(document).ready(function () {
            var allowedTokens = ${emailTokens};
            var isBilingualModeOn = ${isBilingualModeOn};

            $.each($(".tokensSpace"), function (index) {
                <%-- appends tokens list to element--%>
                var tokens = allowedTokens.map(item => {
                    var token = $('<span class="token">').text(item);

                    <%-- adds tooltip text to token--%>
                    token.attr("data-original-title", '${tokenTooltip}');
                    token.on('click', function () {
                        if (isBilingualModeOn) {
                            var messageElement = $("#email_body_" + (index * 2)).find("textarea");
                            var currentMessage = messageElement.val();
                            messageElement.val(currentMessage + ' ' + $(this).text());

                            var messageElementL2 = $("#email_body_" + (index * 2 + 1)).find("textarea");
                            var currentMessageL2 = messageElementL2.val();
                            messageElementL2.val(currentMessageL2 + ' ' + $(this).text());
                        } else {
                            var messageElement = $("#email_body_" + index).find("textarea");
                            var currentMessage = messageElement.val();
                            messageElement.val(currentMessage + ' ' + $(this).text());
                        }
                    });

                    var element = $('<div class="email_token">').append(token);
                    return element;
                });
                $("#tokens_list_" + index).append('<orbis:message code="i18n.eventEmailTokenInfo.Followingt9276434276127025" javaScriptEscape="true"/>', tokens);
            });

            $('.token').tooltip();
        });
    </script>
</c:if>