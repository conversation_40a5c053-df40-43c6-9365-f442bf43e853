<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>

<div class="grd lp-theme__content-wrapper">
	<main class="grd__col--12 lp-theme__main">
		<c:if test="${!siteElement.contentItem.hideHeader}">
			<ui:navBack>
				<ui:navBackItem action="displayHome">
					${isL1 ? siteElement.contentItem.name : siteElement.contentItem.name}
				</ui:navBackItem>
			</ui:navBack>
			<ui:header>
				${siteElement.contentItem.header}
			</ui:header>
		</c:if>
		
		<ui:panel>
			<ui:panelTitle>${isL1 ? event.title : event.l2Title}</ui:panelTitle>
			
			<c:set var="eventDate1">
				<fmt:formatDate value="${event.startDate}" pattern="${orbisDateMedium2}" />
			</c:set>
			<c:set var="eventDate2">
				<fmt:formatDate value="${event.endDate}" pattern="${orbisDateMedium2}" />
			</c:set>
			<p>
				<c:if test="${eventDate1 == eventDate2}">
					<c:if test="${!event.hideTime || not empty isStaffOwner}">
						<fmt:formatDate value="${event.startDate}" pattern="${orbisDateMedium2TimeShort}" />
					</c:if>
					<c:if test="${event.hideTime && empty isStaffOwner}">
						<fmt:formatDate value="${event.startDate}" pattern="${orbisDateMedium2}" />
					</c:if>
					<c:if test="${!event.hideTime || not empty isStaffOwner}">
						<orbis:message code="i18n.ev_aggregatorGlobalEvent.to6884614423342296" />
						<fmt:formatDate value="${event.endDate}" pattern=" ${orbisTimeShort}" />
					</c:if>
				</c:if>
				<c:if test="${eventDate1 != eventDate2}">
					<c:if test="${!event.hideTime || not empty isStaffOwner}">
						<fmt:formatDate value="${event.startDate}" pattern="${orbisDateMedium2TimeShort}" />
					</c:if>
					<c:if test="${event.hideTime && empty isStaffOwner}">
						<fmt:formatDate value="${event.startDate}" pattern="${orbisDateMedium2}" />
					</c:if>
					<orbis:message code="i18n.ev_aggregatorGlobalEvent.to6884614423342296" />
					<c:if test="${!event.hideTime || not empty isStaffOwner}">
						<fmt:formatDate value="${event.endDate}" pattern="${orbisDateMedium2TimeShort}" />
					</c:if>
					<c:if test="${event.hideTime && empty isStaffOwner}">
						<fmt:formatDate value="${event.endDate}" pattern="${orbisDateMedium2}" />
					</c:if>
				</c:if>
				<br />
				<c:if test="${event.locationType == 0 || event.locationType == 3}">
					${event.location}
				</c:if>
			</p>
			
			<c:if test="${event.enableRegistration && not empty event.registrationDeadline}">
				<p>
					<orbis:message code="i18n.ev_aggregatorGlobalEvent.Registrati0597296869173942" />
					<b>
						<c:if test = "${!event.hideTime || not empty isStaffOwner}">
							<fmt:formatDate value="${event.registrationDeadline}" pattern="${orbisDateMedium2TimeShort}" />
						</c:if>
						<c:if test = "${event.hideTime && empty isStaffOwner}">
							<fmt:formatDate value="${event.registrationDeadline}" pattern="${orbisDateMedium2}" /> 
						</c:if>
					</b>
				</p>
			</c:if>
			<p>
				${isL1 ? event.description:event.l2Description}
			</p>
			
			<%@ include file="ev_aggregatorRegisterButton.jsp"%>
			
			
		</ui:panel>
	</main>
</div>