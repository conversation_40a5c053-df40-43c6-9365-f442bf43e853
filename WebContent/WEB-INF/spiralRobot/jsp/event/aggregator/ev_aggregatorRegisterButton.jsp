<c:if test="${event.enableRegistration}">
	<c:if test="${(!event.advancedEvent || not empty currentUser) && not empty eventSe}">
		<c:set var="displayGlobalEventDetails"><o:encrypt action="displayGlobalEventDetails" /></c:set>
		<ui:button href="${eventSe.fullPathWithFQDN}?action=${displayGlobalEventDetails}&eventId=${event.id}">
			<orbis:message code="i18n.ev_aggregatorRegisterButton.Registerfo9448525509096192" />
		</ui:button>
	</c:if>
	<c:if test="${event.advancedEvent && empty currentUser}">
		<%@ include file="/WEB-INF/spiralRobot/jsp/event/event_advancedRegistrationTypesModal.jsp"%>
	</c:if>
</c:if>