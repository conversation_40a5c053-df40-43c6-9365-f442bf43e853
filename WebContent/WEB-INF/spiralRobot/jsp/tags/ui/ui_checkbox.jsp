<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>

<c:if test="${!readOnly}">
	<p id="${id}_label" class="label label--p ${labelClasses}" style="margin:0 !important; ${labelCss} ${hideLabel ? 'font-size: 0;' : ''}">
		${tagBody}
		<c:if test="${not empty sectionsMap.helpText || helpText.populated && ui:isHelpTextLimitReached(helpText.translation)}">
			<a href="javascript:void(0);" id="${id}_help_icon" class="btn__small btn--default is--btn plain position--relative" onclick="$('#${id}_help_modal').uiShow()">
				<i class="material-icons">help</i>
			</a>
			<ui:modal id="${id}_help_modal" i18n_title="${tagBody}" includeClose="false">
				${helpText}
			</ui:modal>
		</c:if>
	</p>
	<div class="input__group ${groupClasses} ${inlineBlockLabel ? 'display--inline-block' : ''}" style="${groupCss}">
		<label class="toggle--single" aria-labelledby="${id}_label">
			<input type="checkbox" class="${classes}" onchange="${onChange}" <c:if test="${not empty value}">value="${value}"</c:if> name="${name}" id="${id}" ${dynamicElementAttrs} ${checked ? 'CHECKED' : ''} ${disabled ? 'DISABLED' : ''} />
			<span class="toggle--single__container"></span>
			<i class="material-icons on">check</i>
			<i class="material-icons off">close</i>
		</label>
		<c:if test="${not empty helpText.populated && !ui:isHelpTextLimitReached(helpText.translation)}">
			<p class="text--help">
				${helpText}
			</p>
		</c:if>
	</div>
</c:if>

<c:if test="${readOnly}">
	<div class="input__group margin--t--l ${classes}">
		<p class="label--disabled  protip" data-pt-classes="tip--default" data-pt-title="This field is not editable">${tagBody}</p>
		<p>${checked ? "Yes" : "No"}</p> 
	</div>
</c:if>