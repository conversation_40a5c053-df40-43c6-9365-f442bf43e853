<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>

<c:if test="${!readOnly}">
	<%-- Structure pulled from ui_toggle.jsp. Unable to use the actual tag until we figure out a way to pass dynamicElementAttrs from one tag to the next. --%>
	<label id="${id}" class="ui__toggle display--flex flex--column ui__checkbox--group--item js__checkbox--group--item ${groupClasses}" style="${groupCss}">
		<c:if test="${not empty tagBody}">
			<p class="label label--p margin--t--none margin--b--s js__toggle--label">
				${tagBody}
			</p>
		</c:if>

		<input
			type="checkbox"
			class="${classes}"
			name="${name}"
			${dynamicElementAttrs}
			<c:if test="${not empty value}">value="${value}"</c:if>
			${checked ? 'CHECKED' : ''}
			${disabled ? 'DISABLED' : ''} />

		<span class="toggle"></span>

		<c:if test="${helpText.populated}">
			<p class="text--help margin--t--s">
				${helpText}
			</p>
		</c:if>
	</label>

</c:if>
	
<c:if test="${readOnly && checked}">
	<li class="margin--b--s">${tagBody}</li>
</c:if>

