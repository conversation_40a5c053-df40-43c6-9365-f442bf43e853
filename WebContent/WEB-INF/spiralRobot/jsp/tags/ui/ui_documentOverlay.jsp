<%@ include file="/WEB-INF/jsp/include.jsp"%>

<style type="text/css">
	
	.modal__inner--document-overlay {
		padding: 16px;
		height: calc(100% - 32px);
		overflow: hidden;
	}
	
	.modal__inner--document-overlay .floating--action-bar {
		bottom: 32px;
	}
	
	@media screen and (min-width: 501px)
	{
		.modal__inner--document-overlay {
			height: calc(100% - 64px);
		}
		
		.modal__inner--document-overlay .floating--action-bar {
			bottom: 48px;
		}
	}
	
	@media screen and (min-width: 601px)
	{
		.modal__inner--document-overlay {
			max-width: none;
			margin: 32px;
			height: calc(100% - 64px);
		}
	}
	
	@media screen and (min-width: 1025px)
	{
		.modal__inner--document-overlay {
			max-width: 896px;
			padding: 40px;
			margin: 32px auto;
		}
	}
	
	.modal__inner--document-overlay.is--fullscreen {
		max-width: none;
		height: auto;
		padding: 0;
		margin: 0;
		left: 0;
		right: 0;
	}
</style>

<div id="${id}" class="modal js--modal display--none js--ui-modal js--ui-document-overlay ${classes}" data-ui-backdrop="${backdrop}" data-ui-show="orbisAppSr.showModal('${id}');" data-ui-hide="orbisAppSr.hideModal('${id}');" data-append-to-body="${appendToBody}">
	<div id="${id}_modalInner" class="modal__inner modal__inner--document-overlay  ${modalClasses} ${fullscreen ? 'is--fullscreen' : 'border-radius--16'}" style="${modalCss}">
	
		<c:if test="${fullscreen}">
			<button type="button" class="modal__btn--close js--close--modal"><i class="material-icons">close</i></button>
		</c:if>
	
		<div class="modal__content" style="max-height: 100%; height: 100%;">
			${tagBody}
		</div>
		
		<ui:section key="actionBar" evaluate="${sectionsMap}">
			
			<c:if test="${!fullscreen}">
				<ui:actionsBar>
					<c:forEach items="${buttons}" var="button">
						<ui:actionsBarItem icon="${button.icon}" onclick="${button.processedOnclick}">
							${button.tagAttributes.title}
						</ui:actionsBarItem>
					</c:forEach>
					<ui:actionsBarItem icon="close" onclick="$('#${id}').uiHide();">
						Close
					</ui:actionsBarItem>
				</ui:actionsBar>
			</c:if>
			
		</ui:section>
	</div>
</div>

<script type="text/javascript">
	$("#${id}_modalInner").perfectScrollbar();
</script>