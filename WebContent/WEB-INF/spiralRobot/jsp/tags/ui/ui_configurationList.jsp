<%@ include file="/WEB-INF/jsp/include.jsp"%>

	<style>
		.fixed--bottom {
			width: 320px;
			bottom: 0;
			right: 0;
			height: 72px;
			position: fixed;
		}

		@@media screen and (min-width: 1024px) {
			.fixed--bottom {
				width: 408px;
				bottom: 0;
				right: 0;
				height: 72px;
				position: fixed;
			}
		}

		.sidebar--action:not(.is--visible) button.fixed--bottom {
			display: none !important;
		}
		
		.ui--configuration-list .modal__btn--close-posting {
			background: #444;
			color: #FFFFFF;
			height: 72px;
			width: 64px;
			position: absolute;
			top: 0;
			right: 0;
			z-index: 5;
		}
		
		.ui--configuration-list .modal__title-posting {
			margin: 0;
			padding: 0;
			display: inline-block;
			position: relative;
			z-index: 4;
			position: absolute;
			top: 0;
			left: 0;
			right: 44px;
			line-height: 44px;
			background: #444;
			padding: 12px 16px;
			height: 72px;
			color: #FFFFFF;
		}
		
		.configuration-list--loading {
			position: fixed;
			left: 0;
			right: 0;
			top: 0;
			bottom: 0;
		}
		
		@media screen and (min-width: 769px) {
			.configuration-list--loading {
				right: 320px;
			}
		}
		
		@media screen and (min-width: 1200px) {
			.configuration-list--loading {
				right: 408px;
			}
		}
		
	</style>
	
	<div id="${id}" class="ui--configuration-list">
		<ui:sidebar id="${id}_sidebar" i18n_title="${title}" appendToBody="false" overrideLayout="true" classes="js--config-list-sidebar">
			<div class="doc-viewer__content position--relative">
				<div class="doc-viewer__filters is--filter-groups data-grid--sidebar__content">
					<button type="button" class="modal__btn--close modal__btn--close-posting js--btn--close-sidebar"><i aria-hidden="true" class="material-icons">close</i></button>
					
					<div class="sidebar--action__content">
						<div class="align--middle display--flex modal__title-posting padding--t--m">
							<h4 class="margin--b--none margin--r--s">
								${title}
							</h4>
						</div>
						
						<div class="sidebar--action__hero is--expanded">
							<div class="padding--l--m padding--r--m padding--t--l padding--b--l display--flex align--middle" style="height: 144px">
								<span class="h3 js--selected-group">${groups[0].tagAttributes.title}</span>
							</div>
		
							<c:if test="${fn:length(groups) > 1}">
								<ui:dropdown size="large" i18n_title="Sections" classes="width--100" type="info">
									<c:forEach items="${groups}" var="group">
										<ui:dropdownItem onclick="$('#${id}').uiConfigList('showGroup', '${group.id}');"> ${group.tagAttributes.title} </ui:dropdownItem>
									</c:forEach>
								</ui:dropdown>
							</c:if>
						</div>
					</div>
					
					<c:forEach items="${groups}" var="group" varStatus="status">
						<ul class="js--config-list-group list--plain ${status.index != 0 ? 'display--none' : ''}" id="${group.id}" data-group-title="${group.tagAttributes.title}">
						
							<c:forEach items="${group.items}" var="item">
								<li class="margin--a--s">
								
									<a id="${item.id}" href="javascript:void(0)" class="js--config-list--page-link" ${item.customAction ? '' : item.tagAttributes.action.dataAttributes} onclick="${item.processedOnClick}">
										${item.tagBody}
									</a>

								</li>
							</c:forEach>
							
						</ul>
					</c:forEach>
					
				</div>
			</div>
		</ui:sidebar>
		
		<article class="form-modal-sidebar js--config-list-config-sidebar js--config-placeholder" style="overflow-y: auto;"></article>
		
		<div class="loading--stuff configuration-list--loading js--configuration-list--loading">
			<p>Loading...</p>
		</div>
		
	</div>
	
	<script type="text/javascript">
		$("#${id}").uiConfigList();
	</script>
	
	<c:if test="${not empty showConfig}">
		<script type="text/javascript">
			$("#${id}").uiConfigList("loadConfig", "${showConfig}");
		</script>
	</c:if>
