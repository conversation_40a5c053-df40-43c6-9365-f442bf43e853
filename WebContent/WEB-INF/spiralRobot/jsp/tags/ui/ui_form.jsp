<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="o" uri="/WEB-INF/tlds/orbis.tld" %>
<%@ taglib prefix="orbis" uri="/WEB-INF/tlds/orbis-taglib.tld" %>


<c:if test="${not empty siteElement}">
	<c:set var="url" scope="page">${siteElement.fullPath}.htm</c:set>
</c:if>

<form class="margin--b--xxl js--ui-form ${tagAttributes.classes}" style="${tagAttributes.css}" id="${tagAttributes.id}" method="${tagAttributes.get ? 'GET' : 'POST'}" enctype="${tagAttributes.removeEnctype ? '' : 'multipart/form-data'}" target="${tagAttributes.target}" action="${tagAttributes.url}" autocomplete="${tagAttributes.autocomplete ? 'on' : 'off'}" name="${tagAttributes.name}" onsubmit="${tagAttributes.onBeforeSubmit}; return $('#${tagAttributes.id}').uiForm('formOnSubmitOnly', function(data){${tagAttributes.callback}});" data-ajax-form="${tagAttributes.ajaxForm}">
	${tagAttributes.action.hiddenInputs}
	<input type="hidden" name="uiForm" value="true" />
	
	${tagBody}
</form>

<script type="text/javascript">
	$('#${tagAttributes.id}').uiForm();
</script>