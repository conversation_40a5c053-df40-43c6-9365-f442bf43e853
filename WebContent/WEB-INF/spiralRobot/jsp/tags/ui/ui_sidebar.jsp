<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>

<style type="text/css">
	.sidebar--updated {
		padding: 0!important;
    }

	.sidebar--updated:not(.is--visible) button.fixed--bottom {
		display: none !important;
	}
	
	.sidebar--updated .modal__btn--close-posting {
		background: #444;
		color: #FFFFFF;
		height: 72px;
		width: 64px;
		position: absolute;
		top: 0;
		right: 0;
		z-index: 5;
	}
	
	.sidebar--updated .modal__title-posting {
		margin: 0;
		display: inline-block;
		z-index: 4;
		position: absolute;
		top: 0;
		left: 0;
		right: 52px;
		line-height: 44px;
		background: #444;
		padding: 12px 16px;
		height: 72px;
		color: #FFFFFF;
	}
	
	.modal__btn--back {
		right: auto;
		left: 0;
	}
	
	.has--back-btn .modal__title-posting {
		left: 52px;
	}
</style>

<c:if test="${!overrideLayout}">
	<div id="${id}" class="sidebar--action ui__sidebar sidebar--updated color--bg--grey--lightest ${location} ${classes} ${not empty backAction ? 'has--back-btn' : ''} js--ui-sidebar" role="navigation" data-open-button-selector="${openButtonSelector}">
		
		<div class="ui__sidebar__content--wrapper position--relative display--flex flex--column">

			<div>
				<c:if test="${not empty backAction}">
					<button type="button" class="modal__btn--close modal__btn--close-posting modal__btn--back js--btn--close-sidebar" onclick="${backAction.processedOnclick}"><i aria-hidden="true" class="material-icons">arrow_back_ios</i></button>
				</c:if>

				<button type="button" class="modal__btn--close modal__btn--close-posting js--btn--close-sidebar"><i aria-hidden="true" class="material-icons">close</i></button>

				<div class="align--middle display--flex modal__title-posting padding--t--m">
					<h4 class="margin--b--none margin--r--s">
						${title}
					</h4>
				</div>
			</div>
			<div class="ui__sidebar__body display--flex flex--column">
				<c:if test="${currentTag.headerContent}">
					<div class="sidebar--action__hero is--expanded">

						<div class="padding--l--m padding--r--m padding--t--l padding--b--l display--flex align--middle" style="height: 144px">
							<c:if test="${not empty currentTag.header}">
								<span class="h3">${currentTag.header.tagAttributes.title}</span>
							</c:if>

							<c:if test="${not empty currentTag.sections}">
								<span class="h3 js--selected-section"></span>
							</c:if>

							<c:forEach items="${currentTag.headerStats}" var="stat">
								<span class="tag-label info text--large is--box display--flex">
									<span class="margin--r--m">
										${stat.tagAttributes.title}
									</span>
									<span class="tag-label color--bg--dark js--header-stat">${stat.tagAttributes.value}</span>
								</span>
							</c:forEach>

							<ui:section key="header" evaluate="${sectionsMap}" />
						</div>

						<c:if test="${not empty currentTag.sections}">
							<ui:dropdown size="large" i18n_title="Sections" classes="width--100" type="info">
								<c:forEach items="${currentTag.sections}" var="section">
									<ui:dropdownItem onclick="orbisAppSr.sidebar.functions.showSection('${id}', '${section.tagAttributes.id}');"> ${section.tagAttributes.title} </ui:dropdownItem>
								</c:forEach>
							</ui:dropdown>
						</c:if>
					</div>
				</c:if>

				<ui:section key="overrideHeader" evaluate="${sectionsMap}" />

				<div class="sidebar--action__content ui__sidebar__content grow--1 position--relative ${removeContentPadding ? '' : 'padding--a--m padding--t--l'} js__sidebar__content">
					${tagBody}
				</div>

				<c:if test="${not empty sectionsMap.footer}">
					<div>
						<ui:section key="footer" evaluate="${sectionsMap}" />
					</div>
				</c:if>
			</div>
		</div>
	</div>
</c:if>

<c:if test="${overrideLayout}">
	<div id="${id}" style="padding: 0;" class="sidebar--action ${location} ${classes} js--ui-sidebar" role="navigation" data-open-button-selector="${openButtonSelector}">
		${tagBody}
	</div>
</c:if>

<c:set var="uniqueCallbackId" value="${o:generateId()}" />
<script type="text/javascript">
	function sidebarCallback${uniqueCallbackId}() {
		orbisAppSr.sidebar.functions.init("${id}", ${appendToBody}, ${alwaysInBody});
		
		<c:if test="${showOnLoad}">
			orbisAppSr.sidebar.functions.show("${id}");
		</c:if>
	}
	
	sidebarCallback${uniqueCallbackId}();
</script>