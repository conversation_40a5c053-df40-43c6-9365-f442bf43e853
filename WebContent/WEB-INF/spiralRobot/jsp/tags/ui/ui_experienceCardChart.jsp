<%@ include file="/WEB-INF/jsp/include.jsp"%>

<orbis:addComponent component="highCharts_5"/>

<li id="${id}" class="card--round is--big">
	<div class="card--round__top position--relative card--round__top--experiential" style="height: 246px;">
		<div class="height--100 display--flex flex--column">
			<div class="grow--1 js--chart-container">
				
			</div>
			<div class="display--flex align--center padding--a--xs js--legend-container" v-cloak>
			
				<button v-for="(d, idx) in data" ref="legendButtons" :key="idx" :data-pt-title="d.name" class="tag-label border-radius--circle display--flex align--center align--middle info text--large border-radius--16 margin--l--xs protip">
					<i class="material-icons border-radius--12" :style="'background-color: ' + d.color + ';'">{{d.icon}}</i>
					<span class="h6 margin--b--none font--bold margin--l--xs">{{d.percentage | roundPercentage}}%</span>
				</button>
				
			</div>
		</div>
	</div>
	
	<div class="card--round__bottom position--relative" style="height: 92px;">
		<h3>
			<a href="javascript:void(0)">${title}</a>
		</h3>
		
		${tagBody}
	</div>
</li>

<script type="text/javascript">
	
	function onLegendMounted_${id}()
	{
		var buttons = this.$refs.legendButtons;
		
		for(var i = 0; i < buttons.length; i++)
		{
			$(buttons[i]).protipSet({
				trigger: "hover",
				position: "top"
			});
		}
	}
	
</script>

<orbis:addComponent component="uiChartCard" callback="$('#${id}').uiChartCard($.extend(${chartParams}, {onLegendMounted: onLegendMounted_${id}}));" />