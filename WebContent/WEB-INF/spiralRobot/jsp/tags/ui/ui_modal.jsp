<%@ include file="/WEB-INF/jsp/include.jsp"%>
<%@ taglib prefix="ui" tagdir="/WEB-INF/tags/ui" %>


<div id="${id}" class="modal js--modal display--none js--ui-modal is--spiral--robot eat--bootstrap ${classes}" data-ui-backdrop="${backdrop}" data-ui-show="orbisAppSr.showModal('${id}');" data-ui-hide="orbisAppSr.hideModal('${id}');" data-append-to-body="${appendToBody}">
	<div class="modal__inner">
		<button type="button" class="modal__btn--close js--close--modal"><i class="material-icons">close</i></button>
		<h3 class="modal__title ${(title == 'Mass Update Appointments') || (title == 'Date Range Search')
		|| (title == 'ADVANCED SEARCHING') || (title == 'Search Options') ? 'sel_massUpdateAppointment' : ''}">${title}</h3>

		${tagBody}

		<c:if test="${includeClose}">
			<ui:button type="info" id="close${id}" hide="${id}"><orbis:message code="i18n.ui_modal.Close4611160457666870" javaScriptEscape="true" /></ui:button>
		</c:if>
	</div>
</div>
<c:if test="${not empty onClose}">
	<script>
		$('#${id}').on('hidden.bs.modal', function () {
			${onClose}
		})
	</script>
</c:if>
