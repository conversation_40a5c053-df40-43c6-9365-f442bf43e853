<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="ui" uri="/WEB-INF/tlds/spiralRobot.tld" %>
<%@ taglib prefix="orbis" uri="/WEB-INF/tlds/orbis-taglib.tld" %>
<%@ taglib prefix="o" uri="/WEB-INF/tlds/orbis.tld" %>

<%-- note that this tag is meant to be used on any view that is related to the current user being viewed,
	 therefore make sure that any actions or items being displayed in here is wrapped in the appropriate permissions --%>

<style type="text/css">

	.dashboard-header{
		min-height: unset !important; 
		height: auto !important; 
	}
	
	.ui--user-profile-header .dashboard-header__container--primary {
		max-width: 100% !important;
	}

   .dashboard-header__profile-widget {
      margin-right: 24px;
   }

   .dashboard-header__profile-widget-image {
      /*width: 88px;
      height: 88px;*/
      display: flex;
      align-items: center;
      justify-content: center;
   }

   .icon-only-btn{
      order: 2;
      color: #fff;
      background: none;
      border: 0px solid white;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      transition: all 200ms ease;
   }
   .icon-only-btn:hover, .icon-only-btn:focus{
      border: 8px solid white;
      transition: all 200ms ease;
   }
   .dashboard-header__profile-widget__btn--more{
      width: 32px;
      height: 32px;
      background: white;
      color: rgba(0,0,0,0.8);
      border-radius: 16px;
      position: absolute;
      bottom: -8px;
      right: -12px;
      transform: scale(1);
      transition: all 200ms ease;
   }

   .dashboard-header__profile-widget__btn--more:hover, .dashboard-header__profile-widget__btn--more:focus{
      transform: scale(1.5);
      transition: all 200ms ease;
   }

   .dashboard-header__profile-information-name-content {
      flex-grow: 1;
   }

   .object--cover {
      width: 100%;
      height: 100%;
      object-fit: cover;
   }

   .dashboard-header__profile-widget-image-container {
      width: 100%;
      height: 100%;
      overflow: hidden;
      border-radius: 100%;
   }

   .ui--user-profile-header .dashboard-header__profile-widget-image.no--image {
      border: none;
   }

</style>

<ui:grid classes="dashboard-header position--relative padding--a--m display--flex align--middle ui--user-profile-header ${classes}" css="flex-wrap: wrap !important;">
	<div class="padding--b--l display--flex width--100 z-index--1">
		<ui:section key="topControls" evaluate="${sectionsMap}" />
	</div>
	
	<ui:gridCol width="6" classes="dashboard-header__container--primary user--profile display--flex flex--column">
		<div class="display--flex">
			<c:if test="${not empty currentUser && !excludeImageSection}">
				<div class="dashboard-header__profile-widget">
				
					<div class="display--flex flex--column align--middle">
						<div class="position--relative dashboard-header__profile-widget-image ${empty profileImageUrl ? 'no--image' : ''}">
							<c:if test="${not empty sectionsMap.additionalInfo}">
								<button onclick="$('#userHeaderAdditionalDetailsSidebar').uiSidebar('show');" aria-label="<orbis:message code="i18n.ui_organizationProfileHeader.Additional1854183012664408" />" type="button" class="align--center align--middle display--flex dashboard-header__profile-widget__btn--more" style="
								    width: 24px;
								    height: 24px;
								    background: white;
								    color: rgba(0,0,0,0.8);
								    border-radius: 16px;
								    position: absolute;
								    bottom: -8px;
								    right: -12px;
								    z-index: 1;
								">
									<i class="material-icons">contact_page</i>
								</button>
							</c:if>
							
							<c:if test="${not empty userStatus}">
								<c:choose>
									<c:when test="${userStatus == 'inactive'}">
									
										<div class="color--bg--error display--flex align--center align--middle protip" 
											data-pt-classes="tip--default" data-pt-position="top" data-pt-title="<orbis:message code="i18n.ui_userProfileHeader.Inactive3518408463556885" />" 
											style="height:24px; width:24px; border-radius:100%; position: absolute;right: 0px; top: 0px; z-index: 1;">
											
											<i class="material-icons color--font--black" style="font-size: 16px;">clear</i>
										</div>
									
									</c:when>
									<c:when test="${userStatus == 'active'}">
									
										<div class="color--bg--success display--flex align--center align--middle protip" 
											data-pt-classes="tip--default" data-pt-position="top" data-pt-title="<orbis:message code="i18n.ui_userProfileHeader.Active6741365345521682" />" 
											style="height:24px; width:24px; border-radius:100%; position: absolute; right: 0px; top: 0px; z-index: 1;">
											
											<i class="material-icons color--font--black" style="font-size: 16px;">done</i>
										</div>
									
									</c:when>
									<c:when test="${userStatus == 'prospect'}">
									
										<div class="color--bg--info display--flex align--center align--middle protip" 
											data-pt-classes="tip--default" data-pt-position="top" data-pt-title="<orbis:message code="i18n.ui_userProfileHeader.Prospect8559134193519068" />" 
											style="height:24px; width:24px; border-radius:100%; position: absolute;right: 0px; top: 0px; z-index: 1;">
											
											<i class="material-icons color--font--black" style="font-size: 16px;">person_add</i>
										</div>
									
									</c:when>
								</c:choose>
							</c:if>
							
							<c:if test="${not empty profileImageUrl}">
								<div class="dashboard-header__profile-widget-image-container">
									<img src="${profileImageUrl}" alt="${user.fullName}" title="${user.fullName}" class="object--cover" />
								</div>
							</c:if>
							
							<c:if test="${empty profileImageUrl}">
								<i class="material-icons color--font--white position--absolute" style="font-size: 56px;">
									face
								</i>
							</c:if>
						</div>
						
						<%@ include file="/WEB-INF/spiralRobot/jsp/tags/ui/ui_headerUserTag.jsp"%>
					
					</div> 
				</div>
			</c:if>
	
			<div class="dashboard-header__profile-information" style="flex-grow: 1;">
				<c:if test="${not empty sectionsMap.statusLabel}">
					<ul class="display--flex list--plain">
						<li>
							<span class="tag-label display--flex align--center align--middle border-radius--circle black margin--b--s margin--r--s">
								<i class="material-icons info">fiber_manual_record</i>
								<ui:section key="statusLabel" evaluate="${sectionsMap}" />
							</span>
						</li>
					</ul>
				</c:if>
				<h1 class="h3 dashboard-header__profile-information-name mobile--small-font color--font--white margin--b--s" style="
					display: flex;
					align-items: center;
					margin-bottom: 0 !important;
					width: 100%;
				">
				
					<c:choose>
						<c:when test="${user.employer}">
							<c:if test="${currentTag.trustedEmployer}">
								<i class="material-icons color--font--white" style="
								    margin-right: 4px;
								" title="Trusted Employer">verified_user</i>
							</c:if>
							<c:if test="${user.convertedFromProspect}">
								<i class="material-icons color--font--white" style="
								    margin-right: 4px;
								" title="Converted Prospect">filter_alt</i>
							</c:if>
						</c:when>
					</c:choose>
				
					<div class="dashboard-header__profile-information-name-content text--truncate">
						<c:if test="${empty acrmSe && !overrideTitle}">
							
							<ui:section key="title" evaluate="${sectionsMap}">
								${user.firstName} <c:if test="${not empty user.commonName && showPreferredName}">"${user.commonName}" </c:if>${user.lastName}
							</ui:section>
							
						</c:if>
						
						<c:if test="${not empty acrmSe && !overrideTitle}">
							
								
							<o:nav anchor="true" action="displayContactOverview" acrmUserId="${user.id}" anchorClass="color--font--white" siteElement="${acrmSe}" >
								
								<ui:section key="title" evaluate="${sectionsMap}">
									${user.firstName} <c:if test="${not empty user.commonName && showPreferredName}">"${user.commonName}" </c:if>${user.lastName}
								</ui:section>
								
								<c:choose>
									<c:when test="${user.student}">
										<c:if test="${'trent' == siteCode && not empty user.alternateId}">
											 - ${user.alternateId}
										</c:if>
									</c:when>
								</c:choose>
							</o:nav>


						</c:if>

						<c:if test="${overrideTitle}">
							<ui:section key="title" evaluate="${sectionsMap}">
							</ui:section>
						</c:if>
					</div>
					
					 
				</h1>
				
				<c:set var="fullSubtitle">
					<c:choose>
						<c:when test="${user.student}">
							
							<c:if test="${'mcmaster' == siteCode}">
								<c:if test="${not empty user.assignedTypes['Student - Full-time']}">
									<orbis:message code="i18n.ui_userProfileHeader.Fulltime4652656192741918" />
								</c:if>
								<c:if test="${not empty user.assignedTypes['Student - Part-time']}">
									<orbis:message code="i18n.ui_userProfileHeader.Parttime8683471963574141" />
								</c:if>
								<c:if test="${not empty user.assignedTypes['Student - MBA Co-op']}">
									<orbis:message code="i18n.ui_userProfileHeader.MBACoop1077671725758357" />
								</c:if>
								<c:if test="${not empty user.assignedTypes['Student - Commerce Internship']}">
									<orbis:message code="i18n.ui_userProfileHeader.CommerceIn1017126196469651" />
								</c:if>
							</c:if>
							
							<c:if test="${'dalhousie' == siteCode}">
								<c:if test="${not empty user.assignedTypes['Student - Engineering and Computer Science Co-op']}">
									<orbis:message code="i18n.ui_userProfileHeader.StudentEng3072019563702852" />
								</c:if>
								<c:if test="${not empty user.assignedTypes['Student - Management Career Services']}">
									<orbis:message code="i18n.ui_userProfileHeader.StudentMan0586077920315778" />
								</c:if>
								<c:if test="${not empty user.assignedTypes['Student - Science Co-op']}">
									<orbis:message code="i18n.ui_userProfileHeader.StudentSci8884188544270304" />
								</c:if>
							</c:if>
							
							<c:if test="${'uvic' == siteCode}">
								<c:if test="${not empty user.assignedTypes['Co-op Student']}">
									<orbis:message code="i18n.ui_userProfileHeader.CoopStuden9902343463964806" />
								</c:if>
							</c:if>
							
						</c:when>
					
						<c:when test="${user.employer}">
							<c:if test="${not empty user.jobTitle}">
								${user.jobTitle}
							</c:if>
						</c:when>
					</c:choose>
					
					${subtitle}
				</c:set>
				
				<c:if test="${not empty fullSubtitle}">
					<h2 class="h6 mobile--small-font color--font--white margin--t--s align--start width--100 text--truncate">
						
						${fullSubtitle}
						
					</h2>
				</c:if>
				
				<c:if test="${not empty sectionsMap.subtitle}">
					<div class="h6 mobile--small-font color--font--white width--100">
						<ui:section key="subtitle" evaluate="${sectionsMap}" />
					</div>
				</c:if>
				
				<c:if test="${not empty impersonatorSe && not empty currentUser.assignedTypes['Database - Can log in as other user'] && empty reLogin123}">
					<div class="align--middle display--flex margin--a--reset--mobile margin--b--m margin--t--s">
					
						<c:set var="acrmUserForI" value="${user}" />
						<%@ include file="/WEB-INF/spiralRobot/jsp/ccrm/ccrm_impersonator.jsp"%>
		
					</div>
				</c:if>
				
				<div>
					<span style="color:white;font-weight:bold">
						<ui:section key="profileSection" evaluate="${sectionsMap}" />
					</span>
				</div>
				
				<c:if test="${empty currentUser}">
					<div style="width: 148px;">
						<%@ include file="/WEB-INF/spiralRobot/jsp/tags/ui/ui_headerUserTag.jsp"%>
					</div>
				</c:if>
				
			</div>
		</div>
	</ui:gridCol>
	
	<c:if test="${currentTag.rightColContent}">
		<ui:gridCol width="6" classes="dashboard-header__container--secondary display--flex align--end mobile--align--center wrap margin--b--m" css="max-width: 400px;">
			
			<ui:section key="rightSideContent" evaluate="${sectionsMap}">
				<%@ include file="/WEB-INF/spiralRobot/jsp/tags/ui/ui_headerTags.jsp"%>
			</ui:section>
			
		</ui:gridCol>
	</c:if>
</ui:grid>

<c:if test="${not empty sectionsMap.additionalInfo}">
	<ui:sidebar i18n_title="i18n.ui_organizationProfileHeader.Additional1854183012664408" id="userHeaderAdditionalDetailsSidebar">
		<ui:section key="additionalInfo" evaluate="${sectionsMap}" />
	</ui:sidebar>
</c:if>

<script type="text/javascript">
	function handleTruncatedText($el)
	{
		$el.each(function(){
			var e = $(this).get(0);
			var isTruncated = e.offsetWidth < e.scrollWidth;
			var hasProtip = $(this).hasClass("protip");
			
			if(isTruncated && !hasProtip)
			{
				$(this).addClass("protip");
			}
			else if(!isTruncated && hasProtip)
			{
				$(this).removeClass("protip");
			}
		});
	}

	function addProtips()
	{
		$(".ui--user-profile-header").find(".text--truncate").each(function(){
			$(this).protipSet({
				gravity: "top",
				title: $(this).text()
			});
		}).on("protipready", function(){
			handleTruncatedText($(this));
		});
	}
	
	$(document).ready(function(){
		addProtips();
		
		$(window).on("resize", function(){
			handleTruncatedText($(".ui--user-profile-header").find(".text--truncate"));
		});
	});
</script>