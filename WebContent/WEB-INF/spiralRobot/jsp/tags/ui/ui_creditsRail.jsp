<%@ include file="/WEB-INF/jsp/include.jsp"%>

<style type="text/css">
	.credit-rail__item{
		margin-right:8px;
		height: 48px; 
		max-height: 48px; 
		min-height: 48px; 
		border-radius: 24px;
		min-width: 100px;
		max-width: 200px;
	}
	.credit-rail__content{

	}
	.credit-rail__label{
		max-width: 144px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	.credit-rail__value{
		max-width: 144px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
</style>

<c:if test="${not empty currentTag.credits}">
	<div id="${id}" class="credit-rail display--flex ${classes}" style="${css}">
		<div id="${id}_ps" class="credit-rail__container position--relative grow--1 display--flex align--middle padding--l--xl padding--r--m">
			<ul class="list--plain list-cards display--inline-flex credit-rail__list">
				<c:forEach items="${currentTag.credits}" var="creditTag">
				
					<li class="credit-rail__item padding--r--l padding--l--m overflow--hidden display--flex color--bg--grey align--middle">
						<div class="margin--r--s">
							<i class="material-icons font--16 color--bg--grey--lighter color--bg--grey--lighter border-radius--circular padding--a--xs">${not empty creditTag.icon ? creditTag.icon : 'person'}</i>
						</div>
						
						<div class="display--flex flex--column">
							<div class="font--10 font--bold color--font--white credit-rail__label">
								${creditTag.tagAttributes.title}
							</div>
							
							<div class="font--14 color--font--white credit-rail__value ${creditTag.tagAttributes.title.populated ? '' : 'font--bold'}">
								${creditTag.tagAttributes.name}
							</div>
						</div>
					</li>
					
				</c:forEach>
			</ul>
		</div>
		
		<ui:button type="black" classes="tag-rail__menu-btn" show="${id}_sidebar">
			<i class="material-icons">${not empty currentTag.icon ? currentTag.icon : 'format_list_bulleted'}</i>
		</ui:button>
	</div>


	<ui:sidebar id="${id}_sidebar" i18n_title="${title}" removeContentPadding="true">
		<ui:surveyCards classes="padding--l--m">
			<c:forEach items="${currentTag.credits}" var="creditTag">
				
				<ui:surveyCard icon="${not empty creditTag.icon ? creditTag.icon : 'person'}" i18n_title="${creditTag.tagAttributes.title}">
					${creditTag.tagAttributes.name}
				</ui:surveyCard>
				
			</c:forEach>
		</ui:surveyCards>
	</ui:sidebar>
</c:if>

<script type="text/javascript">
	
	$("#${id}_ps").perfectScrollbar();
	
</script>