<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>

<c:if test="${!readOnly}">
	<div class="js__checkbox--group--item js__checkbox--group--other ${classes}">
		<%-- Structure pulled from ui_toggle.jsp. Unable to use the actual tag until we figure out a way to pass dynamicElementAttrs from one tag to the next. --%>
		<label id="${id}" class="ui__toggle display--flex flex--column ui__checkbox--group--item" style="${groupCss}">

			<p class="label label--p margin--t--none margin--b--s js__toggle--label">
				<orbis:message code="i18n.ui_checkboxGroupOther.Other7687465730950528" />
			</p>

			<input
				type="checkbox"
				id="${id}"
				class="js__checkbox--group--other__checkbox ${checkboxClasses}"
				${not empty value ? 'CHECKED' : ''}
				${disabled ? 'DISABLED' : ''} />

			<span class="toggle"></span>

			<c:if test="${helpText.populated}">
				<p class="text--help margin--t--s">
					${helpText}
				</p>
			</c:if>
		</label>

		<div class="js__other--textbox__container ${empty value ? 'display--none' : ''}">
			<label for="${otherBoxName}" class="only--screen-reader">${parentTitle}: <orbis:message code="i18n.ui_checkboxGroupOther.OtherValue3704614066315046" /></label>
			<input type="text" placeholder="<orbis:message code="i18n.ui_checkboxGroupOther.OtherValue3704614066315046" htmlEscape="true" />" id="${otherBoxName}" checked="${checked}" value="${value}" name="${otherBoxName}" class="input--box" />
		</div>
	</div>
</c:if>
<c:if test="${readOnly && checked}">
	<li class="margin--b--s">${value}</li>
</c:if>