<%@ include file="/WEB-INF/jsp/include.jsp"%>

<style type="text/css">
	.is--header-sticky .ui--productivity-sidebar {
		top: 60px !important;
	}
	
	.footer--visible .ui--productivity-sidebar {
		bottom: 60px !important;
	}
</style>

<div id="${id}" class="sidebar--action calendar__sidebar shadow--box--none is--outcome ui--productivity-sidebar js--productivity-sidebar right is--visible js--no--close__sidebar ${classes}" role="navigation" aria-expanded="true" style="z-index: 10; animation-duration: initial; background: #F9FAFD; padding-top: 88px;">
	<div class="sidebar--action__content">
		<div class="align--middle display--flex modal__title-posting padding--t--m">
			<h4 class="margin--b--none margin--r--s">
				${title}
			</h4>
		</div>
	</div>
	<button type="button" class="modal__btn--close modal__btn--close-posting js--btn--close-sidebar" aria-expanded="true" onclick="$(this).parent().removeClass('is--visible--mobile');"><i aria-hidden="true" class="material-icons">close</i></button>
	
	${tagBody}
</div>

<script type="text/javascript">
	$("#${id}").data("ps", $("#${id}").perfectScrollbar());
</script>