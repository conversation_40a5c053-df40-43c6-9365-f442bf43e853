<%@ include file="/WEB-INF/jsp/include.jsp"%>

<li id="${id}" class="card--round is--big margin--b--xl has--description ${preview ? 'static-record' : 'record'} ${classes}" style="${css}" ${dynamicElementAttrs}>

	<div class="card--round__top position--relative">
		<c:if test="${empty topRightCornerAction}">
			<div class="experiential--edit edit--tool card--remove color--font--error protip is--clickable js--role-button"
				aria-label="<orbis:message code="i18n.ec_publicRecordCommon.RemoveItem2053453650255125" htmlEscape="true" />" role="button" data-pt-title="<orbis:message code="i18n.ec_publicRecordCommon.RemoveFeat3658590305704232" htmlEscape="true" />">
				<i class="material-icons">cancel</i>
			</div>
		</c:if>
		<c:if test="${not empty topRightCornerAction}">
			<c:if test="${topRightCornerAction.onclickPopulated}">
				<a href="javascript:void(0)" 
					class="experiential--edit edit--tool card--remove protip is--clickable ${topRightCornerAction.classes}"
					style="transform: scale(1); z-index: 3; color: ${headerTextColor}; ${topRightCornerAction.css}"
					onclick="${topRightCornerAction.processedOnclick}"
					data-pt-position="top"
					data-pt-classes="tip--default"
					data-pt-title="${topRightCornerAction.tagAttributes.title}"
					aria-label="${topRightCornerAction.tagAttributes.title}"
					${topRightCornerAction.passthroughAttributes}
					${topRightCornerAction.actionAttribute.dataAttributes}>
					
					<i class="material-icons ${topRightCornerAction.iconClasses}" style="${topRightCornerAction.iconCss}">
						${topRightCornerAction.icon}
					</i>
					
				</a>
			</c:if>
			<c:if test="${!topRightCornerAction.onclickPopulated}">
				<div 
					class="experiential--edit edit--tool card--remove protip ${topRightCornerAction.classes}"
					style="transform: scale(1); color: ${headerTextColor}; ${topRightCornerAction.css}"
					data-pt-position="top"
					data-pt-classes="tip--default"
					data-pt-title="${topRightCornerAction.tagAttributes.title}"
					aria-label="${topRightCornerAction.tagAttributes.title}"
					${topRightCornerAction.passthroughAttributes}
					${topRightCornerAction.actionAttribute.dataAttributes}>
					<i class="material-icons">${topRightCornerAction.icon}</i>
				</div>
			</c:if>
		</c:if>

		<c:choose>
			
			<c:when test="${currentTag.headerImage}">
				
				<img class="card--round__background--img width--100" style="object-fit: cover;" src="${headerBackground}" alt="Experience Background Image">
			
				<%@ include file="ui_expTranscriptCard_headerContent.jsp"%>
				
			</c:when>
			
			<c:when test="${currentTag.headerIcon}">
				
				<div class="card--round__background--color" style="background-color: #232323">
					<i class="material-icons color--font--white" style="font-size: 56px;">${headerBackground}</i>
					
					<%@ include file="ui_expTranscriptCard_headerContent.jsp"%>
				</div>
				
			</c:when>
			
			<c:otherwise>
			
				<div class="card--round__background--color" style="background-color: ${not empty headerBackground ? headerBackground : '#000'}">
					<%@ include file="ui_expTranscriptCard_headerContent.jsp"%>
				</div>
			
			</c:otherwise>
			
		</c:choose>
			
		
	</div>

	<div class="card--round__bottom position--relative">
		<ui:section key="title" evaluate="${sectionsMap}">
			<h3 class="display--flex align--middle margin--b--xs">
			
				<orbis:message var="escapedExpTitle" text="${title}" htmlEscape="true" />
				
				<c:if test="${!preview}">
					<a href="javascript:;" onclick="${currentTag.finalOnclick}" class="text--default text-align--left protip" data-pt-classes="tip--default" data-pt-title="${escapedExpTitle}">
						${o:truncate(title, 60, true)}
					</a>
				</c:if>
				
				<c:if test="${preview}">
					<span class="text--default text-align--left protip" data-pt-classes="tip--default" data-pt-title="${escapedExpTitle}">
						${o:truncate(title, 60, true)}
					</span>
				</c:if>
				
			</h3>
		</ui:section>
		
		<orbis:message var="escapedSubtitle" text="${subtitle}" htmlEscape="true" />
		<span
			class="display--flex margin--b--xs align--middle width--100 text--small protip text--truncate"
			data-pt-classes="tip--default" data-pt-title="${escapedSubtitle}">
			${subtitle}
		</span>
		
		<p class="font-size--small text-align--left">
			${o:truncate(o:stripHtml(description), 140, true)}
		</p>
		
		<c:if test="${not empty badgeActions || !preview}">
			<div class="card--round__bottom-group">
				
				<div class="display--flex align--center">
				
					<c:forEach items="${badgeActions}" var="badgeAction">
					
						<c:if test="${badgeAction.onclickPopulated && !preview}">
							<a href="javascript:void(0)" 
								class="btn__default btn--info plain pill protip crd__header-attention-btn margin--r--s ${badgeAction.classes}"
								style="${badgeAction.css}"
								onclick="${badgeAction.processedOnclick}"
								data-pt-position="top"
								data-pt-classes="tip--default"
								data-pt-title="${badgeAction.tagAttributes.title}"
								aria-label="${badgeAction.tagAttributes.title}"
								${badgeAction.passthroughAttributes}
								${badgeAction.actionAttribute.dataAttributes}>
								
								<i class="material-icons ${badgeAction.iconClasses}" style="${badgeAction.iconCss}">
									${badgeAction.icon}
								</i>
								
							</a>
						</c:if>
						
						<c:if test="${!badgeAction.onclickPopulated || preview}">
						
							<span
								class="btn__default btn--info plain pill protip crd__header-attention-btn margin--r--s ${badgeAction.classes}"
								style="line-height: 1.15; ${badgeAction.css}"
								data-pt-position="top"
								data-pt-classes="tip--default"
								data-pt-title="${badgeAction.tagAttributes.title}"
								aria-label="${badgeAction.tagAttributes.title}">
								
								<i class="material-icons ${badgeAction.iconClasses}" style="${badgeAction.iconCss}">
									${badgeAction.icon}
								</i>
								
							</span>
						</c:if>
						
					</c:forEach>
					
				</div>
				
				<c:if test="${!preview}">
					<div class="display--flex align--middle">
						<ui:section key="footerButton" evaluate="${sectionsMap}">
							<ui:button type="success" style="pill" onclick="${currentTag.finalOnclick}">
								<orbis:message code="i18n.ec_publicRecordCommon.View2633079631607797" />
							</ui:button>
						</ui:section>
					</div>
				</c:if>
			</div>
		</c:if>
	</div>
	
	<c:if test="${!preview}">

		<div id="${id}_modal" class="ec-transcript display--none">
			<div class="exp-transcript--single">
				<div id="${id}_modal_psContainer" class="experiential--badge-details display--flex flex--column is--expanded position--relative">
					<div class="text-align--right">
						<ui:button style="plain" size="large" onclick="orbisAppSr.hideModal('${id}_modal');">
							<i class="material-icons padding--a--s is--clickable">close</i>
						</ui:button>
					</div>
					<div class="display--flex experience-details experience-details--cards js--experience-details">
						<div>
						
							<ui:expTranscriptCards>
								<ui:expTranscriptCard preview="true" i18n_description="${description}" i18n_title="${title}" i18n_subtitle="${subtitle}" i18n_term="${term}" i18n_type="${type}" headerTextColor="${headerTextColor}" headerBackground="${headerBackground}">

									<c:if test="${not empty topRightCornerAction}">
										<ui:action id="topRightCorner" icon="${topRightCornerAction.icon}" i18n_title="${topRightCornerAction.tagAttributes.title}" classes="${topRightCornerAction.classes}" css="${topRightCornerAction.css}" />
									</c:if>

									<c:forEach items="${badgeActions}" var="badgeAction">
										<ui:action id="badges" icon="${badgeAction.icon}" i18n_title="${badgeAction.tagAttributes.title}" classes="${badgeAction.classes}" css="${badgeAction.css})" />
									</c:forEach>
									
									<c:forEach items="${headerBadgeActions}" var="headerBadgeAction">
										<ui:action id="headerBadges" icon="${headerBadgeAction.icon}" i18n_title="${headerBadgeAction.tagAttributes.title}" classes="${headerBadgeAction.classes}" css="${headerBadgeAction.css})" />
									</c:forEach>
									
								</ui:expTranscriptCard>
							</ui:expTranscriptCards>
							
						</div>
						<div class="experience-details--details-section">
							
							<div class="crd--more__header margin--b--m grd align--middle">
								<div class="crd--more__header--flex-container">
									<div>
										<c:choose>
											<c:when test="${not empty modalTitleLinkParams}">
												<o:nav action="${modalTitleLinkParams.action}" additionalParams="${modalTitleLinkParams.additional}" anchor="true" siteElementPath="${modalTitleLinkParams.siteElementPath}">
													<h3>${title}</h3>
												</o:nav>
											</c:when>
											<c:otherwise>
												<h3>${title}</h3>
											</c:otherwise>
										</c:choose>
										<h4>${subtitle}</h4>
									</div>
									
									<div>
										<ui:section key="besideModalTitle" evaluate="${sectionsMap}" />
									</div>
									
								</div>
							</div>
							
							<div class="crd--more__body">
								<c:if test="${description.populated}">
									<div class="margin--b--xl">
										<h3 class="heading--underline h4"><orbis:message code="i18n.ec_publicRecordCommon.Description" /></h3>
										<p>${description}</p>
									</div>
								</c:if>
								
								${tagBody}
							</div>
							
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<%-- adding perfect scrollbar.
			Needs to be updated when the modal is opened so that it can get a proper height.
			Run as an inline function so that it can keep it's own scope not to conflict with other instances of the expTranscriptCard without having to pollute the DOM with random variables --%>
		<script type="text/javascript">
			(() => {
				const ps = $("#${id}_modal_psContainer").perfectScrollbar();
				$("#${id}_modal").on("shown", () => {
					ps.update();
				});
			})()
		</script>
	</c:if>
	
</li>