<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>

<c:set var="format" scope="page">${showDate ? orbisDateShort : ""}${showDate && showTime ? " " : ""}${showTime ? orbisTimeShort : ""}</c:set>
<c:set var="formatJS" scope="page">${showDate ? "orbisAppLocalized.dateTimeFormats.php.dateShort" : "''"} + '${showDate && showTime ? " " : ""}' + ${showTime ? "orbisAppLocalized.dateTimeFormats.php.timeShort" : "''"}</c:set>

<c:if test="${!readOnly}">
	<div id="${groupId}" class="input__group ${classes}" style="${groupCss}">
		<label id="${id}_label" class="label ${not empty value ? 'has--value' : ''} ${labelClasses}" for="${id}">${tagBody}${required ? '*' : ''}</label>
		
		<div class="display--flex" style="align-items: center">
			<input class="input--box js--datepicker has--btn ${required ? 'required' : ''} ${inputClasses}" type="text" name="${name}" id="${id}" placeholder="${placeholder}" value="${o:formatDate(value, format)}" ${disabled ? "DISABLED" : ""} ${dynamicElementAttrs} />
			
			<button type="button" aria-labelledby="${id}_label" onclick="$('#${id}').focus()" class="btn__default btn--info display--flex align--middle has--input is--pill is--left">
				<i class="material-icons">${showDate ? 'date_range' : 'query_builder'}</i>
			</button>
		</div>
		
		<c:if test="${not empty helpText}">
			<p class="text--help">${helpText}</p>
		</c:if>
		<input type="hidden" name="${name}_dateFormat" id="${name}_dateFormat" value="${format}" />
	</div>
	
	<script type="text/javascript">
		function loadDatepicker${id}()
		{
			var params = orbisAppSr.getComponentConfig("datepicker", $.extend({
				<c:if test="${not empty value}">
					value: '${o:formatDate(value, format)}',
				</c:if>
				format : ${formatJS},
				datepicker : ${showDate},
				timepicker : ${showTime},
				defaultSelect: false,
				onChangeDateTime : function(){${not empty onSelect ? onSelect : ''}},
				onShow : function(date, el){
					orbisAppSr.newTopLayer($(this));
				},
				onClose : function(){${not empty onClose ? onClose : ''}}
				<c:if test="${not empty startDate}">
					,minDate : "${startDate}"
				</c:if>
				<c:if test="${not empty endDate}">
					,maxDate : "${endDate}"
				</c:if>
			}, ${overrideParams}));
			
			$.datetimepicker.setLocale(params.lang);
			
			$("#${id}").datetimepicker(params);
		}
	</script>
	
	<orbis:addComponent component="datepicker" callback="loadDatepicker${id}();" />
</c:if>

<c:if test="${readOnly}">
	<div id="${groupId}" class="input__group ${classes}">
		<p class="label--disabled  protip" data-pt-classes="tip--default" data-pt-title="This field is not editable">${tagBody}</p>
		<p>
			${o:formatDate(value, format)}
		</p> 
	</div>
</c:if>