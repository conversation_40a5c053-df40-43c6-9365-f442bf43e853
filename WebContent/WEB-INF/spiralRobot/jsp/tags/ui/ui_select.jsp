<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>

<label class="label ${labelClasses} ${inTable ? 'in--table' : ''}" style="${labelCss}">
	${title}${required ? '*' : ''}
</label>

<div class="select ${inTable ? '' : ' margin--b--xxl'} ${containerClasses}" style="${css}">
	<select id="${id}" name="${name}" size="${size}" onchange="${onchange}" class="js--ui-select sel_TestValidationStatus ${required ? 'required' : ''} ${classes}" ${dynamicElementAttrs} ${disabled ? 'DISABLED' : ''} ${multiple ? 'MULTIPLE' : ''}>
		<c:if test="${includeEmptyOption}">
			<option class="emptyOption" value="">${emptyOptionLabel}</option>
		</c:if>
		<c:forEach var="child" items="${children}">
			<c:if test="${child.optionTag}">
				<option 
					id="${child.id}" 
					class="${child.classes}" 
					aria-label="${child.ariaLabel}" 
					value="${child.value}" 
					${child.selected || not empty value && value == child.value ? 'SELECTED' : ''}
					${child.hidden ? 'hidden' : ''}
					${child.tagAttributes.passthroughAttributes}
					>
					${child.tagBody}
				</option>
			</c:if>
			<c:if test="${!child.optionTag}">
				<optgroup label="${child.tagAttributes.title}" ${child.tagAttributes.passthroughAttributes}>
					<c:forEach var="child" items="${child.tagAttributes.children}">
						<option 
							id="${child.id}" 
							class="${child.classes}" 
							aria-label="${child.ariaLabel}" 
							value="${child.value}" 
							${child.selected || not empty value && value == child.value ? 'SELECTED' : ''}
							${child.hidden ? 'hidden' : ''}
							${child.tagAttributes.passthroughAttributes}
							>
							${child.tagBody}
						</option>
					</c:forEach>
				</optgroup>
			</c:if>
		</c:forEach>
	</select>
	<div class="select__arrow ${arrowClasses}"></div>
	<c:if test="${not empty helpText}">
		<p class="text--help ${helpTextClasses}" style="${helpTextCss}">${helpText}</p>
	</c:if>
</div>

<script type="text/javascript">
	$('#${id}').uiSelect(${uiSelectConfig});
</script>