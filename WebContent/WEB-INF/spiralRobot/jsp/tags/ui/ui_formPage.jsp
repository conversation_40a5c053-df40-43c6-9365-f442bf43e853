<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<c:set var="hasGroups" value="${not empty groups}" />

<c:if test="${empty saveLabel}">
	<orbis:message var="saveLabel" code="i18n.common.save"/>
</c:if>
<c:if test="${empty cancelLabel}">
	<orbis:message var="cancelLabel" code="i18n.common.cancel"/>
</c:if>

<c:if test="${not empty siteElement}">
	<c:set var="url" scope="page">${siteElement.fullPath}.htm</c:set>
</c:if>

<c:set var="finalSaveOnclick">$('#${formId}').submit();</c:set>

<c:if test="${not empty saveOnclick}">
	<c:set var="finalSaveOnclick">${saveOnclick}</c:set>
</c:if>

<div id="${id}" class="ui--form-page">

	<nav class="display--flex dist--between form-page__controls margin--b--m js--form_page--main_controls">
		<div class="display--flex">
		
			<ui:section key="formButtons" evaluate="${sectionsMap}">
				
				<ui:button type="success" size="large" onclick="${finalSaveOnclick}" classes="margin--r--s">${saveLabel}</ui:button>
				
				<c:if test="${not empty cancelAction}">
					<ui:button type="info" size="large" action="${cancelAction}">${cancelLabel}</ui:button>
				</c:if>
				
				<c:if test="${not empty cancelOnclick}">
					<ui:button type="info" size="large" onclick="${cancelOnclick}">${cancelLabel}</ui:button>
				</c:if>
			</ui:section>
		
		</div>
	
		<c:if test="${hasGroups}">
			<ui:button type="info" size="large" classes="btn__hero btn--info  margin--l--m" show="formPageNav_${id}" css="padding:16px 24px;">
				<i class="material-icons">list</i>
			</ui:button>
		</c:if>
	
	</nav>
	
	<nav class="form-page__controls sticky js--form_page--sticky_controls" style="z-index: 10000000002;">
		<ui:button type="info" style="plain" classes="margin--r--m sel_Top" onclick="$('#${id}').uiFormPage('backToTop');">
			<i class="material-icons">arrow_upward</i>
		</ui:button>
		
		<ui:section key="formButtons" evaluate="${sectionsMap}">
			<ui:button type="success" onclick="${finalSaveOnclick}" >${saveLabel}</ui:button>
			
			<c:if test="${not empty cancelAction}">
				<ui:button type="info" action="${cancelAction}">${cancelLabel}</ui:button>
			</c:if>
			
			<c:if test="${not empty cancelOnclick}">
				<ui:button type="info" onclick="${cancelOnclick}">${cancelLabel}</ui:button>
			</c:if>
		</ui:section>
		
		<ui:button type="info" style="plain" classes="margin--l--m ${hasGroups ? '' : 'display--none'}" show="formPageNav_${id}">
			<i class="material-icons">list</i>
		</ui:button>
	</nav>

	<c:set var="showDescriptionSection" value="${!currentTag.isConfigPage || not empty sectionsMap.formDescriptions}" />
	
	<ui:form id="${formId}" siteElement="${siteElement}" get="${get}" autocomplete="${autocomplete}" ajaxForm="${ajaxForm}" target="${target}" url="${url}" name="${formName}" onBeforeSubmit="${onBeforeSubmit}" removeEnctype="${removeEnctype}" classes="${formClasses}" css="${formCss}" callback="${callback}">
		<ui:grid>
			<ui:gridCol width="${showDescriptionSection ? 8 : 12}" classes="form-page">
				${tagAttributes.action.hiddenInputs}
				
				${tagBody}
	
			</ui:gridCol>
		
			<c:if test="${showDescriptionSection}">
				<ui:gridCol width="4" classes="padding--t--none">
					<ui:section key="formDescriptions" evaluate="${sectionsMap}"></ui:section>
				</ui:gridCol>
			</c:if>
		</ui:grid>
	</ui:form>
</div>

<c:if test="${hasGroups}">
	<ui:sidebar id="formPageNav_${id}" i18n_title="i18n.ui_formPage.FormNaviga3120201630482183">
		
		<ul class="js--config-list-group list--plain" id="${group.id}">
			<c:forEach items="${groups}" var="group">
				<li class="margin--b--s">
					<a id="${item.id}" href="javascript:void(0)" onclick="$('#${id}').uiFormPage('goToSection', '${group.id}');">
						${group.tagAttributes.title}
					</a>
				</li>
			</c:forEach>
		</ul>
		
	</ui:sidebar>
</c:if>

<script type="text/javascript">

	$("#${id}").uiFormPage(${formPageParams});
	
</script>
