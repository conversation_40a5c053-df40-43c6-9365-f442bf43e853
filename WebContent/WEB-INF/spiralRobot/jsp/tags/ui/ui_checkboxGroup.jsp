<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>

<c:if test="${!readOnly}">
	<%-- Position relative div here is to capture the error tooltip --%>
	<div class="position--relative">
		<div id="${id}" class="ui__checkbox--group margin--t--l js__checkbox-group js--error-placement ${border ? 'border' : ''} ${classes}" style="${css}">
			<fieldset>
				<legend class="h4 ui__checkbox--group__title">
					<span class="">${title}${required || not empty minSelected ? '*' : ''}</span>
				</legend>

				<%-- filter textbox --%>
				<c:if test="${showFilter}">
					<input type="text" class="input--box ui__checkbox--group__filter--textbox js__filter--textbox" placeholder="Filter options" />
				</c:if>

				<%-- main checkbox group body, has checkboxes and empty state --%>
				<div class="ui__checkbox--group__body js__checkbox--group__body">
					<div class="ui__checkbox--group__body__empty--state label label--p display--none js__empty--state">
						Your filter doesn't match any of the options
					</div>

					${tagBody}
				</div>

				<hr />

				<div class="ui__checkbox--group__footer">
					<%-- sidebar toggle button --%>
					<c:if test="${showSidebar}">
						<button type="button"
								class="btn__small--text btn--info plain align--middle ui__checkbox--group__sidebar--control js__sidebar--toggle">
							<i class="material-icons">launch</i>
						</button>
					</c:if>

					<%-- selection stats --%>
					<div class="checked--count font--12 ${showSidebar ? 'sidebar--enabled' : ''}">
						<span class="js__selected--count"></span> of <span class="js__visible--count"></span>

						<span class="js__is--filtered display--none">
							( filtered )
						</span>
					</div>

					<%-- select all toggle --%>
					<ui:toggle classes="ui__checkbox--group__select--all js__select--all">
						All
						<span class="js__is--filtered display--none">
							(<span class="js__visible--count"></span>)
						</span>
					</ui:toggle>
				</div>
			</fieldset>
		</div>
	</div>
</c:if>

<c:if test="${readOnly}">
	<div class="input__group margin--t--l ui--checkbox-group">
		<ul class="list--plain">
			<li>
				<p class="label--disabled protip" data-pt-classes="tip--default" data-pt-title="These fields are not editable">${title}</p>
			</li>
				${tagBody}
		</ul>
	</div>
</c:if>

<c:if test="${not empty helpText}">
	<p class="text--help">${helpText}</p>
</c:if>

<c:if test="${showSidebar}">
	<%-- Ignoring the forceSidebarToBody prop in the new UI due to the redesign always having checkboxes in the form. Not forcing it to the body will duplicate the values. --%>
	<ui:sidebar id="${id}_sidebar" i18n_title="${title}${required ? '*' : ''}" removeContentPadding="true" alwaysInBody="true">
		<%-- filter textbox --%>
		<c:if test="${showFilter}">
			<ui:section key="overrideHeader">
				<div class="ui__checkbox--group__sidebar__header">
					<input type="text" class="input--box js__filter--textbox" placeholder="Filter options" />
				</div>
			</ui:section>
		</c:if>

		<%-- sidebar checkbox group body, has checkboxes and empty state --%>
		<div class="ui__checkbox--group__sidebar__body js__checkbox--group__body">
			<div class="ui__checkbox--group__sidebar__body__empty--state label label--p display--none js__empty--state">
				Your filter doesn't match any of the options
			</div>

			${tagBody}
		</div>

		<ui:section key="footer">
			<div class="ui__checkbox--group__sidebar__footer">
				<%-- selection stats --%>
				<div class="checked--count font--12">
					<span class="js__selected--count"></span> of <span class="js__visible--count"></span>

					<span class="js__is--filtered display--none">
						( filtered )
					</span>
				</div>

				<%-- select all toggle --%>
				<ui:toggle classes="js__select--all">
					All
					<span class="js__is--filtered display--none">
						(<span class="js__visible--count"></span>)
					</span>
				</ui:toggle>
			</div>
		</ui:section>
	</ui:sidebar>
</c:if>

<script type="text/javascript">
    $("#${id}").uiCheckboxGroup({
		validation: ${validationOptions}
    });
</script>