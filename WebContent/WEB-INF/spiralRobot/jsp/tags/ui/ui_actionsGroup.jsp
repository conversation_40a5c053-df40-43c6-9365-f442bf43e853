<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<%@ page import="com.orbis.utils.tags.ui.I18NAttribute"%>
<%@ page import="com.orbis.utils.tags.ui.ActionAttribute"%>
<%@ page import="com.orbis.utils.tags.ui.UITagHelper"%>
<%@ page import="java.lang.String"%>

<c:set var="useSideBarActionsGroup" value="${o:getConfig('USE_SIDE_BAR_ACTIONS_GROUP') == '1' && o:isUnderMyAccountTemplate(siteElement)}" />

<c:if test="${!useSideBarActionsGroup}">
	<button type="button" id="${id}" style="z-index: 223; ${css}" aria-label="${title}" class="btn btn--menu--circular always--circular color--bg--default js--interaction-btn display--block sel_ActionButtonTest ${classes} ${tagBody.isBlank() ? 'display--none' : ''}" ${dynamicAttributes}>
		<i class="material-icons">${icon}</i>
	</button>

	<nav id="${id}_nav" class="nav--interaction color--bg--default js--interaction-menu actions-group--transform-into--sidebar in--data-explorer">
		<h6 aria-hidden="true" class="nav--interaction__title margin--b--xl">${title}</h6>
		<div class="nav--interaction__list" aria-controls="${id}">
			${tagBody}
		</div>
	</nav>
</c:if>

<c:if test="${useSideBarActionsGroup}">
	
	<div id="${id}_nav_container" class="${tagBody.isBlank() ? 'display--none' : 'display--flex margin--b--m padding--l--m padding--r--m'}">
		<nav id="${id}_nav" class="nav--interaction border-radius--16 shadow--box--none padding--a--m position--static color--bg--default margin--a--none width--100" style="transform: scale(1) !important; opacity: 1 !important; display: inherit !important;">
			<div class="nav--interaction__list">
				${tagBody}
			</div>
		</nav>
	</div>
	
	<script type="text/javascript">
		$("#${id}_nav_container *").removeAttr("tabindex");
		$("#actionsGroupContainer").append($("#${id}_nav_container"));
		$("#${id}_nav").addClass("is--visible");
	</script>
	
</c:if>
