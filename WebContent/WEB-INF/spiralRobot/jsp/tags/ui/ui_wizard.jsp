<%@ include file="/WEB-INF/jsp/include.jsp"%>

<ui:toDo>
	Add an overall progress bar for the entire wizard
	Move style block to an external css file
	Remove the script tag thats removing the header and the navigation once we figure out how we're going to display the wizard
</ui:toDo>

<script type="text/javascript">
	$(".navigation--mobile, header").css("display", "none");
	$("main").removeClass("main--alt");
	$(document).ready(function(){
		$("footer.footer--bottom").removeClass("display--flex").css("display", "none");
		//const ps = $('.js--wizard-vue-container').perfectScrollbar();
	});
</script>

<style type="text/css">
	.wizard-step-group, .wizard-step {
		display: none;
		top: 0;
		left: 0;
		width: 100%;
	}
	
	.current-group, .current-step {
		display: block;
	}
</style>

<ui:toDo>
	This is the template defining each progress item in the progress bar.
	Currently the line connecting each step shows up even if the next step has the class "display--none" on it (add is--last to the last visible li)
	The title of the step can be surrounded by a span or an anchor, this might need some restyling since that wasn't originally in the spec
	Also added a skipped state that wasn't originally in the spec, might want to check my choice of icon for it and add whatever other behaviours for this new state
</ui:toDo>
<script type="text/x-template" id="progressItemTemplate">
	<li
		v-bind:class="{ 'is--todo' : incomplete && !current || skipped, 'is--active' : current, 'is--complete' : completed && !current, 'display--none' : !isVisible}"
		class="wizard__progress-list__item display--flex align--middle margin--b--xl">
		
		<i class="material-icons margin--r--m">
			<template v-if="incomplete && !current">radio_button_unchecked</template>
			<template v-if="current">radio_button_checked</template>
			<template v-if="completed && !current">assignment_turned_in</template>
			<template v-if="skipped && !current">fast_forward</template>
		</i>
		<a v-if="${id != 'applyWizard'} && (completed || skipped)" href="javascript:void(0);" v-on:click="showStep" class="wizard__progress-list__item-title">{{title}}</a>
		<span v-if="${id == 'applyWizard'} || !(completed || skipped)" class="wizard__progress-list__item-title">{{title}}</span>
	</li>
</script>

<ui:toDo>
	This is the template defining the groups in the progress bar.
	The structure put in was just for prototyping, Josh will need to check this out and style it, changing the structure if needed
</ui:toDo>

<script type="text/x-template" id="progressGroupTemplate">
	<div v-bind:class="{'is--active' : currentGroup, 'display--none' : !isVisible}">
		<h3 class="color--font--white">{{title}}</h3>
		<ul class="list--plain wizard__progress-list display--flex">
			<progress-item :key="stepId"
				v-for="(step, stepId) in steps"
				v-bind:title="step.title"
				v-bind:status="step.status"
				v-bind:is-visible="step.isVisible"
				v-bind:step="stepId"
				v-bind:group="id"
				v-bind:current-step="currentStep"
			></progress-item>
		</ul>
	</div>
</script>

<div id="${id}" class=" js--ui-wizard">
	
	<div class="wizard-form" style="padding-bottom:68px;">
		<c:if  test="${empty formId}">
			<c:set var="formId">${id}__form</c:set>
		</c:if>
		<ui:form id="${formId}"  classes="js--ui-wizard-form" siteElement="${siteElement}" onBeforeSubmit="${onBeforeSubmitAction.processedOnclick}">
			${action.hiddenInputs}
			<input type="hidden" class="js--wizard-state-id" name="wizardStateId" value="${wizardStateId}" />
			
			<div class="wizard-step-group-container position--relative">
				${tagBody}
			</div>
			<ui:button classes="js--ui-wizard-prev-btn margin--r--m" size="large" type="info" onclick="$('#${id}').uiWizard('previousStep');" css="display: none;">
				<orbis:message code="i18n.ui_wizard.Previous" />
			</ui:button>
			<ui:button classes="js--ui-wizard-skip-btn margin--r--m" size="large" type="info" onclick="$('#${id}').uiWizard('skipStep');" css="display: none;">
				<orbis:message code="i18n.ui_wizard.Skip" />
			</ui:button>
			<ui:button classes="js--ui-wizard-next-btn margin--r--m" size="large" type="${id == 'applyWizard' ? 'info' : 'success'}" onclick="$('#${id}').uiWizard('nextStep');" css="display: none;">
				<orbis:message code="i18n.ui_wizard.Next" />
			</ui:button>
			<ui:toDo>
				Right now the finish button will submit the form.
				Once we turn the wizard into an overlay, we're going to have to turn the form into an ajax form and close the overlay in the finish action
			</ui:toDo>
			<ui:button classes="js--ui-wizard-finish-btn margin--r--m" size="large" type="${id == 'applyWizard' ? 'info' : 'success'}" onclick="$('#${id}').uiWizard('finish');" css="display: none;">
				<orbis:message code="i18n.ui_wizard.Finish" />
			</ui:button>
			<c:if test="${not empty cancelAction && id == 'applyWizard'}">
				<ui:button classes="js--ui-wizard-cancel-btn" size="large" type="info" onclick="${cancelAction.processedOnclick}">
					<orbis:message code="i18n.ui_wizard.Cancel" />
				</ui:button>
			</c:if>
			<c:if test="${empty cancelAction && id == 'applyWizard'}">
				<ui:button classes="js--ui-wizard-cancel-btn" size="large" type="info" action="displayHome" i18n_confirmOnclick="i18n.ui_wizard.AreYouSureCancel">
					<orbis:message code="i18n.ui_wizard.Cancel" />
				</ui:button>
			</c:if>
		</ui:form>
	</div>
	<div class="wizard__nav--mobile  padding--a--xs">
		
		<button type="button" class="btn__hero btn--white plain width--100" onclick="$('.js--wizard-vue-container').toggleClass('is--open--mobile');"><orbis:message code="i18n.ui_wizard.WizardMenu" /></button>
	
		
	</div>
	<div class="wizard js--wizard-vue-container">
		<div class="wizard__title--mobile align--middle dist--between">
			<h2>{{title}}</h2>
			
			<orbis:message var="cancelWizardText" code="i18n.ui_wizard.CancelWizard" />
			<c:if test="${not empty cancelAction}">
				<ui:button size="small" aria-label="${cancelWizardText}" css="font-size: 8px; font-weight: 400;" onclick="${cancelAction.processedOnclick}">
					<i class="material-icons">close</i>
				</ui:button>
			</c:if>
			
			<c:if test="${empty cancelAction}">
				<ui:button action="displayHome" size="small" aria-label="${cancelWizardText}" css="font-size: 8px; font-weight: 400;">
					<i class="material-icons">close</i>
				</ui:button>
			</c:if>

		</div>
		<button class="wizard__btn--hide-mobile-nav" onclick="$('.js--wizard-vue-container').toggleClass('is--open--mobile');"><orbis:message code="i18n.ui_wizard.close" /></button>
		<h2 class="h3 color--font--white display--flex align--middle wizard__title">
			<c:if test="${not empty LOGO_LEFT_LOCATION}">
				<img src="${LOGO_LEFT_LOCATION}" alt="${sessionScope.isL1 ? LOGO_LEFT_ALT_TEXT : L2_LOGO_LEFT_ALT_TEXT}" class="margin--r--s" style="max-height: 60px; max-width: 60px;" />
			</c:if>
			<span class="display--flex column" style="flex-direction: column; align-items: flex-start; padding-left: 4px; padding-top: 4px;">
			
				<c:if test="${not empty cancelAction && id != 'applyWizard'}">
					<ui:button onclick="${cancelAction.processedOnclick}" size="small" classes="btn--white outline" css="font-size: 8px; font-weight: 400;">
						<orbis:message code="i18n.ep_postingEdit_wizard.cancel0106168483275308" />
					</ui:button>
				</c:if>
				
				<c:if test="${empty cancelAction && id != 'applyWizard'}">
					<ui:button action = "displayHome" size = "small" classes="btn--white outline" css="font-size: 8px; font-weight: 400;">
						<orbis:message code="i18n.ep_postingEdit_wizard.cancel0106168483275308" />
					</ui:button>
				</c:if>
				
				<span class="${id == 'applyWizard' ? 'padding--a--m' : ''}">{{title}}</span>
			</span>
		</h2>

		<ui:toDo>
			The progress sidebar needs to be scrollable. Once that's working, it will also have to scroll automagically based off the currently visible step
		</ui:toDo>
		<div class="wizard__body padding--a--m">
			<c:choose>
				<c:when test="${id == 'applyWizard'}">
					<div v-if="instructions" aria-live="polite" class="note margin--t--xl margin--b--xxxl">
						<h5 class="note__title">{{instructionsTitle}}</h5>
						<div class="note__content" v-html="instructions">
						</div>
					</div>
				</c:when>
				<c:otherwise>
					<div v-if="instructions" aria-live="polite" class="note success margin--t--xl margin--b--xxxl" style="background: rgba(0, 0, 0, 0.2);">
						<h5 class="note__title">{{instructionsTitle}}</h5>
						<div class="note__content">
							<p v-html="instructions" class="color--font--white" style="color: #fff !important;">
							</p>
						</div>
					</div>
				</c:otherwise>
			</c:choose>
			<c:set var="ordinal" value="4" />		
			<div class="wizard__progress">
				<progress-group :key="id"
					v-for="(group, id) in groups"
					v-bind:title="group.title"
					v-bind:id="id"
					v-bind:steps="group.steps"
					v-bind:is-visible="group.isVisible"
					v-bind:current-step="currentStep"
					v-bind:current-group="currentGroup"
				></progress-group>
			</div>
		</div>	

	</div>
</div>




<orbis:addComponent component="uiWizard" callback="$('#${id}').uiWizard(${pluginOpts});" />