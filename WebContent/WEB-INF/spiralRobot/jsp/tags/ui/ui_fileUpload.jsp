<%@ include file="/WEB-INF/jsp/include.jsp"%>
<%@ page import="com.orbis.web.spiralRobot.SpiralRobot" %>
<c:set var="uniqueId" scope="page" value="${o:generateId()}"/>
<c:set var="initFunctionName" scope="page">initFileUpload_${o:generateId()}</c:set>

<style type="text/css">
	.progress {
		width: 100%;
		height: 4px;
		border-radius: 0;
	}
	
	a.btn-retry.disabled {
		pointer-events: none;
	}
	
	.row--flex {
		display: flex; align-items: center;
	}
</style>

<c:set var="buttonLabel" scope="page">${buttonTitle}</c:set>

<ui:note i18n_title="i18n.fileUpload.FileUpload1963264334481637">
	<c:if test="${!buttonOnly}">
		<p class="label" style="margin-top:0 !important;">${tagBody}</p>
		<div id="currentFileInfo_${id}">
			<label class="label" style=" margin-bottom: 0; flex-basis:100%; min-width:100%;">
				<span class="display--block"><orbis:message code="i18n.question_questionFileUpload.CurrentFile" /></span>
			</label>
			<c:if test="${not empty existingFileName && fn:length(existingFileName) > 32}">
				<c:set var="existingFileNameLength" value="${fn:length(existingFileName)}" />
				<c:set var="existingFileName">${fn:substring(existingFileName, 0, 22)}...${fn:substring(existingFileName, existingFileNameLength - 8, existingFileNameLength)}</c:set>
			</c:if>
			<div class="display--flex align--middle margin--b--s">
				<a id="fileLink_${id}" href="javascript:void(0);" onclick="orbisAppSr.buildForm({action:'<o:encrypt action="downloadFileUpload" />', uuid:'${existingFileUUID}'}).submit();">
					${existingFileName}
				</a>
				<a id="deleteFile_${id}" href="javascript:void(0);" class="margin--l--xs display--flex">
					<i class="material-icons" title="<orbis:message code="i18n.question_questionFileUpload.Delete" />">delete</i>
				</a>
			</div>
		</div>
	</c:if>
	
	<div class="input__group" style="flex-basis:1005; min-width:100%;">
		<input type="hidden" name="removeFile_${id}" value="false" />
		<input type="hidden" id="inputFileUpload_${id}" name="${name}" class="${required ? 'required' : ''} validateHiddenField ${inputClasses}" value="${existingFileUUID}" />
		<ui:button id="btn_fileUploadDialog_${id}" classes="btn__default btn--info row--flex" show="fileUploadDialog_${id}" disabled="${disabled}">
			<i class="material-icons">file_upload</i>
			<span>${buttonLabel}</span>
		</ui:button>
	</div>
	
	<c:if test="${not empty helpText}">
		<p class="text--help">${helpText}</p>
	</c:if>
</ui:note>

<ui:modal id="fileUploadDialog_${id}" i18n_title="Choose a File">
	<c:if test="${not empty formats}">
		<ui:note>
			<h6 style="margin-bottom: 10px; margin-top: 0;"><orbis:message code="i18n.fileUpload.SupportedFormats" /></h6>
			<span id="supportedFormats_${id}">
				<c:if test="${formats != '/'}">
					*.<c:forEach var="f" items="${fn:split(formats, ',')}" varStatus="status">${f}<c:if test="${!status.last}">, *.</c:if></c:forEach>
				</c:if>
			</span>
		</ui:note>
	</c:if>
	<div class="row--flex">
		<label><orbis:message code="i18n.fileUpload.MaxFileSize" /></label><label id="maxFileSize_${id}" style="margin-left: 5px;"></label>
	</div>
	<div class="input__group row--flex" style="justify-content: space-between; margin-bottom: 0; background: #f1f1f1; padding: 12px;">
		<div style="display: flex; align-items: center;">
			<input type="hidden" id="fileUploadAction_${id}" value="<o:encrypt action="${type != 'profileImage' ? 'ajaxUploadFile' : 'ajaxUploadProfileImage'}" />" />
			<input type="hidden" id="fileDeleteAction_${id}" value="<o:encrypt action="ajaxDeleteFile" />" />
			<input type="hidden" id="fileDownloadAction_${id}" value="<o:encrypt action="downloadFileUpload" />" />
			<input type="file" id="fileUpload_${id}" class="orbisFileUpload file-upload ${classes}" data-multiple-caption="{count} files selected" ${multiple ? 'MULTIPLE' : ''} />
			<label for="fileUpload_${id}" class="btn__default btn--info">
				<i class="material-icons">file_upload</i>
				<span id="fileUploadLabel_${id}"><orbis:message code="i18n.fileUpload.Chooseafile" /></span>
			</label>
			<label style="margin-left: 5px; margin-bottom: 0; display: none;">Size: <span id="uploadSize_${id}"></span></label>
			<div id="uploadProgressIcon_${id}" style="margin-left: 5px;"></div>
		</div>
		<div class="row--flex">
			<ui:button id="retryFile_${id}" type="defaultt" css="margin-left: 5px; display: none;"><orbis:message code="i18n.common.retry" /></ui:button>
			<ui:button id="cancelFile_${id}" type="info" css="margin-left: 5px; display: none;"><orbis:message code="i18n.common.cancel" /></ui:button>
		</div>
	</div>
	<progress id="uploadProgress_shieldStrength_${id}" value="0" max="100" class="shieldStrength display--none">
		<div id="uploadProgress_${id}" class="display--none">
			<div class="bar"></div>
		</div>
	</progress>
	<c:if test="${integration}">
		<!--  todo: update UI -->
		<input type="hidden" id="tableType_${id}" name="tableType" value="${type}">
		<br />
		<div class="row-flex row-fluid">
			<div class="span3"><label><orbis:message code="i18n.fileUpload.Delimeter" /></label></div>
			<div class="span9"><input type="text" id="delimeter_${id}" name="delimeter"></div>
		</div>
		<div class="row-flex row-fluid">
			<div class="span3"><label><orbis:message code="i18n.fileUpload.PrimaryKey" /></label></div>
			<div class="span9"><input type="text" id="pk_${id}" name="pk"></div>
		</div>
		<div class="row-flex">
			<button id="btnSubmit_${id}" type="submit" class="btn btn-primary btn-small"><orbis:message code="i18n.fileUpload.Upload" /></button>
		</div>
	</c:if>
	<p></p>
</ui:modal>

<script type="text/javascript">
	var controllerPath = "${siteElement.fullPath}.htm";
	var formats = [
		<c:if test="${not empty formats}">
			<c:set var="fileFormats" value="${fn:split(formats, ',')}" />
			<c:forEach var="f" items="${fileFormats}">
				"${f}",
			</c:forEach>
		</c:if>
	];
	
	function ${initFunctionName}() {
		$("#maxFileSize_${id}").html(orbisFileUpload.getReadableFileSize(${maxSize}));
		
		<c:if test="${empty existingFileUUID}">
			$("#currentFileInfo_${id}, #newFileLabel_${id}").addClass("display--none");
		</c:if>
		
		<c:if test="${image}">
			var $uploadImage = $("#fileUploadImage_${id}");
			if (!$uploadImage.data("defaultUrl")) {
				$uploadImage.data("defaultUrl", "${defaultImage}");
			}
			if (!$uploadImage.attr("src")) {
				$uploadImage.attr("src", $uploadImage.data("defaultUrl"));
			}
		</c:if>
		
		orbisFileUpload.init('${id}', '${uploadDirectory}', '${existingFileName}', '${formats}', ${maxSize}, ${autoSubmit});
	}
</script> 

<orbis:addComponent component="orbisFileUpload" callback="${initFunctionName}();" />