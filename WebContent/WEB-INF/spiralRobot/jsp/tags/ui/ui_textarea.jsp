<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>


<c:if test="${!readOnly}">
	<c:choose>
		<c:when test="${fn:contains(classes, 'display--none')}">
			<label class="display--none margin--t--l label ${labelClasses}" for="${id}">${title}${required ? '*' : ''}</label>
		</c:when>
		<c:otherwise>
			<label class="display--block margin--t--l label ${labelClasses}" for="${id}">${title}${required ? '*' : ''}</label>
		</c:otherwise>
	</c:choose>	
	
	<c:choose>
		<c:when test="${type == 'richBasic'}">
			<div class="input__group ${classes}">
				<textarea name="${name}" id="${id}" maxLength="${maxLength}" class="${fieldClasses}" ${dynamicElementAttrs}>${tagBody}</textarea>
				<c:if test="${not empty helpText}">
					<p class="text--help">${helpText}</p>
				</c:if>
			</div>
			
			<script type="text/javascript">
				function loadEditor${id}()
				{
					orbisAppSr.safelyDestroyOrbisEditor("${id}");
					
					var $parentModal = $("#${id}").parents(".js--ui-modal");
					
					if($parentModal.length)
                    {
                        $parentModal.on("shown", function(){
                            const editor = $("#${id}").ckeditor(orbisAppSr.getComponentConfig("ckeditor"));
							if (editor.editor) {
								orbisAppSr.applyCkeditorDialogHandler(editor.ckeditorGet());
							}
                        });
                        
                        $parentModal.on("hidden", function(){
                            orbisAppSr.safelyDestroyOrbisEditor("${id}");
                        });
                    }
                    else
                    {
                    	const ckEditorConfig = orbisAppSr.getComponentConfig("ckeditor");
                    	ckEditorConfig.baseFloatZIndex = 2000000000;
                        CKEDITOR.replace("${id}", ckEditorConfig);
                    }
				}
			</script>
			
			<orbis:addComponent component="ckeditor"/>
			<orbis:addComponent component="ckeditor_jqueryAdapter" callback="loadEditor${id}();"/>
		</c:when>
		<c:when test="${type == 'rich'}">
		
			<div class="input__group ${classes}">
				<textarea name="${name}" id="${id}" maxLength="${maxLength}" class="${fieldClasses}" ${dynamicElementAttrs}>${tagBody}</textarea>
				<c:if test="${not empty helpText}">
					<p class="text--help">${helpText}</p>
				</c:if>
			</div>
			
			<script type="text/javascript">
				function loadEditor${id}()
				{
					orbisAppSr.safelyDestroyOrbisEditor("${id}");
					
					var $parentModal = $("#${id}").parents(".js--ui-modal");
					
					if($parentModal.length)
                    {
                        $parentModal.on("shown", function(){
                            const editor = $("#${id}").ckeditor(orbisAppSr.getComponentConfig("ckeditor", {toolbar : 'default'}));
                            if (editor.editor) {
								CKFinder.setupCKEditor(editor.ckeditorGet(), "/core/ckfinder/");
								orbisAppSr.applyCkeditorDialogHandler(editor.ckeditorGet());
							}
                        });
                        
                        $parentModal.on("hidden", function(){
                            orbisAppSr.safelyDestroyOrbisEditor("${id}");
                        });
                    }
                    else
                    {
                    	const ckEditorConfig = orbisAppSr.getComponentConfig("ckeditor");
                    	ckEditorConfig.toolbar = 'default';
                    	ckEditorConfig.baseFloatZIndex = 2000000000;
                        CKEDITOR.replace("${id}", ckEditorConfig);
                    }
				}
			</script>
			
			<orbis:addComponent component="ckeditor"/>
			<orbis:addComponent component="ckeditor_jqueryAdapter" callback="loadEditor${id}();"/>
		</c:when>
		
		<c:when test="${type == 'text'}">
		
			<div id="${containerId}" class="input__group ${classes}">
				<textarea name="${name}" id="${id}" maxLength="${maxLength}" class="width--100 ${required ? 'required' : ''} ${fieldClasses}" style="min-height: 144px; ${fieldCss}" ${dynamicElementAttrs}>${tagBody}</textarea>
				<c:if test="${not empty helpText}">
					<p class="text--help">${helpText}</p>
				</c:if>
			</div>
			
		</c:when>
		
	</c:choose>
</c:if>

<c:if test="${readOnly}">
</c:if>