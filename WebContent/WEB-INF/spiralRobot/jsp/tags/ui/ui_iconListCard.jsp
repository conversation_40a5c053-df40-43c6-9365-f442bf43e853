<%@ include file="/WEB-INF/jsp/include.jsp"%>

<li id="${id}" class="list-card has--icon-and-label js--posting display--flex margin--b--l prevent--mobile-styles position--relative ${classes}" style="${css}">
	<c:if test="${not empty icon || subtitle.populated || not empty sectionsMap.iconArea}">
		<div class="list-card__img-container display--flex align--center position--relative ${darkMode ? 'color--bg--dark' : ''}">
			<span class=" display--flex align--middle">
				<ui:section key="iconArea" evaluate="${sectionsMap}">
					<i class="material-icons ${darkMode ? 'color--font--white' : ''}" style="font-size: 48px; color: #616161;">${icon}</i>
				</ui:section>
			</span>
			
			<c:if test="${subtitle.populated}">
				<span style="border-radius: 0 0 16px 16px; " class="list-card__type height--16">
					${subtitle}
				</span>
			</c:if>
		</div>
	</c:if>
	<div class="list-card__content-container display--flex wrap space--between padding--a--s" style="min-width:120px;">
		<div>
			<h6 class="margin--a--none margin--b--s display--flex align--middle dist--between width--100">
			
				<c:if test="${!currentTag.onclickPopulated}">
					${title}
				</c:if>
				
				<c:if test="${currentTag.onclickPopulated}">
					<a href="javascript:void(0)" onclick="${currentTag.processedOnclick}">${title}</a>
				</c:if>
				
			</h6>

			<div>
				${tagBody}
			</div>
		</div>
		
		<c:if test="${not empty sectionsMap.sideControls}">
			<div class="padding--r--m margin--b--m margin--t--m">
				<ui:section key="sideControls" evaluate="${sectionsMap}" />
			</div>
		</c:if>
	</div>
</li>