<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>

<c:set var="inSideBarActionsGroup" value="${o:getConfig('USE_SIDE_BAR_ACTIONS_GROUP') == '1' && o:isUnderMyAccountTemplate(siteElement) && currentTag.inActionsGroup}" />

<div id="${id}" class="drop-down js--ui-drop-down ${outerContainerClasses}" style="${outerContainerCss}">
	<button type="button" id="${id}-dropdown" class="${size} ${type} ${style} drop-down__btn ${isFloating ? 'js--floating-drop-down__btn' : 'js--drop-down__btn'} ${containerClasses} ${inSideBarActionsGroup ? 'display--flex' : ''} ${classes}" style="${css}" ${dynamicElementAttrs}>
		<c:if test="${!isIconDropdown}">
			<span class="js--drop-down-title ${title == 'Advanced Search' ? 'sel_AdvancedSearch' : '' } ${containerClasses} ${inSideBarActionsGroup ? 'text-align--left' : ''}">${title}</span>
			<i class="material-icons icon--drop-down">keyboard_arrow_down</i>
		</c:if>
		<c:if test="${isIconDropdown}">
			<i class="material-icons icon--drop-down">${title}</i>
		</c:if>
	</button>
	<ul aria-controls="${id}-dropdown" class="drop-down__list js--drop-down__list ${listClasses }">
		${tagBody}
	</ul>
</div>

<script type="text/javascript">
	orbisAppSr.dropdown.functions.init('${id}'${noSelect ? ', true' : ''});
</script>
