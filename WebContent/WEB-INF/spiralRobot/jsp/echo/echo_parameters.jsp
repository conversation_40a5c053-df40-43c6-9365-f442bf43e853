<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>

<c:if test="${not empty params}">
	<table class="table is--list width--80">
		<caption class="table__caption">Parameters</caption>
		<thead class="table__header">
			<tr class="table__row--header">
				<th class="table__heading"><orbis:message code="i18n.echo_parameters.Parameter" /></th>
				<th class="table__heading"><orbis:message code="i18n.echo_parameters.Value" /></th>
			</tr>
		</thead>
		
		<tbody>			
			<c:forEach var="item" items="${params}">
				<tr class="table__row--body">
					<td class="table__value">${item.key}</td>
					<td class="table__value">
						<c:forEach var="value" items="${item.value}">
							${value}
						</c:forEach>
					</td>
				</tr>
			</c:forEach>
		</tbody>
	</table>
</c:if>

<c:if test="${empty params}">
	<h1 style="text-align:center; margin-top:200px;"><orbis:message code="i18n.echo_parameters.NoParameters" /></h1>
</c:if>

<pre>
	${requestDump}
</pre>