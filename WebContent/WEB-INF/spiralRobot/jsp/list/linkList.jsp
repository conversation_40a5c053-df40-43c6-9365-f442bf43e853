<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete">Not being used</ui:isConverted>   --%>

<c:set var="serverPrefix" value="${pageContext.request.serverName}"/>

<div class="contentContainer">
	<div class="">
		<%@ include file="listHeader.jsp"%>
		<br>
		<c:forEach var="element" items="${URIElements}">
			<c:if test="${element.id==siteElementId}">
		<c:set scope="page" var="listRoot" value="${element}"/>
			</c:if>
		</c:forEach>
		<div class="fullWidth">
			<table summary="A list of resource links" cellpadding="0" cellspacing="0" class="fullWidth">
				<tr>
					<th
						scope="col"
						class="show">
						<orbis:message code="i18n.linkList.LINKS" />
					</th>
					<th scope="col">
						<orbis:message code="i18n.linkList.VIEW" />
					</th>
				</tr>
				<tr>
					<td class="tableLine" colspan="2">
						&nbsp;
					</td>
				</tr>
				<authz:acl
					domainObject="${siteElement}"
					hasPermission="4">
					<c:set
						var="hasEditPermission"
						value="1" />
				</authz:acl>
		
	<c:set var="currentMonth" value="-1" />	
	<c:set var="listElementCount" value="0" />
	<c:forEach var="listElement" items="${elements}">
	
	 		<c:set var="hideLink" value=""/>
					<c:if test="${listElement.status=='offline'}">
	 			<c:set var="hideLink" value="true"/>
	 			<authz:acl domainObject="${listElement}" hasPermission="4">
					<c:set var="hideLink" value=""/>
						</authz:acl>
					</c:if>
					<c:if test="${empty hideLink}">
						<%--if the month is different, print it out--%>
						<tr>
							<td class="listItem <c:if test="${listElement.status=='offline'}">offline</c:if>">
				
			<c:set var="imageLink" value="${listElement.contentItem.url}"/>	
								<%{
				String serverName = (String)pageContext.getAttribute("serverPrefix");
					                String imageLink = (String) pageContext.getAttribute("imageLink");
					                pageContext.setAttribute("target", "");
					                if (!imageLink.startsWith("http://" + serverName))
					                {
					                    pageContext.setAttribute("target", "target='_blank'");
					                }
					            }%>
			<a title="<c:out value="${listElement.contentItem.title}"/>" ${target} href="<c:out value="${listElement.contentItem.url}"/>">
							<c:out value="${listElement.contentItem.title}"/></a>
	
					<c:out value="${listElement.contentItem.description}" escapeXml="false"/>
	
							</td>
							<td	class="listItem ${listElement.status == 'offline' ? 'offline' : ''}"
								align="right" width="95px" valign="top">
								
								<c:if test="${not empty hasEditPermission}">
									<c:if test="${not elmLoop.first}">
										<a href="?move=-1&id=${listElement.id}" title="<orbis:message code="i18n.linkList.Moveup" />"><img src="${contextPath}/site/images/buttons/butUp.gif"></a>
									</c:if>
									<c:if test="${not elmLoop.last}">
										<a href="?move=1&id=${listElement.id}" title="<orbis:message code="i18n.linkList.Movedown" />"><img src="${contextPath}/site/images/buttons/butDown.gif"></a>
									</c:if>
								</c:if>
								<a
									title="${listElement.contentItem.title}"
									target="blank"
									href="${listElement.contentItem.url}"><img alt="<orbis:message code="i18n.linkList.View" />"	src="${contextPath}/site/images/buttons/butMag.gif"></a>
								<c:if test="${not empty hasEditPermission}">
									<a href="?edit=${listElement.id}"><img alt="<orbis:message code="i18n.linkList.Edit" />" src="${contextPath}/site/images/buttons/butSmallEdit.gif"></a>
								</c:if>
								
							</td>
						</tr>
					</c:if>
		
		<c:set var="listElementCount" value="${listElementCount+1}" />
				</c:forEach>
				<c:if test="${empty elements}">
					<tr>
	<td colspan="2" class="listItem">
							<orbis:message code="i18n.linkList.Therearecu9844452258683725" />
						</td>
					</tr>
				</c:if>
			</table>
		</div>

<c:set scope="page" var="itemType" value="linkItemController"/><br>

		<%@ include file="listFooter.jsp"%>

</div></div>