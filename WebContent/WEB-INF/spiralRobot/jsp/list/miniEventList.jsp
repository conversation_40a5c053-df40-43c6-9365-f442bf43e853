<!-- MAY NOT BE USED...MAYBE IN CANADIAN TIRE? -->
<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%-- <ui:isConverted type="complete">Not being used</ui:isConverted>   --%>

<%@ page import="java.util.List" %>
<%@ page import="com.orbis.web.site.SiteElement" %>
<%@ page import="java.util.*" %>
<%@ include file="/WEB-INF/spiralRobot/jsp/util/jspFunctions.jsp" %>

<%
	// in portal home
	int MAX_EVENT_ITEMS = 3;
	int MAX_TITLE_CHARACTERS = 65;
	
	List se = (List)request.getAttribute("URIElements");
	Iterator iter = se.iterator();
	while (iter.hasNext())
	{
		SiteElement el = (SiteElement)iter.next();
		if ( el.getType().equals("micrositeController") ) {
			// in a microsite
			MAX_EVENT_ITEMS = 3;
			MAX_TITLE_CHARACTERS = 46;
			pageContext.setAttribute("micrositeElement", el);	
		}
	}

	List items = (List)request.getAttribute("elements");
	Collections.sort(items);

%>
<div class="miniEventListContainer" >
	<div class="miniEventListTitle" >
		<c:if test="${not empty campusList}" >CAMPUS </c:if>EVENTS:
		<%if(items.size()>MAX_EVENT_ITEMS)
		{%>	
			
			<c:if test="${not empty campusList}">
				<a title="View all campus events" href="${contextPath}/campus-events.htm" ><fmt:message key="miniEventList.moreText" /></a>&nbsp;
			</c:if>
			<c:if test="${empty campusList}">
				]<a title="View all events" href="${contextPath}/<c:out value="${micrositeElement.url}"/>/home/<USER>" ><fmt:message key="miniEventList.moreText" /></a>&nbsp;
			</c:if>

		<%
		}
		// limit to a certain number of items
		items = items.subList(0, Math.min(MAX_EVENT_ITEMS, items.size()));
		request.setAttribute("eventItems", items);	
		request.setAttribute("eventItemsCount", new Integer(items.size()));
		%>
	</div>
	<div class="miniEventList">

		<c:forEach var="element" items="${URIElements}">
			<c:if test="${element.id==siteElementId}">
				<c:set scope="page" var="listRoot" value="${element}"/>
			</c:if>
		</c:forEach>
	
		<c:set var="internalLinkPrefix" value="${contextPath}"/>

		<%
		Date now = new Date();
		now.setTime(now.getTime()-86400000);
		pageContext.setAttribute("now",now);
		
		%>
		<c:set var="currentMonth" value="-1" />	
		<c:set var="listElementCount" value="0" />
		<c:forEach var="listElement" items="${eventItems}">

			<c:set var="eventTitle" value="${listElement.contentItem.title}"/>
	 		<c:set var="hideLink" value=""/>
 			<c:if test="${listElement.status=='offline'}">
 				<c:set var="hideLink" value="true"/>
 			</c:if>

			<c:if test="${empty hideLink}">

				<c:if test="${listElement.contentItem.date.time > now.time}">
			 		<c:if test="${listElement.status=='offline'}"><DIV class="offline"></c:if>
				
					<%--print the article's date--%>
					<fmt:formatDate type="date" value="${listElement.contentItem.date}" pattern="MMM dd/yy" />
					<br>
					<a title="<c:out value="${listElement.contentItem.title}"/>" href="<c:out value="${internalLinkPrefix}${listElement.fullPath}.htm"/>">
					<%= limitTitleSize( (String)pageContext.getAttribute("eventTitle"), MAX_TITLE_CHARACTERS ) %></a>
							
							<c:if test="${listElementCount!=(eventItemsCount-1)}">
								<br><br>
							</c:if>
		
					<c:if test="${listElement.status=='offline'}"></DIV></c:if>
		
				</c:if>
			</c:if>
	
	<c:set var="listElementCount" value="${listElementCount+1}" />
</c:forEach>

	<c:if test="${empty eventItems}">
	<br>There are no events.
	</c:if>

</div>
</div>
