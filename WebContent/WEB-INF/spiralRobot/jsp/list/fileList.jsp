<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>

<%@ include file="listHeader.jsp"%>
<c:forEach var="element" items="${URIElements}">
	<c:if test="${element.id==siteElementId}">
		<c:set scope="page" var="listRoot" value="${element}" />
	</c:if>
</c:forEach>
<c:set scope="page" var="itemType" value="fileItemController" />
<%@ include file="listFooter.jsp"%>
<ui:panel>
	<ui:grid>
		<ui:gridCol width="12">
			<c:if test="${not empty siteElement.contentItem.description}">
				<c:out value="${siteElement.contentItem.description}" escapeXml="false" />
			</c:if>
		</ui:gridCol>
	</ui:grid>
	<ui:grid>
		<ui:gridCol width="12">
			<table class="table hover table-bordered">
				<authz:acl domainObject="${siteElement}" hasPermission="4">
					<c:set var="hasEditPermission" value="1" />
				</authz:acl>
				<c:set var="currentMonth" value="-1" />
				<c:set var="listElementCount" value="0" />
				<c:forEach var="listElement" items="${elements}" varStatus="elmLoop">
					<c:set var="hideLink" value="" />
					<c:if test="${listElement.status=='offline'}">
						<c:set var="hideLink" value="true" />
						<authz:acl domainObject="${listElement}" hasPermission="4">
							<c:set var="hideLink" value="" />
						</authz:acl>
					</c:if>
					
					<c:if test="${empty hideLink}">
						<%--if the month is different, print it out--%>
						<tr class="table__row--body">
							<td class="table__value" ${listElement.status== 'offline' ? 'offline' : ''}" width="75%">
								<c:if test="${not empty listElement.contentItem.path}">
									<a title="${listElement.contentItem.title}" href="${listElement.contentItem.path}" target="_blank"> ${listElement.contentItem.title}</a>
									<c:if test="${listElement.status == 'offline'}">
										<span class="label label-important"><orbis:message code="i18n.fileList.offline7648989856423508" /></span>
									</c:if>
									<br>
									${listElement.contentItem.description}
								</c:if>
								<c:if test="${empty listElement.contentItem.path}">
									${listElement.contentItem.title}
								</c:if>
							</td>
							<td class="table__value" style="width: 25%;">
								<c:if test="${not empty listElement.contentItem.path}">
									<ui:form action="${listElement.contentItem.path}" target="_blank">
										<ui:button buttonType="submit" size="small"><orbis:message code="i18n.fileList.View" /></ui:button>
									</ui:form>
								</c:if>
								<c:if test="${not empty hasEditPermission}">
									<ui:button size="small" onclick="window.location='?edit=${listElement.id}';"><orbis:message code="i18n.fileList.Edit" /></ui:button>
								</c:if>
								<authz:acl domainObject="${listElement}" hasPermission="4">
									<c:if test="${not elmLoop.first}">
										<a href="?move=-1&amp;id=${listElement.id}" title="Move up">
											<img src="${contextPath}/site/images/buttons/butUp.gif">
										</a>
									</c:if>
									<c:if test="${not elmLoop.last}">
										<a href="?move=1&amp;id=${listElement.id}" title="Move down">
											<img src="${contextPath}/site/images/buttons/butDown.gif">
										</a>
									</c:if>
								</authz:acl>
							</td>
						</tr>
						<c:set var="listElementCount" value="${listElementCount+1}" />
					</c:if>
				</c:forEach>
				<c:if test="${empty elements}">
					<tr class="table__row--body">
						<td class="table__value" colspan="3"><orbis:message code="i18n.fileList.Therearecu2960068675434463" /></td>
					</tr>
				</c:if>
			</table>
		</ui:gridCol>
	</ui:grid>
</ui:panel>