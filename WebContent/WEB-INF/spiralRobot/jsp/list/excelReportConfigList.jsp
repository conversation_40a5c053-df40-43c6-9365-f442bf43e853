<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%-- <ui:isConverted type="complete" >Not being used</ui:isConverted>   --%>

<div class="contentContainer">
  <div class="">

<%@ include file="listHeader.jsp" %>

<c:forEach var="element" items="${URIElements}">
	<c:if test="${element.id==siteElementId}">
		<c:set scope="page" var="listRoot" value="${element}"/>
	</c:if>
</c:forEach>

<table summary="A list of excel reports" class="table table-striped table-bordered">
	<tr>
		<th scope="col" class="show"> <orbis:message code="i18n.excelReportConfigList.REPORT" /> </th>
		<th scope="col" class="show"> <orbis:message code="i18n.excelReportConfigList.DESCRIPTION" /> </th>
	    <th style="text-align:right">
	    	<input class="btn btn-small" type="button" value="<spring:message code="i18n.excelReportConfigList.GenerateAllReports" />" onclick="window.location='?generateAllReports'">
	    </th>
	</tr>
    <tr>
    	<td colspan="3">
    		<hr  >
    	</td>
	</tr>
	<authz:acl domainObject="${siteElement}" hasPermission="4"> 
		<c:set var="hasEditPermission" value="1" /> 
	</authz:acl> 
	<c:set var="currentMonth" value="-1" />
	<c:set var="listElementCount" value="0" /> 
	<c:forEach var="listElement" items="${elements}"> 
 
		<c:set var="hideLink" value=""/> 
		<c:if test="${listElement.status=='offline'}"> 
			<c:set var="hideLink" value="true"/> 
			<authz:acl domainObject="${listElement}" hasPermission="4"> 
				<c:set var="hideLink" value=""/> 
			</authz:acl> 
		</c:if> 
		<c:if test="${empty hideLink}">
      		<%--if the month is different, print it out--%>
			<tr>
			
				<td class="listItem ${listelement.status == 'offline' ? 'offline' : ''}">
					
						${listElement.contentItem.title}
					
				</td>
  				<td class="listItem ${listelement.status == 'offline' ? 'offline' : ''}">
  					${listElement.contentItem.description}
  				</td>  				
				<td class="listItem lastColumn" >
					<a title="view report" href="<c:out value="${contextPath}${listElement.fullPath}.htm"/>"><img alt="<orbis:message code="i18n.excelReportConfigList.View" />" border=0 src="${contextPath}/site/images/buttons/butMag.gif"></a>
					<a title="view excel report" href="<c:out value="${contextPath}${listElement.fullPath}.htm?generateExcelReport"/>"><img alt="<orbis:message code="i18n.excelReportConfigList.ViewExcel" />" src="${contextPath}/site/images/buttons/butExcel.gif"></a>					
				</td>
			</tr>
		<c:set var="listElementCount" value="${listElementCount+1}" /> 
 	</c:if>
	</c:forEach>
	<c:if test="${empty elements}">
		<tr>
	    	<td colspan="3" class="listItem">
	    <orbis:message code="i18n.excelReportConfigList.Therearecu15479167943698202" />     
</td>
		</tr>
	</c:if>
</table>

  </div>
</div>
<br>
<%@ include file="listFooter.jsp" %>
<br>
<%--<c:if test="${not empty siteElement}">
	<authz:acl domainObject="${siteElement}" hasPermission="4">
		<c:if test="${empty param.edit}">
			<a title="Add All Reports by category"  href="?addReportsByCategory" >Add All Reports for category</a>
		</c:if>
	</authz:acl>
</c:if>--%>
