<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete">Not being used</ui:isConverted>   --%>
	<div class="newsListPage">
		<%@ include file="listHeader.jsp"%>
		<c:set	var="internalLinkPrefix" value="${contextPath}" />
		<c:set	scope="page" var="itemType"	value="newsItemController" />
		<div class="clearFix"></div>
		<c:if test="${not empty siteElement}">
			<authz:acl domainObject="${siteElement}" hasPermission="4">
				<c:if test="${empty param.edit}">
					<input type="button" class="btn btn-small" value="${siteElement.contentItem.i18nAddNews}" text="${siteElement.contentItem.i18nAddNews}" onclick="window.location='?edit=-1&amp;formParent=${siteElement.id}&amp;index=0&amp;type=${itemType}';">
				</c:if>
			</authz:acl>
		</c:if>
			<table
				summary="A list of news items"
				cellpadding="0"
				cellspacing="0"
				class="fullWidth">
				<tr>
					<th scope="col">
						<orbis:message code="i18n.newsList.DATEPOSTED" />
					</th>
					<th scope="col">
						<orbis:message code="i18n.newsList.TITLE" />
					</th>
					<th>
						<orbis:message code="i18n.newsList.VIEW" />
					</th>
				</tr>
				<c:set
					var="currentMonth"
					value="-1" />
				<c:set
					var="listElementCount"
					value="0" />
				<c:forEach var="listElement" items="${elements}">
					<c:set var="hideLink"	value="" />
					<c:if test="${listElement.status=='offline'}">
						<c:set	var="hideLink"	value="true" />
						<authz:acl	domainObject="${listElement}" hasPermission="4">
							<c:set	var="hideLink"	value="" />
						</authz:acl>
					</c:if>
					<c:if test="${empty hideLink}">
						<%-- if the month is different, print it out --%>
						<c:if test="${currentMonth != listElement.contentItem.date.month}">
							<tr class="tableHeader">
								<td class="listTitleContainer ${listElement.status == 'offline' ? 'offline' : ''}" colspan="3">
									<h3>
										<c:set
											var="currentMonth"
											value="${listElement.contentItem.date.month}" />
											
										<c:set var="monthName">
											<fmt:formatDate
												type="date"
												value="${listElement.contentItem.date}"
												pattern="MMMM" />
										</c:set>
										<orbis:message code="i18n.${monthName}.${siteElement.contentItem.language}"/>
									</h3>
								</td>
							</tr>
								<tr><td colspan="3" class="tableLine">&nbsp;</td></tr>
						</c:if>
					
						<tr>
							<td class="listItem ${listElement.status == 'offline' ? 'offline' : ''}">
								<%-- print the article's date --%>
								<orbis:message code="i18n.${monthName}.short.${siteElement.contentItem.language}"/>
								<fmt:formatDate
									type="date"
									value="${listElement.contentItem.date}"
									pattern=" dd/yy" />
							</td>
							<td class="listItem ${listElement.status == 'offline' ? 'offline' : ''}">
								<a
									title="${listElement.contentItem.title}"
									href="${internalLinkPrefix}${listElement.fullPath}.htm">
									<c:out value="${listElement.contentItem.title}" />
								</a>
							</td>
							<td class="listItem lastColumn">
								<a
									title="${listElement.contentItem.title}"
									href="${internalLinkPrefix}${listElement.fullPath}.htm">
									<img
										alt="<spring:message code="i18n.newsList.View" javaScriptEscape="true"/>"
										src="${contextPath}/site/images/buttons/butMag.gif">
								</a>
							</td>
						</tr>
					</c:if>
					<c:set
						var="listElementCount"
						value="${listElementCount+1}" />
				</c:forEach>
				<c:if test="${empty elements}">
					<tr>
						<td colspan="3" class="listItem">
							<orbis:message code="i18n.newsList.Therearecu4166789741688429" />
						</td>
					</tr>
				</c:if>
				
			</table>	
	</div>
