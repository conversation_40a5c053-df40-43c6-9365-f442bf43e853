<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete">Not being used</ui:isConverted>   --%>

<div class="contentContainer">
	<div class="">
		<%@ include file="listHeader.jsp"%>
		<c:forEach
			var="element"
			items="${URIElements}">
			<c:if test="${element.id==siteElementId}">
				<c:set
					scope="page"
					var="listRoot"
					value="${element}" />
			</c:if>
		</c:forEach>
		
		<c:set
			var="internalLinkPrefix"
			value="${contextPath}" />
		<div class="fullwidth">
			<table
				summary="A list of news items "
				cellpadding="0"
				cellspacing="0"
				class="fullWidth"
				border="0">
				<tr>
					<th
						scope="col"
						class="show">
						<orbis:message code="i18n.faqList.Question" /></th>
					<td
						scope="col"
						class="show"></td>
				</tr>
				<tr><td colspan=2 class="tableLine">&nbsp;</td></tr>
				<c:set
					var="listElementCount"
					value="0" />
				<c:forEach
					var="listElement"
					items="${elements}"
					varStatus="elmLoop">
					<c:set
						var="listItemStyle"
						value="listItem ${listElement.status == 'offline' ? 'offline' : ''}" />				
					<c:set
						var="hideLink"
						value="" />
					<c:if test="${listElement.status=='offline'}">
						<c:set
							var="hideLink"
							value="true" />
						<authz:acl
							domainObject="${listElement}"
							hasPermission="4">
							<c:set
								var="hideLink"
								value="" />
						</authz:acl>
					</c:if>
					<c:if test="${empty hideLink}">
						<tr>
							<td class="${listItemStyle}">
								<%--print the article's date--%>
								<%--<a
									title="view answer"
									href="${internalLinkPrefix}${listElement.fullPath}.htm">--%>${listElement.contentItem.question}<%--</a>--%></td>
							<td class="${listItemStyle}" style="text-align:right">
								
								<authz:acl
									domainObject="${listElement}"
									hasPermission="4">		
										<c:if test="${not elmLoop.first}">
											<a href="?move=-1&id=${listElement.id}" title="<orbis:message code="i18n.faqList.Moveup" />"><img src="${contextPath}/site/images/buttons/butUp.gif"></a>
										</c:if>
										<c:if test="${not elmLoop.last}">
											<a href="?move=1&id=${listElement.id}" title="<orbis:message code="i18n.faqList.Movedown" />"><img src="${contextPath}/site/images/buttons/butDown.gif"></a>
										</c:if>
								</authz:acl>	
									<input type="button" class="btn btn-small" onclick="window.location.href='${internalLinkPrefix}${listElement.fullPath}.htm';" value="<spring:message code="i18n.faqList.Answer" />">
													
							</td>
						</tr>
					</c:if>
					<c:set
						var="listElementCount"
						value="${listElementCount+1}" />
				</c:forEach>
				<c:if test="${empty elements}">
					<tr>
						<td
							colspan="2"
							class="listItem"><orbis:message code="i18n.faqList.Therearecu3976613191912318" /></td>
					</tr>
				</c:if>
			</table>
		</div>
		
	</div>
</div>

<c:set
	scope="page"
	var="itemType"
	value="faqItemController" />
<br>
<%@ include file="listFooter.jsp"%>
