<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%-- <ui:isConverted type="complete">Not being used</ui:isConverted>   --%>
<%@ page import="java.util.List" %>
<%@ page import="com.orbis.web.site.SiteElement" %>
<%@ page import="com.orbis.web.content.job.JobItem" %>
<%@ page import="java.util.*" %>

<div class="contentContainer">
	<div class="">

<%@ include file="listHeader.jsp" %>

<c:forEach var="element" items="${URIElements}">
	<c:if test="${element.id==siteElementId}">
		<c:set scope="page" var="listRoot" value="${element}"/>
	</c:if>
</c:forEach>

<%
	List items = (List)request.getAttribute("elements");
	Collections.sort(items, new Comparator() {
			public int compare(Object o1, Object o2) {	
				o1=((SiteElement)o1).getContentItem();
				o2=((SiteElement)o2).getContentItem();	
				if ( ((JobItem)o1).getDate().getTime() > ((JobItem)o2).getDate().getTime() ) {
					// o1 is after o2
					return 1;
				} else if ( ((JobItem)o1).getDate().getTime() == ((JobItem)o2).getDate().getTime() ) {
					return 0;
				} else {
					return -1;
				}
			}
	});
%>

<c:set var="internalLinkPrefix" value="${contextPath}"/>

<div class="fullWidth">
	<table summary="Jobs/Volunteers list " cellpadding="0" cellspacing="0" class="fullWidth">
		<tr>
		<th scope="col" class="show">
			<orbis:message code="i18n.jobList.DATEPOSTED" />
		</th>
		<th scope="col" class="show">
			<orbis:message code="i18n.jobList.POSITION" />
		</th>
		<th scope="col" class="show">
		<orbis:message code="i18n.jobList.DEPARTMENT" />
		</TH>
		<th><orbis:message code="i18n.jobList.VIEW" /></th>
		</tr>
		
		<tr>
		<td colspan="4">
		<hr  >
		</td>
		</tr>
		
	<c:set var="currentMonth" value="-1" />	
	<c:set var="listElementCount" value="0" />
	<c:forEach var="listElement" items="${elements}">
	
	 		<c:set var="hideLink" value=""/>
	 		<c:if test="${listElement.status=='offline'}">
	 			<c:set var="hideLink" value="true"/>
	 			<authz:acl domainObject="${listElement}" hasPermission="4">
					<c:set var="hideLink" value=""/>
				</authz:acl>
	 		</c:if>
	
		<c:if test="${empty hideLink}">
	
			<tr>
				<td  class="listItem <c:if test="${listElement.status=='offline'}">offline</c:if>">
					<%--print the article's date--%>
					<fmt:formatDate type="date" value="${listElement.contentItem.date}" pattern="MMM dd/yy" />
					</TD>
					<TD  class="listItem <c:if test="${listElement.status=='offline'}">offline</c:if>">
					<a title="<c:out value="${listElement.contentItem.position}"/>" href="<c:out value="${internalLinkPrefix}${listElement.fullPath}.htm"/>">
							<c:out value="${listElement.contentItem.position}"/>
						 </a>
				</td>
				<td  class="listItem <c:if test="${listElement.status=='offline'}">offline</c:if>">
					<c:out value="${listElement.contentItem.department}"/>
				</td>
				<td  class="listItem <c:if test="${listElement.status=='offline'}">offline</c:if>" align="right">
				<a title="<c:out value="${listElement.contentItem.position}"/>" href="<c:out value="${internalLinkPrefix}${listElement.fullPath}.htm"/>"><img alt="<orbis:message code="i18n.jobList.View" />" src="${contextPath}/site/images/buttons/butMag.gif"></a>
				</td>
			
			</tr>
		
		</c:if>
		
		<c:set var="listElementCount" value="${listElementCount+1}" />
	</c:forEach>
	
	<c:if test="${empty elements}">
	<tr>
	<td colspan="4" class="listItem">
	There are currently no 
	<c:if test="${siteElement.type=='jobListController'}" >
		jobs
	</c:if>
	<c:if test="${siteElement.type=='volunteerListController'}" >
		volunteer positions
	</c:if>
	
	available in this list.
	</td>
	</tr>
	</c:if>
	
	</table>
</div>

</div></div>

<c:if test="${siteElement.type=='jobListController'}" >
	<c:set scope="page" var="itemType" value="jobItemController"/><br>
</c:if>
<c:if test="${siteElement.type=='volunteerListController'}" >
	<c:set scope="page" var="itemType" value="volunteerItemController"/><br>
</c:if>
<div style="text-align:left">
	<%@ include file="listFooter.jsp" %>
</div>
