<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%@ page import="java.util.List" %>
<%@ include file="/WEB-INF/spiralRobot/jsp/util/jspFunctions.jsp" %>
<%-- <ui:isConverted type="complete">Not being used</ui:isConverted>   --%>

<c:set var="maxItemLength" scope="request" value="${siteElement.contentItem.maxItemLength}"/>

<%
	// in portal home
	int MAX_TITLE_CHARACTERS= ((Integer)request.getAttribute("maxItemLength")).intValue();
		
	List items = (List)request.getAttribute("elements");
	request.setAttribute("newsItems", items);
	request.setAttribute("newsItemsCount", new Integer(items.size()));
%>

<div class="miniNewsListContainer" >

	<div class="miniNewsListTitleContainer" >
		
		<div class="miniNewsListTitleLeft">
			<h3><fmt:message key="${startFromRoot == true ? 'miniNewsList.siteTitle' : 'miniNewsList.title'}" /></h3>
		</div>
		
		<div class="miniNewsListTitleRight">
			<c:if test="${not empty moreElement}">
				[<a title="View more news articles" href="${contextPath}${moreElement.fullPath}.htm" class="linkButton"><fmt:message key="miniNewsList.moreText" /></a>]&nbsp;
			</c:if>
			<jsp:include page="/WEB-INF/spiralRobot/jsp/editBtn.jsp" />
		</div>

	</div>
	
	<div class="miniNewsList">
		<div class="miniNewsContentContainer">
			<c:forEach var="element" items="${URIElements}">
				<c:if test="${element.id == siteElementId}">
					<c:set scope="page" var="listRoot" value="${element}"/>
				</c:if>
			</c:forEach>
	
			<c:set var="internalLinkPrefix" value="${contextPath}"/>
		
			<c:set var="currentMonth" value="-1" />	
			<c:set var="listElementCount" value="0" />
			<c:forEach var="listElement" items="${newsItems}">
				<c:set var="newsTitle" value="${listElement.contentItem.title}"/>
	
		 		<c:set var="hideLink" value=""/>
	 			<c:if test="${listElement.status=='offline'}">
		 			<c:set var="hideLink" value="true"/>
	 			</c:if>
	
				<c:if test="${empty hideLink}">
					<div class="${listElement.status == 'offline' ? 'offline' : ''} miniNewsLineBox">
				
						<div class="dateBox"><fmt:formatDate type="date" value="${listElement.contentItem.date}" pattern="MMM dd/yy" /></div>
		
						<div class="descBox"><a title="${listElement.contentItem.title}" href="${internalLinkPrefix}${listElement.fullPath}.htm">
							<%= limitTitleSize( (String)pageContext.getAttribute("newsTitle"), MAX_TITLE_CHARACTERS ) %></a></div>
					</div>
				</c:if>
		
				<c:set var="listElementCount" value="${listElementCount+1}" />
		
			</c:forEach>
			
			<c:if test="${empty newsItems}">
				<br>There is currently no news.
			</c:if>
		</div>
	</div>
</div>
