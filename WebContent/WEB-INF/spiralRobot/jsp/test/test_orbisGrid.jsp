<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<script type="text/javascript">
	$(function() {

	});
</script>

<orbis:navigation title="i18n.test_orbisGrid.OrbisGridt1795764306355250">
	<orbis:navButton title="i18n.common.navigation.backToHome" action="displayHome"
		primaryIcon="icon-chevron-left" />
</orbis:navigation>

<c:if test="${not empty successMessage}">
	<ui:notification type="success">${successMessage}</ui:notification>
</c:if>
<c:if test="${not empty errorMessage}">
	<ui:notification type="error">${errorMessage}</ui:notification>
</c:if>

<div class="box">
	<div class="boxContent">
		<c:set var="gridID" value="test_orbisGrid" scope="request" />
		<c:set var="gridLoadAjaxMethodName" value="loadOrbisGridTestTable" />
		<c:set var="gridLoadOnDocumentReady" value="true" />
		
		<c:set var="gridDefaultOrderBy" value="cu.user.lastName asc" />
		<c:set var="gridFilters">
			<orbis:gridThNoFilter />
			<orbis:gridThNumberFilter property="virtual.14" />
			<orbis:gridThNumberFilter property="virtual.15" />
			<orbis:gridThNumberFilter property="virtual.16" />
			<orbis:gridThNumberFilter property="virtual.17" />
			<orbis:gridThNumberFilter property="virtual.18" />
			<orbis:gridThNumberFilter property="virtual.19" />
			<orbis:gridThNumberFilter property="virtual.13" />
			<orbis:gridThNumberFilter property="virtual.20" />
			<orbis:gridThTextFilter property="cu.company.organization.name" />
			<orbis:gridThTextFilter property="cu.company.name" />
			<orbis:gridThTextFilter property="cu.user.firstName" />
			<orbis:gridThTextFilter property="cu.user.lastName" />
			<orbis:gridThTextFilter property="cu.user.jobTitle" />
			<orbis:gridThSelectFilter property="cu.user.userStatus">
				<orbis:gridOption>
					<orbis:message code="i18n.dashboard_staffMyContacts.Active" />
				</orbis:gridOption>
				<orbis:gridOption>
					<orbis:message code="i18n.dashboard_staffMyContacts.Prospect" />
				</orbis:gridOption>
				<orbis:gridOption>
					<orbis:message code="i18n.dashboard_staffMyContacts.Inactive" />
				</orbis:gridOption>
			</orbis:gridThSelectFilter>
			<orbis:gridThTextFilter property="cu.user.city" />
			<orbis:gridThTextFilter property="cu.user.phoneNumber" />
			<orbis:gridThSelectFilter property="cu.company.organization.userStatus">
				<orbis:gridOption>
					<orbis:message code="i18n.dashboard_staffMyContacts.Active" />
				</orbis:gridOption>
				<orbis:gridOption>
					<orbis:message code="i18n.dashboard_staffMyContacts.Prospect" />
				</orbis:gridOption>
				<orbis:gridOption>
					<orbis:message code="i18n.dashboard_staffMyContacts.Inactive" />
				</orbis:gridOption>
			</orbis:gridThSelectFilter>
			<orbis:gridThSelectFilter property="cu.company.userStatus">
				<orbis:gridOption>
					<orbis:message code="i18n.dashboard_staffMyContacts.Active" />
				</orbis:gridOption>
				<orbis:gridOption>
					<orbis:message code="i18n.dashboard_staffMyContacts.Prospect" />
				</orbis:gridOption>
				<orbis:gridOption>
					<orbis:message code="i18n.dashboard_staffMyContacts.Inactive" />
				</orbis:gridOption>
			</orbis:gridThSelectFilter>

		</c:set>

		<%@ include file="/WEB-INF/spiralRobot/jsp/grid/grid_placeHolder.jsp"%>
	</div>
</div>