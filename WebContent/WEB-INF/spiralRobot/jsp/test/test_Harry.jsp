<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<script type="text/javascript">
	var controllerPath = "/myAccount/staff/dashboard/tf.htm";
	$(document).ready(function(){
		$('#divLink').click(function(e){
			e.preventDefault();
			loadTest();
		});
	});
	
	function loadTest()
	{
		var request = new Object();
		request.action = '<o:encrypt action="secondTest" />';
		request.rnd = Math.random() * 100000;
		
		$("#loadDiv").load(controllerPath, request);
	}
	
	function postTest()
	{
		
		var request = {action: '<o:encrypt action="thirdTest" />', rnd: (Math.random() * 100000)};
		alert(request.action);
		
		var foo = {"gep":123,"bar":true};
		alert(foo.gep);
		
		$.post(controllerPath, request, function(data, status, xhr) {
			alert(data.bar);
		}, "json");

	}
	
</script>

Users: ${fn:length(users)}

<table>
	<tr>
		<td><orbis:message code="i18n.test_Harry.Username" /></td>
		<td><orbis:message code="i18n.test_Harry.UserID" /></td>
		<td><orbis:message code="i18n.test_Harry.UserFirstname" /></td>
		<td><orbis:message code="i18n.test_Harry.UserLastname" /></td>
	</tr>
	<c:forEach var="user" items="${users}">
		<c:if test="${user.id lt 110}">
			<tr>
				<td>${user.username}</td>
				<td>${user.id}</td>
				<td>${user.firstName}</td>
				<td>${user.lastName}</td>
			</tr>
		</c:if>
	</c:forEach>
</table>

<o:nav anchor="true" action="secondTest">
	${enrollment.program.name}
</o:nav>

<div style="margin-top:20px; font-weight:bold; font-size:20px">
	<a href="/myAccount/staff/dashboard/tf.htm?action=<o:encrypt action="secondTest" />">Second Link</a>
</div>

<div style="margin-top:20px; font-weight:bold; font-size:20px">
	<a href="javascript:void(0)" onclick="$('#fooForm').submit()">Third Link</a>
	<form enctype="multipart/form-data" id="fooForm" method="POST" action="${siteElement.fullPath}.htm">
		<o:encrypt input="true" action="secondTest" />
	</form>
</div>

<div style="margin-top:20px; font-weight:bold; font-size:20px">
	<a id="divLink" href=""><orbis:message code="i18n.test_Harry.LoadDiv" /></a>
</div>

<div id="loadDiv" style="border:1px solid #AAA;padding:5px;"></div>
<button onclick="postTest()"><orbis:message code="i18n.test_Harry.posttest" /></button>
