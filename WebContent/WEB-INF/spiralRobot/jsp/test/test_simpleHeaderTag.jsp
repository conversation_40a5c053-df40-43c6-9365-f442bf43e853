<%@ include file="/WEB-INF/jsp/include.jsp"%>

<ui:simpleHeader i18n_title="Page Title" i18n_subtitle="Page Subtitle">
	<ui:action id="back" i18n_title="Back to Home" action="displayHome" />
	<ui:action i18n_title="Module Configuration" action="displayHome" icon="settings" />
	<ui:action i18n_title="Share" action="displayHome" icon="share" />

	<ui:section key="rightSideContent">
		<ui:button size="large" type="error" classes="display--flex align--center color--font--white" style="pill">
			<i class="material-icons margin--r--s">chat</i>
			Send Message
		</ui:button>
	</ui:section>
</ui:simpleHeader>

<p>
	Tag accepts 2 different action types. It accepts a single back action that takes the ID of "back", and it accepts a default action (without ID) that will show up as Icon buttons.
	If there are more than 2 actions, it will show a dropdown with the actions.
</p>

<ui:simpleHeader i18n_title="Page Title" i18n_subtitle="Page Subtitle">
	<ui:action id="back" i18n_title="Back to Home" action="displayHome" />
	<ui:action i18n_title="Action 1" action="displayHome" icon="share" />
	<ui:action i18n_title="Action 2" action="displayHome" icon="share" />
	<ui:action i18n_title="Action 3" action="displayHome" icon="share" />
	<ui:action i18n_title="Action 4" action="displayHome" icon="share" />

	<ui:section key="rightSideContent">
		<ui:button size="large" type="error" classes="display--flex align--center color--font--white" style="pill">
			<i class="material-icons margin--r--s">chat</i>
			Send Message
		</ui:button>
	</ui:section>
</ui:simpleHeader>

<ui:simpleHeader i18n_title="Page Title" i18n_subtitle="Page Subtitle" classes="margin--t--xl">

</ui:simpleHeader>