<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<c:set var="title"><spring:message code="i18n.testCkEditor_test1.CKEditorTest1" /> </c:set>
<%@ include file="test_commonHeader.jsp"%>

<style>
	legend {font-weight:bold;}
</style>

Demonstration of CKEditor initialized with Javascript
<HR>

<script type="text/javascript">

	$(document).ready(function() {
		CKEDITOR.replace("ckeditor1", {toolbar : "default"});
		CKEDITOR.replace("ckeditor2", {toolbar : "basic"});
		CKEDITOR.replace("ckeditor3");
	});
		
</script>

<fieldset>
    <legend>ckeditor1</legend>

	<ul>
		<li>configured with "default" toolbar</li>
	</ul>

	<form enctype="multipart/form-data" id="form1" method="POST" action="${siteElement.fullPath}.htm">
		<o:encrypt input="true" action="displayCkEditor_preview" />
		<textarea id="ckeditor1" name="fooContent">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut velit sapien, ultrices at porttitor et, sodales et lacus. Suspendisse placerat nisi quis tellus scelerisque et bibendum risus elementum. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aenean viverra justo et arcu eleifend suscipit.</textarea>	

		<input type="submit" value="<spring:message code="i18n.testCkEditor_test1.RegularSubmitworks" />">
		<input type="button" value="<spring:message code="i18n.testCkEditor_test1.JQuerySubmitworks" />" onclick="$('#form1').submit();">
		<input type="button" value="<spring:message code="i18n.testCkEditor_test1.documentge8388529677537041" />" onclick="$('#form1').submit();">
	</form>

</fieldset>	

<BR>

<fieldset>
    <legend>ckeditor2</legend>

	<ul>
		<li>configured with "basic" toolbar</li>
	</ul>

	<form enctype="multipart/form-data" id="form2" method="POST" action="${siteElement.fullPath}.htm">
		<o:encrypt input="true" action="displayCkEditor_preview" />
		<textarea id="ckeditor2" name="fooContent">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut velit sapien, ultrices at porttitor et, sodales et lacus. Suspendisse placerat nisi quis tellus scelerisque et bibendum risus elementum. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aenean viverra justo et arcu eleifend suscipit.</textarea>	

		<input type="submit" value="<spring:message code="i18n.testCkEditor_test1.RegularSubmitworks" />">
		<input type="button" value="<spring:message code="i18n.testCkEditor_test1.JQuerySubmitworks" />" onclick="$('#form2').submit();">
		<input type="button" value="<spring:message code="i18n.testCkEditor_test1.documentge4235987541851226" />" onclick="$('#form2').submit();">
	</form>

</fieldset>	

<fieldset>
    <legend>ckeditor3</legend>

	<ul>
		<li>configured with "no configuration"</li>
	</ul>

	<form enctype="multipart/form-data" id="form3" method="POST" action="${siteElement.fullPath}.htm">
		<o:encrypt input="true" action="displayCkEditor_preview" />
		<textarea id="ckeditor3" name="fooContent">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut velit sapien, ultrices at porttitor et, sodales et lacus. Suspendisse placerat nisi quis tellus scelerisque et bibendum risus elementum. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus. Aenean viverra justo et arcu eleifend suscipit.</textarea>	

		<input type="submit" value="<spring:message code="i18n.testCkEditor_test1.RegularSubmitworks" />">
		<input type="button" value="<spring:message code="i18n.testCkEditor_test1.JQuerySubmitworks" />" onclick="$('#form3').submit();">
		<input type="button" value="<spring:message code="i18n.testCkEditor_test1.documentge05852804861600702" />" onclick="$('#form3').submit();">
	</form>

</fieldset>	
