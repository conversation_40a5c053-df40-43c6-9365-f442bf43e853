<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>


<script type="text/javascript">

	function goToHybrid()
	{
		$("input[name='action']").val('<o:encrypt action="submitSpiralRobotOrganizedFormAndGoToHybrid" />');
	}

</script>

<ui:navBack>
	<ui:navBackItem action="displayHome">Home</ui:navBackItem>
	<ui:navBackItem action="displaySpiralRobotForm">Form</ui:navBackItem>
</ui:navBack>

<ui:header>
	Spiral Robot Organized Forms
</ui:header>


<ui:formPage formId="testForm" action="submitSpiralRobotOrganizedForm">
	<ui:section key="extraButtons">
		<ui:button type="warning" size="large" onclick="alert(123);">Extra 1</ui:button>
		<ui:button type="info" onclick="alert(234);"> Extra 2 </ui:button>
		${originalSection}
	</ui:section>
		
	<ui:formGroup i18n_title="General Info" id="general">		
			
		<ui:textbox name="firstName" required="true">First Name</ui:textbox>
		
		<ui:textbox name="lastName" required="true">Last Name</ui:textbox>
		
		<ui:textbox name="readOnly" readOnly="true">I'm read only because reasons</ui:textbox>
		
		<ui:datepicker name="birthdate">Date of Birth</ui:datepicker>
		
		<ui:textbox name="email" classes="required" i18n_helpText="Ex: <EMAIL>">Email*</ui:textbox>
		
		<ui:textbox name="password" type="password">Password</ui:textbox>
		
		<ui:textbox name="confirmPassword" type="password">Confirm Password</ui:textbox>
		
		<ui:textbox name="dragonballs" type="number">How many dragonballs have you found?</ui:textbox>
		
		<ui:checkbox name="dragonRadar">Did you use Bulma's dragonball radar?</ui:checkbox>
	</ui:formGroup>
	
	<ui:formGroup i18n_title="Collecting dragon balls" id="balls">		
		<ui:checkboxGroup i18n_title="Which dragon balls have you found?" name="dragonballsFound">
			<ui:checkboxGroupItem value="1 Star">1 Star</ui:checkboxGroupItem>
			<ui:checkboxGroupItem value="2 Star">2 Stars</ui:checkboxGroupItem>
			<ui:checkboxGroupItem value="3 Star">3 Stars</ui:checkboxGroupItem>
			<ui:checkboxGroupItem value="4 Star">4 Stars</ui:checkboxGroupItem>
			<ui:checkboxGroupItem value="5 Star">5 Stars</ui:checkboxGroupItem>
			<ui:checkboxGroupItem value="6 Star">6 Stars</ui:checkboxGroupItem>
			<ui:checkboxGroupItem value="7 Star">7 Stars</ui:checkboxGroupItem>
			<ui:checkboxGroupOther />
		</ui:checkboxGroup>
	</ui:formGroup>
	
	<ui:formGroup i18n_title=" Teacher Review" id="teacher">	
		<ui:rangeSlider min="0" max="9001" name="powerLevel">Power Level</ui:rangeSlider>
		
		<ui:radioGroup name="betterTeacher" i18n_title="Who is the better teacher?">
			<ui:radioButton value="Master Roshi">Master Roshi</ui:radioButton>
			<ui:radioButton value="Mr. Popo">Mr. Popo</ui:radioButton>
			<ui:radioButton value="King Kai">King Kai</ui:radioButton>
			<ui:radioButton value="Korin">Korin</ui:radioButton>
			<ui:radioGroupOther />
		</ui:radioGroup>
	</ui:formGroup>
	
	<ui:formGroup i18n_title="Peer Review" id="peer">
			<ui:switch name="yamchaIsUseless" i18n_title="Do you agree that Yamcha is pretty useless?">
				<ui:switchItem value="Yes" checked="true">Yes</ui:switchItem>
				<ui:switchItem value="Yep">Yep</ui:switchItem>
			</ui:switch>
			
			<ui:textarea name="reasons" i18n_title="If you responded yes, please provide a little summary as to why he is useless.">
				Because reasons...
			</ui:textarea>
			
			<ui:textarea name="manyReasons" type="richBasic" i18n_title="If you responded yep, please provide a little summary as to why he is useless.">
				<p>
					Because many reasons...
				</p>
			</ui:textarea>
			
			<ui:textarea name="complexReasons" type="rich" i18n_title="If you wanted to respond with both answers, please provide a little summary as to why he is useless.">
				<p>
					Because complex reasons...
				</p>
			</ui:textarea>
	</ui:formGroup>
			
</ui:formPage>