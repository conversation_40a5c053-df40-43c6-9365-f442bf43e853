<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<%@ include file="/WEB-INF/spiralRobot/jsp/grid/grid_paging.jsp"%>

<script type="text/javascript">
	function ${gridID}_toggleAllGridCheck(checked)
	{
		$.ajax({
			type : "POST",
			url : "${siteElement.fullPath}.htm",
			data : {action:'<o:encrypt action="myContactsCheckToggleAll" />', gridId: '${gridID}', gridInstanceId: '${gridInstanceId}', checked: checked,  gridFilters: ${gridID}_getAppliedFilters()},
			async : false,
			dataType : "html",
			success : function(data, textStatus, xhr) {
				if (orbisAppSr.checkAjaxResponse(xhr)) {
					$("input.ms_check").each(function(){
						this.checked = checked;
					});
					${gridID}_updateSelectedCount();
				} else {
					orbisAppSr.alertDialog("<spring:message code="i18n.dashboard_staffMyContactsAjax.servercomm3743570986844985" javaScriptEscape="true"/>: "
							+ textStatus);
				}
			}
		});
	}
	
</script>

<table class="table table-bordered table-hover">
	<thead>
		<tr>
			<td></td>
			<th colspan="6" class="noWrap">
				<orbis:message code="i18n.dashboard_staffMyOrgDiv.INTERACTIONS" />
			</th>
			<th colspan="2" class="noWrap">
				<orbis:message code="i18n.dashboard_staffMyContactsAjax.CURRENTTERM" />
			</th>
			<th colspan="10" class="noWrap">
				<orbis:message code="i18n.dashboard_staffMyOrgDiv.CONTACTINFORMATION" />
			</th>
		</tr>
		<tr class="tablesorter-headerRow">
			<th>
				<label for="${gridID}_selectAll" style="font-size: 0; position: absolute;">Select All</label>
				<input id="${gridID}_selectAll" type="checkbox" onclick="${gridID}_toggleAllGridCheck(this.checked); ">
			</th>
			<orbis:gridTh property="virtual.14" sortType="number">
				<orbis:message code="i18n.dashboard_staffMyOrgDiv.Alerts" />
			</orbis:gridTh>
			<orbis:gridTh property="virtual.15" sortType="number">
				<orbis:message code="i18n.dashboard_staffMyOrgDiv.Risks" />
			</orbis:gridTh>
			<orbis:gridTh property="virtual.16" sortType="number">
				<orbis:message code="i18n.dashboard_staffMyOrgDiv.Tasks" />
			</orbis:gridTh>
			<orbis:gridTh property="virtual.17" sortType="number">
				<orbis:message code="i18n.dashboard_staffMyOrgDiv.Messages" />
			</orbis:gridTh>
			<orbis:gridTh property="virtual.18" sortType="number">
				<orbis:message code="i18n.dashboard_staffMyOrgDiv.Notes" />
			</orbis:gridTh>
			<orbis:gridTh property="virtual.19" sortType="number">
				<orbis:message code="i18n.dashboard_staffMyContactsAjax.Forms" />
			</orbis:gridTh>
			<orbis:gridTh property="virtual.13" sortType="number">
				<orbis:message code="i18n.dashboard_staffMyContactsAjax.ofPostings" />
			</orbis:gridTh>
			<orbis:gridTh property="virtual.20" sortType="number">
				<orbis:message code="i18n.dashboard_staffMyContactsAjax.ofCoopHires" />
			</orbis:gridTh>
			<orbis:gridTh property="cu.company.organization.name">
				<orbis:message code="i18n.dashboard_staffMyOrgDiv.Organization" />
			</orbis:gridTh>
			<orbis:gridTh property="cu.company.name">
				<orbis:message code="i18n.dashboard_staffMyOrgDiv.Division" />
			</orbis:gridTh>
			<orbis:gridTh property="cu.user.firstName">
				<orbis:message code="i18n.common.firstName" />
			</orbis:gridTh>
			<orbis:gridTh property="cu.user.lastName">
				<orbis:message code="i18n.common.lastName" />
			</orbis:gridTh>
			<orbis:gridTh property="cu.user.jobTitle">
				<orbis:message code="i18n.common.jobTitle" />
			</orbis:gridTh>
			<orbis:gridTh property="cu.user.userStatus">
				<orbis:message code="i18n.dashboard_staffMyContactsAjax.ContactStatus" />
			</orbis:gridTh>
			<orbis:gridTh property="cu.user.city">
				<orbis:message code="i18n.dashboard_staffMyContactsAjax.ContactCity" />
			</orbis:gridTh>
			<orbis:gridTh property="cu.user.phoneNumber">
				<orbis:message code="i18n.dashboard_staffMyContactsAjax.PhoneNumber" />
			</orbis:gridTh>
			<orbis:gridTh property="cu.company.organization.userStatus">
				<orbis:message code="i18n.dashboard_staffMyContactsAjax.OrgStatus" />
			</orbis:gridTh>
			<orbis:gridTh property="cu.company.userStatus">
				<orbis:message code="i18n.dashboard_staffMyContactsAjax.DivStatus" />
			</orbis:gridTh>

		</tr>
	</thead>
	<tbody>
		<c:if test="${numberOfRecords > 0}">
			<c:forEach var="g" items="${gridData}">
				<tr>
					<td>
						<label for="${gridID}_record${g[0]}" style="font-size: 0; position: absolute;">Select record ${g[0]}</label>
						<input id="${gridID}_record${g[0]}" type="checkbox" class="ms_check" onclick="${gridID}_toggleGridCheck(this.checked, ${g[0]}, ''); "
							<c:if test="${gridChecks[gridInstanceId][g[0]]}">checked</c:if>>
					</td>
					<td>
						<c:if test="${not empty g[14] && g[14] > 0}">
							<a href="javascript:void(0)"
								onclick="loadInteractionCountsModal(null, '${g[0]}', null, null, null, null, null, 'Alerts', '${fn:replace(g[6],'\'','\\\'')} ${g[7]}')">
								<span class="badge badge-inverse">${g[14]}</span>
							</a>
						</c:if>
						<c:if test="${empty g[14] || g[14] == 0}">0</c:if>
					</td>
					<td>
						<c:if test="${not empty g[15] && g[15] > 0}">
							<a href="javascript:void(0)"
								onclick="loadInteractionCountsModal(null, '${g[0]}', null, null, null, null, null, 'Risks', '${fn:replace(g[6],'\'','\\\'')} ${g[7]}')">
								<span class="badge badge-inverse">${g[15]}</span>
							</a>
						</c:if>
						<c:if test="${empty g[15] || g[15] == 0}">0</c:if>
					</td>
					<td>
						<c:if test="${not empty g[16] && g[16] > 0}">
							<a href="javascript:void(0)"
								onclick="loadInteractionCountsModal(null, '${g[0]}', null, null, null, null, null, 'Tasks', '${fn:replace(g[6],'\'','\\\'')} ${g[7]}')">
								<span class="badge badge-inverse">${g[16]}</span>
							</a>
						</c:if>
						<c:if test="${empty g[16] || g[16] == 0}">0</c:if>
					</td>
					<td>
						<c:if test="${not empty g[17] && g[17] > 0}">
							<a href="javascript:void(0)"
								onclick="loadInteractionCountsModal(null, '${g[0]}', null, null, null, null, null, 'Messages', '${fn:replace(g[6],'\'','\\\'')} ${g[7]}')">
								<span class="badge badge-inverse">${g[17]}</span>
							</a>
						</c:if>
						<c:if test="${empty g[17] || g[17] == 0}">0</c:if>
					</td>
					<td>
						<c:if test="${not empty g[18] && g[18] > 0}">
							<a href="javascript:void(0)"
								onclick="loadInteractionCountsModal(null, '${g[0]}', null, null, null, null, null, 'Notes', '${fn:replace(g[6],'\'','\\\'')} ${g[7]}')">
								<span class="badge badge-inverse">${g[18]}</span>
							</a>
						</c:if>
						<c:if test="${empty g[18] || g[18] == 0}">0</c:if>
					</td>
					<td>
						<c:if test="${not empty g[19] && g[19] > 0}">
							<a href="javascript:void(0)"
								onclick="loadInteractionCountsModal(null, '${g[0]}', null, null, null, null, null, 'Forms', '${fn:replace(g[6],'\'','\\\'')} ${g[7]}')">
								<span class="badge badge-inverse">${g[19]}</span>
							</a>
						</c:if>
						<c:if test="${empty g[19] || g[19] == 0}">0</c:if>
					</td>
					<td>
						<c:if test="${not empty g[13]}">${g[13]}</c:if>
						<c:if test="${empty g[13]}">0</c:if>
					</td>
					<td>
						<c:if test="${not empty g[20]}">${g[20]}</c:if>
						<c:if test="${empty g[20]}">0</c:if>
					</td>
					<td>
						<o:nav anchor="true" action="displayOrganization" organizationId="${g[2]}" siteElement="${acrmAdminSe}">${g[1]}</o:nav>
					</td>
					<td>
						<o:nav anchor="true" action="displayDivision" divisionId="${g[4]}" siteElement="${acrmAdminSe}">${g[3]}</o:nav>
					</td>
					<td>${g[6]}</td>
					<td>
						<o:nav anchor="true" action="displayContactOverview" userId="${g[0]}" siteElement="${acrmAdminSe}">${g[7]}</o:nav>
					</td>
					<td>${g[12]}</td>
					<td>${g[5]}</td>
					<td>${g[21]}</td>
					<td>${g[9]}</td>
					<td>${g[10]}</td>
					<td>${g[11]}</td>

				</tr>
			</c:forEach>
		</c:if>
		<c:if test="${numberOfRecords == 0}">
			<tr>
				<td colspan="16">
					<div class="alert alert-info">
						<orbis:message code="i18n.dashboard_staffMyContactsAjax.NoRecordsFound" />
					</div>
				</td>
			</tr>
		</c:if>
	</tbody>
</table>
