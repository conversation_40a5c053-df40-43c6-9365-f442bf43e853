<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<orbis:addComponent component="orbisTools" />

<c:set scope="request" var="contentHeaderTitle"
	value="${siteElement.elementTitle}" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/util_viewContentHeader.jsp" />
<c:if test="${not empty successMessage}">
	<ui:notification type="success">${successMessage}</ui:notification>
</c:if>

<ui:switch name="sw1" i18n_title="Some Switch title">
	<ui:switchItem checked="true" classes="someClass" value="SomeValue">
		I'm BIG
	</ui:switchItem>
	<ui:switchItem checked="true" classes="someClass" value="SomeValue">
		I'm not
	</ui:switchItem>
</ui:switch>


<c:if test = "${ currentUser.root }">
	<div>
		<o:nav anchor = "true" action = "test" anchorClass = "btn btn-primary">
			Test
		</o:nav>
	</div>
</c:if>

<div class="box boxContent">

	<div class="well">
		CURRENT LOCALE: <B>${orbisLocale}</B>
	</div>

	<ul>
		<li>
			<o:nav anchor="true" action="displayMissingFrTranslations">
				<b>Localization: Check for missing FR translations</b>
			</o:nav>
		</li>
		<li>
			<o:nav anchor="true" action="runCompetencyMigration">
				<b>Run Competency Migration</b>
			</o:nav>
		</li>
		
		<li>
			<a href="javascript:void(0)" onclick="orbisAppSr.showConfirmModal('Are you sure?', function(){<o:nav action='runQuestionClean' />})">
				<b>Question Frameworks - Remove Whitespace</b>
			</a>
		</li>

		<li>
			<o:nav anchor="true" action="displayLoadingOverlayDelayTest">
				<b>Loading Overlay Delay Test</b>
			</o:nav>
		</li>

		<li>
			<o:nav anchor="true" action="displayForceSpiralRobotTest">
				<b>Test Force Spiral Robot</b>
			</o:nav>
		</li>

		<li>

			<o:nav anchor="true" action="displayAbnTestingPage">
				<b>ABn Campaign Test</b>
			</o:nav>
		</li>

		<li>
			<o:nav anchor="true" action="displaySimpleHeaderTag">
				<b>Simple Header Tag</b>
			</o:nav>
		</li>

		<li>
			<o:nav anchor="true" action="displayDataViewerDemo">
				<b>Data Viewer Demo</b>
			</o:nav>
		</li>

		<li>
			<o:nav anchor="true" action="displayCheckboxGroupTesting">
				<b>ui:checkboxGroup</b>
			</o:nav>
		</li>

		<li><o:nav anchor="true" action="displayBreadcrumbTrackerPage" reset="true">
				<b>Breadcrumb Tracker</b>
			</o:nav>
		</li>
		<li><o:nav anchor="true" action="displaySpiralRobotDocs">
				<b>Spiral Robot Documentation</b>
			</o:nav>
		</li>
		<li><o:nav anchor="true" action="displaySpiralRobotConversionCheatSheet">
				<b>Spiral Robot Conversion Cheat Sheet</b>
			</o:nav>
		</li>
		
		<li><o:nav anchor="true" action="displaySpiralRobotConversionToolSample">
				<b>Spiral Robot Conversion Tool Sample</b>
			</o:nav>
		</li>		
		<li>
			<o:nav anchor="true" action="displaySpiralRobotPOC">
				<b>Spiral Robot Proof of Concept</b>
			</o:nav>
		</li>		
		<li><o:nav anchor="true" action="displayMinhTest">
				<b>Minh Test</b>
			</o:nav>
		</li>
		
		<li><b>Student Qualifications</b>
			<ul>
				<li><o:nav anchor="true" action="displayQualificationTests" qualifierEntityClassName="com.orbis.test.qf.TestQFEntity" qualifierEntityId="1">
					<b>Qualification Tests</b></o:nav>
				</li>
				<li>
					<ui:modal id="stuQualModal" i18n_title="Test Student Qualifications" includeClose="true">
						<form enctype="multipart/form-data" class="form-horizontal"
							method="post" id="stuQualForm">
							<o:encrypt input="true" action="testStudentQualification" />
							<div class="control-group">
								<label class="control-label" for="studentUsername">
										Student Username </label>
								<div class="controls">
									<input type="text" name="studentUsername" id="studentUsername" />
								</div>
							</div>
						</form>
						<ui:button class="btn btn-primary"
								onclick="$('#stuQualForm').submit();">Save changes</ui:button>
					</ui:modal>
					<ui:button onclick="orbisAppSr.showModal('stuQualModal')"> <b>Test Student Qualifications</b> </ui:button>
				</li>
				<li>
					<ui:sidebar id="qfStudentQualificationsSidebar" 
						i18n_title="i18n.exp_posting_selfPlace.Seeifyouqu9309407817135365"
						location="right">
						<c:set var="ajaxCheckStudentQualificationsTag">
							<ui:ajax id="qfStudentQualifications" hideLoadButton="true" autoLoad="false"
								action="ajaxCheckStudentQualifications"
								qualifierEntityClassName="com.orbis.test.qf.TestQFEntity"
								qualifierEntityId="1"
								studentEntityClassName="com.orbis.test.qf.TestQFStudentEntity"
								studentEntityId="${studentPostingQualAnswers.id}" />
						</c:set>
						<%@ include file="/WEB-INF/jsp/qf/qf_studentQualificationSidebar.jsp"%>
					</ui:sidebar>
					<ui:button show="qfStudentQualificationsSidebar" onclick="openQFStudentQualifySidebar();">
						<b><orbis:message code="i18n.exp_posting_selfPlace.Seeifyouqu9309407817135365" /></b>
					</ui:button>
				</li>
				<li>
					<o:nav anchor="true" action="displayStudentQualificationsWaitingForApproval" qualifierEntityClassName="com.orbis.test.qf.TestQFEntity" qualifierEntityId="1">
						<b><orbis:message code="i18n.qf_studentQualificationsWaitingForApproval.Studentqua3002055872707126"/></b>
					</o:nav>
				</li>
			</ul>
		</li>
		
		<li><o:nav anchor="true" action="displaySpiralRobot">
				<b>Spiral Robot</b>
			</o:nav>
			<ul>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotOrbisApp">
						OrbisApp
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotLayoutGrid">
						Layout Grid
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotForm">
						Form
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotDatePicker">
						Date Picker
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotCards">
						Cards
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotTabs">
						Tabs
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotSidebar">
						Sidebar
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotButtons">
						Buttons
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotModalsAndNotifications">
						Modals and Notifications
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotHeader">
						Header
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotNotes">
						Notes
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotPanels">
						Panels
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotFullscreen">
						Fullscreen
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displayUiAjaxTag">
						Ajax
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displayUiIconPicker">
						Icon Picker
					</o:nav>
				</li>
				
				<li>
					<o:nav anchor="true" action="displaySpiralRobotAccordion">
						Accordion
					</o:nav>
				</li>
				
				<li>
					<o:nav anchor="true" action="displaySpiralRobotDateCard">
						Date Card
					</o:nav>
				</li>
				
				<li>
					<o:nav anchor="true" action="displaySpiralRobotCharts">
						Charts
					</o:nav>
					<ul>
						<li>
							<o:nav anchor="true" action="displaySpiralRobotSimpleLineChart">
								Simple Line Chart
							</o:nav>
						</li>
					</ul>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotLargeHeader">
						Large Header
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotWizard">
						Wizard
					</o:nav>
				</li>
				
				<li>
					<o:nav anchor="true" action="displaySpiralRobotListCards">
						List Card
					</o:nav>
				</li>
				
				<li>
					<o:nav anchor="true" action="displaySpiralRobotFormPage">
						Form Page
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotCheckboxGroup">
						Checkbox Group
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotProgressBar">
						ProgressBar
					</o:nav>
				</li>
			</ul>
		</li>
		<li><b>Custom JQuery plugins</b>
			<ul>
				<li><o:nav anchor="true" action="displayCustomJQueryIsVisible">
						Is Visible
					</o:nav></li>
			</ul>
		</li>
		<li><b>Spiral Robot Javascript Tests</b>
			<ul>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotJsTestsTouchEvents">
						Touch Events
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotJsTestsCalendar">
						Calendar
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotJsTestsLayerPrioritization">
						Layer Prioritization
					</o:nav>
				</li>
				<li>
					<o:nav anchor="true" action="displaySpiralRobotJsTestsLoadingIcon">
						Loading Icon
					</o:nav>
				</li>
			</ul>
		</li>
		<li>
			<strong>
				<o:nav anchor="true" action="displayWizardTag">
					Wizard Tag
				</o:nav>
			</strong>
		</li>
		<li><o:nav anchor="true" action="displayQuestionHome">
				<B>Question2 Framework Tests</B>
			</o:nav></li>
		<li><o:nav anchor="true" action="displayGridTest">
				<b>JQGrid Test</b>
			</o:nav></li>
		<li><a href="javascript:void(0)"
			onclick="orbisTools.showBookmarks();"> <b>Orbis Bookmarklets</b>
		</a></li>
		<li><o:nav anchor="true" action="displayWordTemplateTest">
				<b>Word Template Test</b>
			</o:nav></li>
		<li><o:nav anchor="true" action="displayGridJSONEditor">
				<b>Grid JSON Editor</b>
			</o:nav></li>
		<li><B>CKEditor Tests</B>
			<ul>
				<li><o:nav anchor="true" action="displayCkEditor_test1">
						Test #1 - CKEditor initialized with javascript.
					</o:nav></li>
				<li><o:nav anchor="true" action="displayCkEditor_test2">
						Test #2 - CKEditor initialized with <B>&lt;ckeditor:editor&gt;</B> TAG.
					</o:nav></li>
				<li><o:nav anchor="true" action="displayCkEditor_test3">
						Test #3 - CKEditor initialized with <B>&lt;ckeditor:editor&gt;</B> TAG, and integrated with CKFinder using the <B>&lt;ckfinder:setupCKEditor&gt;</B>
					TAG.
					</o:nav></li>
				<li><o:nav anchor="true" action="displayCkEditor_test4">
						Test #4 - CKEditor initialized by the <B>orbisEditor</B> JQuery extension.
					</o:nav></li>
				<li><o:nav anchor="true" action="displayCkEditor_test5">
						Test #5 - CKEditor initialized by our <B>/WEB-INF/spiralRobot/jsp/ckeditor.jsp</B> sub-jsp.
					</o:nav></li>
				<li><o:nav anchor="true" action="displayCkEditor_test6">
						Test #6 - Demonstration of the "char counter
					</o:nav></li>
				<li><o:nav anchor="true" action="displayCkEditor_test7">
						Test #7 - Changes that James requested
					</o:nav></li>
			</ul></li>

		<li><o:nav anchor="true" action="displayFormToBuildFormConverter">
				<b> Convert forms to orbisAppSr.buildForm </b>
			</o:nav></li>
			<li><o:nav anchor="true" action="displayPredicateLog">
				<b>Predicate Log</b>
			</o:nav></li>
		<li><B>Form Tester</B>
			<ul>
				<li><o:nav anchor="true" action="displayFormTestSender">
						FormTester
					</o:nav></li>
			</ul></li>

		<li><o:nav anchor="true" action="displayCharts">
				Highcharts Tester
			</o:nav></li>

		<li><B>Full Calendar Tests</B>
			<ul>
				<li><o:nav anchor="true" action="displayFullCalendar">
						Test #1 - Displaying Calendar, and calendar in dialog box.
					</o:nav></li>
				<li><o:nav anchor="true" action="displayFullCalendar2">
						Test #2 - Adding buttons to add dates.
					</o:nav></li>
				<li><o:nav anchor="true" action="displayFullCalendar3">
						Test #3 - Adding dates with a fullCalendar object and from a source stream.
					</o:nav></li>
				<li><o:nav anchor="true" action="displayFullCalendar4">
						Test #4 - Adding dates with a fullCalendar object and from multiple source streams.
					</o:nav></li>
			</ul></li>

		<li><B>More Calendar Stuff...</B>
			<ul>
				<li><o:nav anchor="true" action="displayOrbisCalendar">
						Orbis Calendar
					</o:nav></li>
				<li><o:nav anchor="true" action="displayOrbisAgendaCalendar">
						Orbis Agenda Calendar
					</o:nav></li>
			</ul></li>

		<li><B>Grid Mobile Button Tests</B>
			<ul>
				<li><o:nav anchor="true" action="search" subAction="search"
						showMobile="true">
						Grid Mobile Button
					</o:nav></li>
			</ul></li>

		<li><B></B> <o:nav anchor="true"
				action="displayGoogleMapTestHome">
				Google Maps API
			</o:nav></li>

		<li><a href="javascript:void(0);"
			onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayJspConatinsSiteCode" />">
				Site Code Tools - Look for jsp files that contains sitecontent </a></li>

		<li><B>I18n Tools</B>
			<ul>
				<li><a href="javascript:void(0);"
					onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayLookForText" method="2" />">
						Step 1: Method 2 - picks up static strings like </a></li>
				<li><a href="javascript:void(0);"
					onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayLookForText" method="3" />">
						Step 2: Method 3 - picks up static strings from orbisApp dialogs
						like orbisAppSr.displaySuccessMessage("Updated successfully!") </a></li>
				<li><a href="javascript:void(0);"
					onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayLookForText" method="1" />">
						Step 3: Method 1 - picks up static strings using other use cases -
						FINAL scan </a></li>
				<li><o:nav anchor="true"
						action="displayPropertyEngFrComparator">
						Step 4: Look for missing usages between Core*.properties files
					</o:nav></li>
				<li><a href="javascript:void(0);"
					onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayTranslateFromDuplicates" />">
						Step 5: (OPTIONAL) Find untranslated keys (from FR prop file) &
						Translate them manually </a></li>
				<li><a href="javascript:void(0);"
					onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>',5000); <o:nav action="displayTranslatedXlsFileFromFrench" />">
						Step 6: Create an EXCEL file from Step 5. Untranslated keys with
						possible fr translations from coreMessages_fr property file </a></li>
				<li><a href="javascript:void(0);"
					onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="storeUniquePropertiesStep2" />">
						Step 7: Import translation EXCEL file (obtained from translator or
						file from Step 6) </a></li>
				<li><a href="javascript:void(0);"
					onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>',2000); <o:nav action="storeUniquePropertiesStep1" />">
						Step 8: Export new EXCEL file that needs to be translated (FINAL
						OUTPUT) </a></li>
				<li>Eliminate duplicate rows in the properties files:
					<ul>
						<li><a href="javascript:void(0);"
							onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayEliminatePropertiesDuplicateRows" language="en" />">
								coreMessages.properties </a></li>
						<li><a href="javascript:void(0);"
							onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayEliminatePropertiesDuplicateRows" language="fr" />">
								coreMessages_fr.properties </a></li>
					</ul>
				</li>
				<li>Look for duplicate Strings in:
					<ul>
						<li><a href="javascript:void(0);"
							onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayPropertyDuplicates" language="en" />">
								coreMessages.properties </a></li>
						<li><a href="javascript:void(0);"
							onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayPropertyDuplicates" language="fr" />">
								coreMessages_fr.properties </a></li>
					</ul>
				</li>
				<li>Commonize core messages that have similar content
					<ul>
						<li><a href="javascript:void(0);"
							onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="eliminatePropertiesDuplicateStrings2" />">
								Replace commonized i18n codes in jsps (Preformatted) </a></li>
						<li><a href="javascript:void(0);"
							onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayEliminatePropertiesDuplicateStrings" />">
								Replace commonized i18n codes in jsps </a></li>
						<li><a href="javascript:void(0);"
							onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayTrimCoreMessages" language="fr" />">
								Trim unnecessary characters from english and french core
								messages </a></li>
					</ul>
				</li>
				<li>Look for jsp files that contains translations
					<ul>
						<li><a href="javascript:void(0);"
							onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayJspConatinsI18n" language="en" />">
								jsp programming errors </a></li>
						<c:if
							test="${fn:contains(siteLanguage, 'en') && fn:contains(siteLanguage, 'fr')}">
							<li><a href="javascript:void(0);"
								onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayJspConatinsI18n" language="fr" />">
									Strings missing in coreMessages_fr.properties </a></li>
						</c:if>
					</ul>
				</li>
				<li>Look for untranslated files
					<ul>
						<li><o:nav anchor="true" action="displayInputJspFile">
								Enter a jsp file to translate
							</o:nav></li>

						<li><a href="javascript:void(0);"
							onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayManualTranslation" />">
								Manual Translation Page - JSP & Java </a></li>
						<li><a href="javascript:void(0);"
							onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayManualTranslationJs" />">
								Manual Translation Page - JavaScript </a></li>
						<li><a href="javascript:void(0);"
							onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayManualTranslationJavas" />">
								Look for Java Files </a></li>
					</ul>

				</li>
				<li>Alphabetize Properties File.
					<ul>
						<li><a href="javascript:void(0);"
							onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayReOrganizeProperties" language="en" />">
								English coreMessages </a></li>
						<li><a href="javascript:void(0);"
							onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayReOrganizeProperties" language="fr" />">
								French coreMessages </a></li>
					</ul>
				</li>
				<li>Look for orphaned keys
					<ul>
						<li><a href="javascript:void(0);"
							onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="populateJavaKeysList" />">
								Update list of keys found in .java files </a></li>
						<li><a href="javascript:void(0);"
							onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayLookForOrphans" />">
								Remove orphaned keys from .properties files </a></li>
					</ul>
				</li>
				<li><a href="javascript:void(0);"
					onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayJspMissingInclude" />">
						Look for JSP files that are translated but missing the include.jsp
				</a></li>
				<li><a href="javascript:void(0);"
					onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="displayAddMarkedTranslationEntries" />">
                    Add translation entries for text in JSP's wrapped with '<& &>'
                </a></li>
                <li>
					<o:nav anchor="true" action="displaySwapL1L2">
						Swap primary and secondary language values
					</o:nav>
				</li>	
				<li><o:nav anchor="true" action="displaySwapDashboardModule">
					Swap Dashboard primary and secondary language values
				</o:nav></li>
			</ul></li>
		<li><o:nav anchor="true" action="displayRedirectTest">
				Redirection Tests
			</o:nav></li>
		<li><o:nav anchor="true" action="testRome">
				Test ROME
			</o:nav></li>

		<li><o:nav anchor="true" action="displayFindAssignedTypes">
				Find Assigned Types
			</o:nav></li>
		<li><o:nav anchor="true" action="displayQueryHelper">
				Query Helper
			</o:nav></li>
		<li><o:nav anchor="true" action="displayNTransferHome">
				MySQL 2 SQL Server Transfer
			</o:nav></li>

		<li><B>Tree Widget Tests</B>
			<ul>
				<li><o:nav anchor="true" action="displayTreeTest1">
						Tree Widget: Test #1
					</o:nav></li>
				<li><o:nav anchor="true" action="displayTreeTest2">
						Tree Widget: Test #2 (using TreeWidgetUtils)
					</o:nav></li>
			</ul></li>

		<li><o:nav anchor="true" action="displayUTResult">
				U Of T Test - SOAP 1.2 Result
			</o:nav></li>
		<li><o:nav anchor="true" action="displayJQueryValidate">
				JQuery Validation Tests
			</o:nav></li>
		<li><o:nav anchor="true" action="displayEntityImportExportTest">
				Entity Import/Export - Import/Export Test Answer
			</o:nav></li>

		<li><o:nav anchor="true" action="displayTestDay1">
				Debbie Tests
			</o:nav></li>
		<li><o:nav anchor="true" action="displayDougTestCompanyList">
				Doug's Tests
			</o:nav></li>
		<li><o:nav anchor="true" action="displayRegexHome">
				Regex Utils
			</o:nav></li>
		<li><B>Orbis Messages</B>
			<ul>
				<li><o:nav anchor="true"
						action="displayOrbisMessagesJavascript">Javascript</o:nav></li>
				<li><o:nav anchor="true" action="displayOrbisMessagesOrbisTags">Orbis Tags</o:nav></li>
			</ul></li>
		<li><o:nav anchor="true" action="displayJQueryValidationExample">Jquery Validation</o:nav>
		</li>
		<li><B>Common Site Bars</B>
			<ul>
				<li><o:nav anchor="true" action="displayNavigationBar">Navigation
						Bar</o:nav></li>
				<li><o:nav anchor="true" action="displayActionsBar">Actions
						Bar</o:nav></li>
			</ul></li>
		<li><o:nav anchor="true" action="displayOrbisDropdown">
				Orbis Dropdown
			</o:nav></li>
		<li><B>Orbis Page Structures</B>
			<ul>
				<li><o:nav anchor="true"
						action="displayOrbisPageStructures_basic">Basic</o:nav></li>
				<li><o:nav anchor="true"
						action="displayOrbisPageStructures_tables">Tables</o:nav></li>
				<li><o:nav anchor="true"
						action="displayOrbisPageStructures_tabs">Tabs</o:nav></li>
				<li><o:nav anchor="true"
						action="displayOrbisPageStructuresDatePicker">Date Picker</o:nav></li>
				<li><o:nav anchor="true"
						action="displayOrbisPageStructuresSimpleJSDatePicker">JS Simple Date Picker</o:nav></li>
				<li><o:nav anchor="true"
						action="displayOrbisPageStructuresAutoComplete">AutoComplete</o:nav></li>
				<li><o:nav anchor="true"
						action="displayOrbisPageStructuresCheckboxWidget">Checkbox Widget</o:nav></li>
			</ul></li>
		<li><o:nav anchor="true" action="displayTestFU">
				Orbis file Upload filter
			</o:nav></li>
		<li><B>Orbis Tags</B>
			<ul>
				<li><o:nav anchor="true" action="displayOrbisDateFormatTag">Date
						Format</o:nav></li>
			</ul></li>

		<li><o:nav anchor="true" action="displayTableSorterTest">
				Table Sorter Test
			</o:nav></li>

		<li><o:nav anchor="true" action="displayOrbisDialogsTest">
				Orbis Dialogs Test
			</o:nav></li>
		<li><o:nav anchor="true" action="displayLdapTest">
				LDAP Test
			</o:nav></li>
		<li><o:nav anchor="true" action="displayWebCalTest">
				WebCal Tests
			</o:nav></li>
		<li><o:nav anchor="true" action="displayScrollSpyDemo">
				ScrollSpy Widget Demo
			</o:nav></li>
		<li><a href="javascript:void(0);"
			onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="catchPortalHelp" />">
				Catch Portal Help Messages </a></li>
		<li><a href="javascript:void(0);"
			onclick="orbisAppSr.showLoadingOverlay('<orbis:message code="i18n.common.processingPleaseWait" javaScriptEscape="true"/>'); <o:nav action="sendPortalHelpToDemo" />">
				Push Help Messages to Demo </a></li>
		<li><o:nav anchor="true" action="displayTestApplicationBundling">
				Test Application Bundling
			</o:nav></li>
		<li><o:nav anchor="true" action="displayVikTest">
				Vik Test
			</o:nav></li>
		<li><o:nav anchor="true" action="displayOrbisGridTest">
				Orbis Grid Test
			</o:nav></li>

		<li><o:nav anchor="true" action="displayPDFJSTest1">PDF JS</o:nav>
		</li>

		<li><o:nav anchor="true" action="displayLastViewedTests">Testing Last Viewed</o:nav>
		</li>


		<li><o:nav anchor="true" action="displayNathansTestingGrounds">Nathan's Testing Grounds</o:nav>
		</li>
		<li><o:nav anchor="true" action="displayScottsTestingGrounds"><strong>Scott's Testing Grounds</strong></o:nav>
		</li>
		<li><o:nav anchor="true" action="displayOrbisAjaxTagTest">orbis:ajax tag</o:nav>
		</li>
		<li><o:nav anchor="true" action="displayOrbisTripleLookupTagTest">orbis:tripleLookup tag</o:nav>
		</li>
		<li><o:nav anchor="true" action="displayOrbisSideBarTagTest">orbis:sideBar tag</o:nav>
		</li>
		<li><b>Dylan's Tests</b>

			<ul>
				<li><o:nav anchor="true" action="processAccessibility">Accessibility Test</o:nav>
				</li>
				<li><o:nav anchor="true" action="displayAccessibilityTest">Accessibility Test for Single File</o:nav>
				</li>
			</ul></li>

		<li><b>Websockets Test</b>
			<ul>
				<li><o:nav anchor="true" action="displayTestEndpoint">test Endpoint</o:nav>
				</li>
			</ul></li>
		<li>
			<o:nav anchor="true" action="displayJuansTests">Juan's Tests</o:nav>
		</li>		
		<li>
			<o:nav anchor="true" action="displayCCPrototyping" >CC Prototyping - Admin</o:nav>
			<ui:dropdown i18n_title="Other Pages">
				<ui:dropdownItem action="displayCCPrototyping" subAction="employer">Employer</ui:dropdownItem>
				<ui:dropdownItem action="displayCCPrototyping" subAction="student">Student</ui:dropdownItem>
				<ui:dropdownItem action="displayCCPrototyping" subAction="school">School</ui:dropdownItem>
				<ui:dropdownItem action="displayCCPrototyping" subAction="school_page">School Page</ui:dropdownItem>
			</ui:dropdown>
		</li>
		<li>
			<o:nav anchor="true" action="displayDocViewerTest">Doc Viewer Test</o:nav>
		</li>		
		<li>
			<o:nav anchor="true" action="displayEvansTests">Evan's Tests</o:nav>
		</li>		
		<li>
			<o:nav anchor="true" action="displayExpRedesignFSHomepage">Experiential Redesign - Field Supervisor - Homepage</o:nav>
		</li>
		<li>
			<o:nav anchor="true" action="displayExpRedesignEmployerHomepage">Experiential Redesign - Field Supervisor - Student Course Progress</o:nav>
		</li>		
		<li>
			<o:nav anchor="true" action="displayExpRedesignEmployerHomepage">Experiential Redesign - Employer - Homepage</o:nav>
		</li>		
	</ul>
</div>
