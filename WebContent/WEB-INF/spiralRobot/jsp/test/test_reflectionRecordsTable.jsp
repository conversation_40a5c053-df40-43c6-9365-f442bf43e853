<%@ include file="/WEB-INF/jsp/include.jsp"%>

<table class="table table-bordered">
	<thead>
		<tr>
			<th>
				<orbis:message code="i18n.test_reflectionRecordsTable.Actions5700472000793898" />
			</th>
			<th>
				<orbis:message code="i18n.test_reflectionRecordsTable.Assignment4314059644319780" />
			</th>
		</tr>	
	</thead>
	<tbody>
		<c:forEach var="r" items="${ records }">
			<tr>
				<td>
					<o:nav anchor="true" 
					       anchorClass="btn btn-mini btn-primary"
                           action="displayReflectionRecord" 
                           acrmReflectionRecordId="${ r.id  }"
                           target="_blank">
						<orbis:message code="i18n.test_reflectionRecordsTable.view8214032624411618" />
					</o:nav>				
				</td>
				<td>
					${ r.displayName }
				</td>
			</tr>
		</c:forEach>
	</tbody>
</table>