<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<c:set var="title"><spring:message code="i18n.test_testDay1.DebbiesTestonDay1" /> </c:set>
<%@ include file="test_commonHeader.jsp"%>



<BR>
<HR>

${now}
<BR><fmt:formatDate value="${now}" pattern="${orbisDateLong} ${orbisTimeShort2}"/>


	
	<table class="table table-striped table-bordered">
	<tr>
		<th>
			<orbis:message code="i18n.test_testDay1.Name" />
		</th>
		<th>
			<orbis:message code="i18n.test_testDay1.DatePosted" />
		</th>
		<th>
			<orbis:message code="i18n.test_testDay1.EndDate" /> 
		</th>
		<th>
			<orbis:message code="i18n.test_testDay1.PostedBy" /> 
		</th>
	</tr>	
	<tr>
		<td colspan="4" class="tableLine">
			&nbsp;
		</td>
	</tr>
	<c:forEach var="j" items="${jobs}">
	<tr>
		<td>${j.organization}</td>
		<td><fmt:formatDate value="${j.datePosted}" pattern="${orbisDateMedium}"/></td>
		<td><fmt:formatDate value="${j.dateDeadline}" pattern="${orbisDateMedium}"/></td>
		<td>${j.postedBy.firstName} ${j.postedBy.lastName}</td>		
	</tr>
	</c:forEach> 
</table>

