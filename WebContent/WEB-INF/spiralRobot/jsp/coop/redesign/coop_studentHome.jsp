<%@ include file="/WEB-INF/jsp/include.jsp"%>

<style type="text/css">
	.table--config-darker thead th {
		background: #666 !important; /* color--bg--grey--darker */
	}
</style>

<%@ include file="/WEB-INF/spiralRobot/jsp/coop/coop_studentActionBar.jsp"%>

<%@ include file="/WEB-INF/jsp/coop/redesign/coop_studentHome_header.jsp"%>

<c:set var="bilingualAdmittedStudentHomeContent">${(isL1) ? siteElement.contentItem.admittedStudentHomeContent : siteElement.contentItem.l2AdmittedStudentHomeContent}</c:set>
<c:if test="${not empty currentUser.assignedTypes['Co-op Student'] && not empty bilingualAdmittedStudentHomeContent}">
	<ui:note block="true" type="info" classes="margin--t--m">
		${bilingualAdmittedStudentHomeContent}
	</ui:note>
</c:if>

<c:if test="${not empty admission}">
	<%@ include file="/WEB-INF/jsp/coop/redesign/coop_recordInfo.jsp"%>
</c:if>
	
<c:if test="${not empty dfModelProgram.categories && dfModelProgram.canWrite}">
	<ui:formPage action="saveApplicationProgramQs">
		<input type="hidden" name="admissionId" value="${admission.id}">
		<input type="hidden" name="returnAction" value="displayHome">
		
		<c:set scope="request" var="DFModel" value="${dfModelProgram}" />
		<c:set scope="request" var="DFAnswerrEntity" value="${admission}" />
		<jsp:include page="/WEB-INF/spiralRobot/jsp/df/df_form1.jsp" />
	</ui:formPage>
</c:if>
<c:if test="${not empty sequences}">
	<ui:grid>
		<ui:gridCol width="7">
			<c:if test="${not empty admission}">
				<%@ include file="/WEB-INF/jsp/coop/redesign/coop_studentTerm.jsp"%>
			</c:if>
		</ui:gridCol>
		
		<ui:gridCol width="4" offset="1">
			
			<%@ include file="/WEB-INF/jsp/coop/redesign/coop_recordSeqTable.jsp"%>
			
		</ui:gridCol>
	</ui:grid>
</c:if>