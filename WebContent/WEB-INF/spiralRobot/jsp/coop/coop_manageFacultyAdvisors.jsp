<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>

<ui:manageItemsSidebar id="manageFacultyAdvisors"
	title="i18n.coop_manageFacultyAdvisors.FacultyAdv5361720119384397"
	callback="onProgramAdminSave();" action="ajaxSaveProgramAdmins"
	formData="programId" formDataValue="${program.id}"
	items="${allProgramAdmins}" checkboxName="memberIds"
	checklist="${users}" isManageUsers="true" />
 
<c:set var="instructionsContent">
	Lorem ipsum instructions content.
</c:set>
 
<ui:configurationListPage i18n_title="i18n.coop_manageFacultyAdvisors.ManageFacu6538214324298058">

	<ui:manageItemList useDefaultActions="true" action="updateModuleFaculty"  i18n_instructionsTitle="i18n.common.Instructions" i18n_instructionsContent="${instructionsContent}" id="manageFacultyAdvisorsList" i18n_title="i18n.coop_manageFacultyAdvisors.FacultyAdv5361720119384397">
		<ui:manageItemListCol headerColor="grey--dark" id="nonCoordinators"  i18n_title="i18n.coop_manageFacultyAdvisors.BNonMember9421338498095893">
			<c:forEach var="p" items="${nonFaculty}">
				<span class="js--user" data-value="${p.id}">
					<i class="material-icons margin--r--m" aria-hidden="true">face</i>
					${p.firstName} ${p.lastName} (${p.username})
				</span>
			</c:forEach>
		</ui:manageItemListCol>
		
		<ui:manageItemListCol headerColor="success" id="coordinators"  i18n_title="i18n.coop_manageFacultyAdvisors.BMemberFac32293898843870816">
			<c:forEach var="p" items="${faculty}">
				<span class="js--user" data-value="${p.id}">
					<i class="material-icons margin--r--m" aria-hidden="true">face</i>
					${p.firstName} ${p.lastName} (${p.username})
				</span>
			</c:forEach>
		</ui:manageItemListCol>
	</ui:manageItemList>
	
</ui:configurationListPage>