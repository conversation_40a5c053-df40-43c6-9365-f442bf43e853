<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>

<style>
	.nestedTable td {border:none;text-align:right;background-color:transparent !important;}
	.reportTitle {
		font-weight: bold;
		font-size: 2em;
	}
</style>


<ui:secondaryActionsGroup type="success" id="reportNavigation" aria-label="i18n.coop_recordsTermComparison.ReportNavi9009364120805552">
	<ui:dropdownItem onclick="$(window).scrollTop($('#one').offset().top -75)"><orbis:message code="i18n.coop_recordsTermComparison.EmploymentDataByTerm" /></ui:dropdownItem>
</ui:secondaryActionsGroup>

<ui:grid>
	<ui:gridCol classes="content" width="8">		
		<ui:panel id="one">
			<ui:panelTitle>
				<orbis:message code="i18n.coop_recordsTermComparison.EmploymentDataByTerm" />
			</ui:panelTitle>
			<div class="reportSubTitle"></div><br>
			<div id="placedBar"></div>
			<orbis:addComponent component="highCharts" />
			<script type="text/javascript">
				$(document).ready(function() {
					$("#placedBar").orbisChart(${placedBar});
				});
			</script>
		</ui:panel>
	</ui:gridCol>
</ui:grid>
