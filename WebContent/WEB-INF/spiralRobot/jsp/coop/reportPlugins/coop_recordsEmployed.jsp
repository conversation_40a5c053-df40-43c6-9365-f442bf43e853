<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>
<orbis:addComponent component="orbisNotePopups" />

<style>
	.nestedTable td {border:none;text-align:right;background-color:transparent !important;}
	.reportTitle {
		font-weight: bold;
		font-size: 2em;
	}
</style>

<ui:secondaryActionsGroup id="reportNavigation" aria-label="i18n.coop_recordsEmployed.ReportNavi1200919051641615" type="success">
	<c:if test="${not empty genderData}">
		<ui:dropdownItem onclick="$(window).scrollTop($('#one').offset().top -75)"><orbis:message code="i18n.coop_reports_coopAdmissionSeq.EmploymentByGender" /></ui:dropdownItem>
	</c:if>
	<c:if test="${not empty internationalData}">
		<ui:dropdownItem onclick="$(window).scrollTop($('#one').offset().top -75)"><orbis:message code="i18n.coop_reports_coopAdmissionSeq.Employment19613801128532948" /></ui:dropdownItem>
	</c:if>
	<c:if test="${not empty sfrPlacementData}">	
		<ui:dropdownItem onclick="$(window).scrollTop($('#one').offset().top -75)"><orbis:message code="i18n.coop_reports_coopAdmissionSeq.EmploymentByMethod" /></ui:dropdownItem>
	</c:if>
	<c:if test="${not empty citizenStatusData}">	
		<ui:dropdownItem onclick="$(window).scrollTop($('#one').offset().top -75)"><orbis:message code="i18n.coop_reports_coopAdmissionSeq.Employment09333750243922256" /></ui:dropdownItem>
	</c:if>
	<c:if test="${not empty industryData}">	
		<ui:dropdownItem onclick="$(window).scrollTop($('#one').offset().top -75)"><orbis:message code="i18n.coop_reports_coopAdmissionSeq.EmploymentByIndustry" /></ui:dropdownItem>
	</c:if>
	<c:if test="${not empty locationData}">	
		<ui:dropdownItem onclick="$(window).scrollTop($('#one').offset().top -75)"><orbis:message code="i18n.coop_reports_coopAdmissionSeq.EmploymentByLocation" /></ui:dropdownItem>
	</c:if>
	<c:if test="${not empty salaryData}">	
		<ui:dropdownItem onclick="$(window).scrollTop($('#one').offset().top -75)"><orbis:message code="i18n.coop_reports_coopAdmissionSeq.SalaryStatistics5163020530926359" /></ui:dropdownItem>
	</c:if>
</ui:secondaryActionsGroup>
<ui:grid>
	<ui:gridCol classes="content" width="6">
		<c:if test="${not empty genderData}">
			<ui:panel id="one">
				<ui:panelTitle>
					<orbis:message code="i18n.coop_reports_coopAdmissionSeq.EmploymentByGender" />
				</ui:panelTitle>
				<div class="reportSubTitle"></div><br>
				<div id="genderPie"></div>
				<orbis:addComponent component="highCharts" />
				<script type="text/javascript">
					$(document).ready(function() {
						$("#genderPie").orbisChart(${genderPie});
					});
				</script>
			</ui:panel>
		</c:if>
		 
		<c:if test="${not empty internationalData}">
			<ui:panel id="two" classes="margin--t--m">
				<ui:panelTitle>
					<spring:message code="i18n.coop_reports_coopAdmissionSeq.Employment19613801128532948" />
				</ui:panelTitle>
				<div class="reportSubTitle"></div><br>
				<div id="internationalPie"></div>
				<orbis:addComponent component="highCharts" />
				<script type="text/javascript">
					$(document).ready(function() {
						$("#internationalPie").orbisChart(${internationalPie});
					});
				</script>
			</ui:panel>
		</c:if>
		
		<c:if test="${not empty sfrPlacementData}">
			<ui:panel id="three" classes="margin--t--m">
				<ui:panelTitle>
					<orbis:message code="i18n.coop_recordsEmployed.EmploymentByMethod" />
				</ui:panelTitle>
				<div class="reportSubTitle"></div><br>
				<div id="sfrPlacementBar"></div>
				<orbis:addComponent component="highCharts" />
				<script type="text/javascript">
					$(document).ready(function() {
						$("#sfrPlacementBar").orbisChart(${sfrPlacementBar});
					});
				</script>
			</ui:panel>
		</c:if>
		 
		<c:if test="${not empty citizenStatusData}">
			<ui:panel id="four" classes="margin--t--m">
				<ui:panelTitle>
					<orbis:message code="i18n.coop_reports_coopAdmissionSeq.Employment09333750243922256" />
				</ui:panelTitle>
				<div class="reportSubTitle"></div><br>
				<div id="citizenStatusPie"></div>
				<orbis:addComponent component="highCharts" />
				<script type="text/javascript">
					$(document).ready(function() {
						$("#citizenStatusPie").orbisChart(${citizenStatusPie});
					});
				</script>
			</ui:panel>
		</c:if>
		
		<c:if test="${not empty industryData}">
			<ui:panel id="five" classes="margin--t--m">
				<ui:panelTitle>
					<orbis:message code="i18n.coop_reports_coopAdmissionSeq.EmploymentByIndustry" />
				</ui:panelTitle>
				<div class="reportSubTitle"></div><br>
				<div id="industryBar"></div>
				<orbis:addComponent component="highCharts" />
				<script type="text/javascript">
					$(document).ready(function() {
						$("#industryBar").orbisChart(${industryBar});
					});
				</script>
			</ui:panel>
		</c:if>
		
		<c:if test="${not empty locationData}">
			<ui:panel id="six" classes="margin--t--m">
				<ui:panelTitle>
					<spring:message code="i18n.coop_reports_coopAdmissionSeq.EmploymentByLocation" />
				</ui:panelTitle>
				<div class="reportSubTitle"></div><br>
				<div id="locationPie"></div>
				<orbis:addComponent component="highCharts" />
				<script type="text/javascript">
					$(document).ready(function() {
						$("#locationPie").orbisChart(${locationPie});
					});
				</script>
			</ui:panel>
		</c:if>
		
		<c:if test="${not empty salaryData}">
			<ui:panel id="seven" classes="margin--t--m">
				<ui:panelTitle>
					<orbis:message code="i18n.coop_reports_coopAdmissionSeq.SalaryStatistics5163020530926359" />
				</ui:panelTitle>	
					<ui:helpTip i18n_text="
					 	i18n.coop_reports_coopAdmissionSeq.06842661045664467 
						i18n.coop_reports_coopAdmissionSeq.5320927016938773  
						i18n.coop_reports_coopAdmissionSeq.5400399671052994  
						i18n.coop_reports_coopAdmissionSeq.2764173117173093  
						i18n.coop_reports_coopAdmissionSeq.9271187835067146  
						i18n.coop_reports_coopAdmissionSeq.73214573269815    
						i18n.coop_reports_coopAdmissionSeq.1216407971565574  
						i18n.coop_reports_coopAdmissionSeq.6619786081215404" />
					
				<ui:keyValueList>
					
					<ui:keyValueListItem>
						<ui:key><orbis:message code="i18n.coop_reports_coopAdmissionSeq.High" /></ui:key>
						:
						<ui:value><fmt:formatNumber value="${salaryData.hourlyHigh}" type="currency" currencySymbol="$" /> /<orbis:message code="i18n.coop_recordsEmployed.hour" /> </ui:value>
					</ui:keyValueListItem>

					<ui:keyValueListItem>
						<ui:key><orbis:message code="i18n.coop_reports_coopAdmissionSeq.Low" /></ui:key>
						:
						<ui:value><fmt:formatNumber value="${salaryData.hourlyLow}" type="currency" currencySymbol="$" /> /<orbis:message code="i18n.coop_recordsEmployed.hour6826319512835808" /></ui:value>
					</ui:keyValueListItem>
					<ui:keyValueListItem>
						<ui:key><orbis:message code="i18n.coop_recordsEmployed.Median" /></ui:key>
						:
						<ui:value><fmt:formatNumber value="${salaryData.hourlyMedian}" type="currency" currencySymbol="$" /> /<orbis:message code="i18n.coop_recordsEmployed.hour7034915314543592" /></ui:value>
					</ui:keyValueListItem>
					<ui:keyValueListItem>
						<ui:key><orbis:message code="i18n.coop_reports_coopAdmissionSeq.Average" /></ui:key>
						:
						<ui:value><fmt:formatNumber value="${salaryData.hourlyAverage}" type="currency" currencySymbol="$" /> /<orbis:message code="i18n.coop_recordsEmployed.hour1502962040693936" /></ui:value>
					</ui:keyValueListItem>
					<ui:keyValueListItem>
						<ui:key><orbis:message code="i18n.coop_reports_coopAdmissionSeq.SalariesCounted" /></ui:key>
						:
						<ui:value><fmt:formatNumber value="${salaryData.salariesCounted}" type="number" /></ui:value>
					</ui:keyValueListItem>
					<ui:keyValueListItem>
						<ui:key><orbis:message code="i18n.coop_recordsEmployed.SalariesNotCounted" /></ui:key>
						:
						<ui:value><fmt:formatNumber value="${salaryData.salariesNotCounted}" type="number" /></ui:value>
					</ui:keyValueListItem>
				</ui:keyValueList>
			</ui:panel>
		</c:if>
	</ui:gridCol>
</ui:grid>
