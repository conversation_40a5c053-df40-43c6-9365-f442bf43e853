<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>

<style>
	.nestedTable td {border:none;text-align:right;background-color:transparent !important;}
	.reportTitle {
		font-weight: bold;
		font-size: 2em;
	}
</style>

<ui:secondaryActionsGroup id="reportNavigation" aria-label="i18n.coop_coordinators.ReportNavigation" type="success" >
	<ui:dropdownItem onclick="$(window).scrollTop($('#one').offset().top -75)"><spring:message code="i18n.cc_validatorReport.ReportSummary" /></ui:dropdownItem>
	<c:if test="${not empty releasedData}">
		<ui:dropdownItem onclick="$(window).scrollTop($('#two').offset().top -75)">
			<c:set var="itsANew6"><orbis:message code="i18n.coop_coordinators.Coordinator" /></c:set>
			<c:if test="${serviceTeamEngaged}">
				<c:set var="itsANew6"><orbis:message code="i18n.coop_coordinators.ServiceTeamMember" /></c:set>
			</c:if>
			<orbis:message code="i18n.coop_coordinators.ReleasedCo6154139017794301" arguments="${itsANew6}" />
		</ui:dropdownItem>
	</c:if>
	<c:if test="${not empty coordinatorDataMap}">
		<ui:dropdownItem onclick="$(window).scrollTop($('#three').offset().top -75)">
			<c:set var="itsANew96"><orbis:message code="i18n.coop_coordinators.Coordinator" /></c:set>
			<c:if test="${serviceTeamEngaged}">
				<c:set var="itsANew96"><orbis:message code="i18n.coop_coordinators.ServiceTeamMember" /></c:set>
			</c:if>
			<orbis:message code="i18n.coop_coordinators.Employment08051016117637333" arguments="${itsANew96}" /></ui:dropdownItem>
	</c:if>
</ui:secondaryActionsGroup>
<ui:grid>
	<ui:gridCol classes="content" width="8">
		<ui:panel id="one" classes="plain">
			<ui:panelTitle><orbis:message code="i18n.cc_validatorReport.ReportSummary" /></ui:panelTitle>
			<c:set var="itsANew96"><orbis:message code="i18n.coop_coordinators.Coordinators" /></c:set>
			<c:if test="${serviceTeamEngaged}">
				<c:set var="itsANew96"><orbis:message code="i18n.coop_coordinators.ServiceTeamMembers" /></c:set>
			</c:if>
			<ui:key><orbis:message code="i18n.coop_coordinators.CoopAdmiss9586247499740974" arguments="${itsANew96}" /></ui:key>
			<ui:card type="card01" size="xxs" classes="margin--t--s">
				<ui:cardBody><orbis:message code="i18n.coop_coordinators.Employed9893281215163728" /></ui:cardBody>
				<ui:cardStat>${casEmployed}</ui:cardStat>
			</ui:card>
			<ui:card type="card01" size="xxs">
				<ui:cardBody><orbis:message code="i18n.coop_coordinators.Unemployed8427231640911015" /></ui:cardBody>
				<ui:cardStat>${casUnemployed}</ui:cardStat>
			</ui:card>
			<ui:card type="card01" size="xxs">
				<ui:cardBody><orbis:message code="i18n.cc_validatorReport.Total" /></ui:cardBody>
				<ui:cardStat>${casTotal}</ui:cardStat>
			</ui:card>
		</ui:panel>
		
		<c:if test="${not empty releasedData}">
			<ui:panel id="two" classes="margin--b--l">
				<ui:panelTitle><orbis:message code="i18n.coop_coordinators.ReleasedCo6149248516123164" /></ui:panelTitle>
				<div class="reportSubTitle"></div>
				<div id="releasedBar"></div>
				<orbis:addComponent component="highCharts" />
				<script type="text/javascript">
					$(document).ready(function() {
						$("#releasedBar").orbisChart(${releasedBar});
					});
				</script>
			</ui:panel>
		</c:if>
		
		<c:if test="${not empty coordinatorDataMap}">
			<div class="container--table">
				<table id="three" class="table is--list zebra width--100">
					<caption class="table__caption"><orbis:message code="i18n.coop_coordinators.EmploymentStatisticsAllTerms" /></caption>
					<thead class="table__header">
						<tr class="table__row--header">
							<th class="table__header"><orbis:message code="i18n.coop_coordinators.Coordinator" /></th>
							<th class="table__header"><orbis:message code="i18n.coop_coordinators.Employed" /></th>
							<th class="table__header"><orbis:message code="i18n.coop_coordinators.NotEmployed" /></th>
							<th class="table__header"><orbis:message code="i18n.coop_coordinators.Total" /></th>
						</tr>
					</thead>
					<tbody>
						<c:forEach var="coordinator" items="${coordinatorDataMap}">
							<tr class="table__row--body">
								<td class="table__value">${coordinator.key}</td>
								<td class="table__value">${coordinator.value[true]}</td>
								<td class="table__value">${coordinator.value[false]}</td>
								<td class="table__value">${coordinator.value['Total']}</td>
							</tr>
						</c:forEach>
					</tbody>
					<tfoot>
						<tr class="table__row--footer">
							<td class="table__value--footer" colspan="4"><orbis:message code="i18n.coop_coordinators.EmploymentStatisticsAllTerms" /></td>
						</tr>
					</tfoot>
				</table>
			</div>
		</c:if>
	</ui:gridCol>
</ui:grid>