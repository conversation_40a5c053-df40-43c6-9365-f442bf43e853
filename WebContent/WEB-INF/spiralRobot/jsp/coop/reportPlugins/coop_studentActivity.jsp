<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>

<style>
	.nestedTable td {border:none;text-align:right;background-color:transparent !important;}
	.reportTitle {
		font-weight: bold;
		font-size: 2em;
	}
</style>

<ui:secondaryActionsGroup id="reportNavigation" aria-label="i18n.coop_studentActivity.ReportNavi5089627388768164" type="success">
	<ui:dropdownItem onclick="$(window).scrollTop($('#one').offset().top -75)"><orbis:message code="i18n.coop_studentActivity.AverageSea9490974674196911" /></ui:dropdownItem>
</ui:secondaryActionsGroup>

<orbis:message var="reportHelpContent" code="i18n.coop_studentActivity.ReportHelpContent" />
<ui:modal id="reportHelpModal" i18n_title="i18n.coop_studentActivity.ReportHelpTitle" includeClose="false">
	${reportHelpContent}
</ui:modal>

<ui:grid>
	<ui:gridCol classes="content" width="6">		
		<ui:panel id="one" classes="plain">
			<ui:panelTitle>
				<orbis:message code="i18n.coop_studentActivity.AverageSea9313170983032527" />
				<c:if test="${not empty reportHelpContent}">
					<button type="button" style="pointer-events:initial !important;" class="btn__small btn--default plain position--relative" onclick="$('#reportHelpModal').uiShow();"><i class="material-icons">help</i></button>
				</c:if>
			</ui:panelTitle>
			
			<h4><orbis:message code="i18n.coop_studentActivity.Views" /></h4>
			<ul style="list-style-type: none; padding: 0;">
				<li>
					<ui:card type="card01" size="xxs">
						<ui:cardBody><orbis:message code="i18n.coop_studentActivity.Employed" /></ui:cardBody>
						<ui:cardStat>${averageJobViewsEmp} / <orbis:message code="i18n.common.student.lowerCase" /></ui:cardStat>
					</ui:card>
				</li>
				<li>
					<ui:card type="card01" size="xxs" >
						<ui:cardBody><orbis:message code="i18n.coop_studentActivity.Unemployed" /></ui:cardBody>
						<ui:cardStat>${averageJobViews} / <orbis:message code="i18n.common.student.lowerCase" /></ui:cardStat>
					</ui:card>
				</li>
			</ul>
			<h4><orbis:message code="i18n.coop_studentActivity.Applications" /></h4>
			<ul style="list-style-type: none; padding: 0;">
				<li>
					<ui:card type="card01" size="xxs">
						<ui:cardBody><orbis:message code="i18n.coop_studentActivity.Employed" /></ui:cardBody>
						<ui:cardStat>${averageApplicationsEmp} / <orbis:message code="i18n.common.student.lowerCase" /></ui:cardStat>
					</ui:card>
				</li>
				<li>
					<ui:card type="card01" size="xxs">
						<ui:cardBody><orbis:message code="i18n.coop_studentActivity.Unemployed" /></ui:cardBody>
						<ui:cardStat>${averageApplications} / <orbis:message code="i18n.common.student.lowerCase" /></ui:cardStat>
					</ui:card>
				</li>
			</ul>
			<h4><orbis:message code="i18n.coop_studentActivity.Interviews" /></h4>
			<ul style="list-style-type: none; padding: 0;">
				<li>
					<ui:card type="card01" size="xxs">
						<ui:cardBody><orbis:message code="i18n.coop_studentActivity.Employed" /></ui:cardBody>
						<ui:cardStat>${averageInterviewsEmp} / <orbis:message code="i18n.common.student.lowerCase" /></ui:cardStat>
					</ui:card>
				</li>
				<li>
					<ui:card type="card01" size="xxs">
						<ui:cardBody><orbis:message code="i18n.coop_studentActivity.Unemployed" /></ui:cardBody>
						<ui:cardStat>${averageInterviews} / <orbis:message code="i18n.common.student.lowerCase" /></ui:cardStat>
					</ui:card>
				</li>
			</ul>
			<h4><orbis:message code="i18n.coop_studentActivity.Logins1488304387183436" /></h4>
			<ul style="list-style-type: none; padding: 0;">
				<li>
					<ui:card type="card01" size="xxs">
						<ui:cardBody><orbis:message code="i18n.coop_studentActivity.Employed" /></ui:cardBody>
						<ui:cardStat>${averageLoginsEmp} / <orbis:message code="i18n.common.student.lowerCase" /></ui:cardStat>
					</ui:card>
				</li>
				<li>
					<ui:card type="card01" size="xxs">
						<ui:cardBody><orbis:message code="i18n.coop_studentActivity.Unemployed" /></ui:cardBody>
						<ui:cardStat>${averageLogins} / <orbis:message code="i18n.common.student.lowerCase" /></ui:cardStat>
					</ui:card>
				</li>
			</ul>
		</ui:panel>
	</ui:gridCol>
</ui:grid>