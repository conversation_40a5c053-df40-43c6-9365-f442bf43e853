<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>
<orbis:addComponent component="highCharts" />
<orbis:addComponent component="orbisNotePopups" />

<style>
	.nestedTable td {border:none;text-align:right;background-color:transparent !important;}
	.reportTitle {
		font-weight: bold;
		font-size: 2em;
	}
</style>

<orbis:message var="reportHelpContent" code="i18n.coop_recordsNotEmployed.ReportHelpContent" />
<ui:modal id="reportHelpModal" i18n_title="i18n.coop_recordsNotEmployed.ReportHelpTitle" includeClose="false">
	${reportHelpContent}
</ui:modal>

<ui:secondaryActionsGroup type="success" id="reportNavigation" aria-label="i18n.coop_recordsNotEmployed.ReportNavi9693696089989005" >
	<ui:dropdownItem onclick="$(window).scrollTop($('#one').offset().top -75)"><orbis:message code="i18n.coop_recordsNotEmployed.ReportSummary" /></ui:dropdownItem>
	<ui:dropdownItem onclick="$(window).scrollTop($('#two').offset().top -75)"><orbis:message code="i18n.coop_recordsNotEmployed.StudentInt8485018776084164" /></ui:dropdownItem>
	<ui:dropdownItem onclick="$(window).scrollTop($('#three').offset().top -75)"><orbis:message code="i18n.coop_recordsNotEmployed.Unemployed3489727914991664" /></ui:dropdownItem>
</ui:secondaryActionsGroup>
<ui:grid>
	<ui:gridCol classes="content" width="6">
		<c:if test="${not empty reportHelpContent}">
			<button type="button" style="pointer-events:initial !important;" class="btn__small btn--default plain position--relative" onclick="$('#reportHelpModal').uiShow();"><i class="material-icons">help</i></button>
		</c:if>
		<table class="table width--100 is--list">
			<caption class="table__caption"><orbis:message code="i18n.coop_recordsNotEmployed.ReportSummary" /></caption>
			<thead class="table__header">
				<tr class="table__row--header">
					<td class="table__heading"></td>
					<th class="table__heading"><orbis:message code="i18n.coop_recordsNotEmployed.Yes" /></th>
					<th class="table__heading"><orbis:message code="i18n.coop_recordsNotEmployed.No" /></th>
					<th class="table__heading">%</th>
				</tr>
			</thead>
			<tbody>
				<tr class="table__row--body">
					<td class="table__value"><orbis:message code="i18n.coop_recordsNotEmployed.TotalUnemployed" /></td>
					<td class="table__value">${totalUnemployed}</td>
					<td class="table__value"></td>
					<td class="table__value"></td>
				</tr>
				<c:if test="${not empty TACData}">
					<tr class="table__row--body">
						<td class="table__value"><orbis:message code="i18n.coop_recordsNotEmployed.AcceptedTe8306842636643004" /></td>
						<td class="table__value">${TACData['Accepted']}</td>
						<td class="table__value">${TACData['Not Accepted']}</td>
						<td class="table__value">${tacAcceptedPercent}%</td>
					</tr>
				</c:if>
				<c:if test="${not empty ReleaseData}">
					<tr class="table__row--body">
						<td class="table__value"><orbis:message code="i18n.coop_recordsNotEmployed.ReleasedToEmployers" /></td>
						<td class="table__value">${ReleaseData['Released']}</td>
						<td class="table__value">${ReleaseData['Not Released']}</td>
						<td class="table__value">${releasedPercent}%</td>
					</tr>
				</c:if>
				<c:if test="${not empty SIPie}">
					<tr class="table__row--body">
						<td class="table__value"><orbis:message code="i18n.coop_recordsNotEmployed.StudentInt2594450678624166" /></td>
						<td class="table__value">${submittedIntentionCount}</td>
						<td class="table__value">${notSubmittedIntentionCount}</td>
						<td class="table__value">${submittedIntentionPercent}%</td>
					</tr>
				</c:if>
			</tbody>
		</table>
		
		<ui:panel id="two" classes="margin--t--m">
			<ui:panelTitle>
				<orbis:message code="i18n.coop_recordsNotEmployed.StudentInt6572521593557166" />
			</ui:panelTitle>
			<c:if test="${not empty SIPie}">
				<div class="reportSubTitle"></div>
				<div id="SIPie"></div>
				<script type="text/javascript">
					$(document).ready(function() {
						$("#SIPie").orbisChart(${SIPie});
					});
				</script>
			</c:if>
			<c:if test="${empty SIPie}">
				<orbis:message code="i18n.coop_recordsNotEmployed.Theconfigu1871524102216818" />
			</c:if>
		</ui:panel>
		
		<ui:panel id="three" classes="margin--t--m">
			<ui:panelTitle>
				<orbis:message code="i18n.coop_recordsNotEmployed.Unemployed3489727914991664" />
			</ui:panelTitle>
			<div class="reportSubTitle"></div>
			<div id="unemployedByProgramChart"></div>
			<script type="text/javascript">
				$(document).ready(function() {
					$("#unemployedByProgramChart").orbisChart(${unemployedByProgramChart});
				});
			</script>
		</ui:panel>
	</ui:gridCol>
</ui:grid>