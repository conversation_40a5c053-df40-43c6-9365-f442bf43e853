<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>
<orbis:addComponent component="orbisNotePopups" />


<ui:grid>
	<ui:gridCol width="4">
		<ui:dropdown id="jump-to" i18n_title="i18n.coop_jobPostings.View" >
			<ui:dropdownItem onclick="$(window).scrollTop($('#one').offset().top -75)">
				<orbis:message code="i18n.cc_validatorReport.ReportSummary" />
			</ui:dropdownItem>
			<ui:dropdownItem onclick="$(window).scrollTop($('#two').offset().top -75)">
				<orbis:message code="i18n.coop_reports_home.JobConvers2554306817950498" />
			</ui:dropdownItem>
			<c:if test="${not empty programData}">
				<ui:dropdownItem onclick="$(window).scrollTop($('#three').offset().top -75)">
					<orbis:message code="i18n.coop_jobPostings.PostingsBy06327071465346368" />
				</ui:dropdownItem>
			</c:if>
		</ui:dropdown>
	</ui:gridCol>	
</ui:grid>
<ui:grid>
	<ui:gridCol classes="content" width="8">
		<ui:panel classes="plain" id="one">
			<ui:panelTitle>
				<orbis:message code="i18n.cc_validatorReport.ReportSummary" />
			</ui:panelTitle>
			<ui:card type="card01" size="xxs">
				<ui:cardBody>
					<orbis:message code="i18n.coop_jobPostings.JobPostings" />
				</ui:cardBody>
				<ui:cardStat>
					${totalPostings}
				</ui:cardStat>
			</ui:card>
		</ui:panel>
		
		<ui:panel classes="plain" id="two">
			<ui:panelTitle>
				<orbis:message code="i18n.coop_reports_home.JobConvers2554306817950498" />
				<c:set var="jobConversionStatisticsTitle">
					<h3><orbis:message code="i18n.coop_reports_home.547057969757228" /></h3>
					<orbis:message code="i18n.coop_reports_home.521003641765949" />
				</c:set>
				<ui:helpTip i18n_text="${jobConversionStatisticsTitle}"/>
			</ui:panelTitle>
			<ui:card type="card01" size="xxs">
				<ui:cardHeader>
					<orbis:message code="i18n.coop_reports_home.****************" />
				</ui:cardHeader>
				<ui:cardBody>
					<c:if test="${not empty jobToApplicationRatio}">
						<orbis:message code="i18n.coop_jobPostings.jobToAppli6651699329444417" arguments="${jobToApplicationRatio['appliedJobPostings']}, ${totalPostings}" />	
					</c:if>
				</ui:cardBody>
				<ui:cardStat>
					<c:choose>
						<c:when test="${empty jobToApplicationRatio}"><orbis:message code="i18n.coop_jobPostings.NA" /></c:when>
						<c:otherwise>${jobToApplicationRatio['ratio']}%</c:otherwise>
					</c:choose>
				</ui:cardStat>
			</ui:card>
			<ui:card type="card01" size="xxs">
				<ui:cardHeader>	
					<orbis:message code="i18n.coop_reports_home.****************" />
				</ui:cardHeader>
				<ui:cardBody>
					<c:if test="${not empty jobToInterviewRatio}">
						<orbis:message code="i18n.coop_jobPostings.jobToInter08564006520105383" arguments="${jobToInterviewRatio['interviewedJobPostings']}, ${totalPostings}" />					
					</c:if>
				</ui:cardBody>
				<ui:cardStat>
					<c:choose>
						<c:when test="${empty jobToInterviewRatio}"><orbis:message code="i18n.coop_jobPostings.NA" /></c:when>
						<c:otherwise>${jobToInterviewRatio['ratio']}%</c:otherwise>
					</c:choose>
				</ui:cardStat>
			</ui:card>
			<ui:card type="card01" size="xxs">
				<ui:cardHeader>	
					<orbis:message code="i18n.coop_reports_home.22302574238064832" />
				</ui:cardHeader>
				<ui:cardBody>
					<c:if test="${not empty jobToPlacementRatio}">
						<orbis:message code="i18n.coop_jobPostings.jobToPlace841854993568331" arguments="${jobToPlacementRatio['placedJobPostings']}, ${totalPostings}" />					
					</c:if>
				</ui:cardBody>
				<ui:cardStat>
					<c:choose>
						<c:when test="${empty jobToPlacementRatio}"><orbis:message code="i18n.coop_jobPostings.NA" /></c:when>
						<c:otherwise>${jobToPlacementRatio['ratio']}%</c:otherwise>
					</c:choose>
				</ui:cardStat>
			</ui:card>
		</ui:panel>
		
		<c:if test="${not empty programData}">
			<ui:panel id="three">
				<ui:panelTitle><orbis:message code="i18n.coop_jobPostings.PostingsBy3241725758468168" /></ui:panelTitle>
				<div id="programBar" style="height: 600px;"></div>
				<orbis:addComponent component="highCharts" />
				<script type="text/javascript">
					$(document).ready(function() {
						$("#programBar").orbisChart(${programBar});
					});
				</script>
			</ui:panel>
		</c:if>
	</ui:gridCol>
</ui:grid>