<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<%-- <ui:isConverted type="complete">ui converted, orbisAppSr.setUpRegularDialog (ln 141) needs to be converted</ui:isConverted>   --%>

<c:set var="editButtonLabel">
	<orbis:message code="i18n.grid_columnManager.Edit" />
</c:set>
<c:set var="changeButtonLabel">
	<orbis:message code="i18n.grid_columnManager.Change" />
</c:set>

<script type="text/javascript">
	var gridID = '${gridID}';
	var contentItemId = '${contentItemId}';
	var gridPermissions = JSON.parse('${gridPermissionsJSON}');
	var controllerPath = "${siteElement.fullPath}.htm";
	
	function uuid() {
		return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
				var r = Math.random()*16|0, v = c == 'x' ? r : (r&0x3|0x8);
				return v.toString(16);
		});
	}

	function shift(categoryId, direction) {
		var gridCategory = $('div.gridCategory').filter(function() {
			return $(this).data('categoryId') === categoryId;
		});

		var next = gridCategory.next();
		var prev = gridCategory.prev();

		if (direction === 'up' && prev.length === 1) {
			gridCategory.insertBefore(prev);
		} else if (direction === 'down' && next.length === 1) {
			gridCategory.insertAfter(next);
		}
	}
	
	function getFormattedGridContent(content)
	{
		var gridContent = content;
		
		var boxColour = $(".gridContentColourPickerSelected").val();
		
		if(!orbisAppSr.isEmpty(gridContent))
		{
			var startIndex = 0;
			
			var toCheckFor = ['<div class="alert alert-info">', '<div class="alert alert-warning">', '<div class="alert alert-danger">'];
			
			for(i = 0; i < toCheckFor.length; i++)
			{
				var foundIndex = gridContent.indexOf(toCheckFor[i]);
				if(foundIndex == 0)
				{
					startIndex = foundIndex + toCheckFor[i].length;
					break;
				}
			}
			
			if(startIndex != 0)
			{
				gridContent = gridContent.substring(startIndex, gridContent.lastIndexOf("</div>"));
			}
		}
		
		if(!orbisAppSr.isEmpty(boxColour) && !orbisAppSr.isEmpty(gridContent))
		{
			var temp = '<div class="alert alert-' + boxColour + '">\n';
			temp += gridContent;
			temp += '\n</div>';
			gridContent = temp;
		}
		
		return gridContent;
	}

	function getGridDefinition() {
		var gridContent = getFormattedGridContent($("#gridContent").val());
		var gridL2Content = getFormattedGridContent($("#gridL2Content").val());
		
		var toReturn = {
			gridID : gridID,
			contentItemId : contentItemId,
			categories : [],
			columns : [],
			permissions : {},
			content : gridContent,
			l2Content : gridL2Content,
			lockHeaders : $("#lockHeaders:checked").length > 0,
			wrapHeaders : $("#wrapHeaders:checked").length > 0,
			lockedColumns : $("#lockedColumns").val()
		};
		
		var categoryOrdinal = 1;
		var columnOrdinal = 1;
		
		$('.gridCategory').each(function() {
			var cat = {
				categoryID: $(this).data('categoryId'),
			categoryName: $('input[name=categoryName]', this).val(),
			l2CategoryName: $('input[name=l2CategoryName]', this).val(),
			ordinal: categoryOrdinal
			};

			$('.gridColumn', this).each(function() {
				var col = {
					colID: $(this).data('colId'),
					columnName: $('input[name=columnName]', this).val(),
					l2ColumnName: $('input[name=l2ColumnName]', this).val(),
					parallelized: $('input[name=parallelized][type=checkbox]:checked', this).val() === 'true',
					visible: $('input[name=visible][type=checkbox]:checked', this).val() === 'true',
					gridColumnWidth: $('input[name=gridColumnWidth][type=text]', this).val(),
					sortOrdinal: Number.parseInt($('select[name=sortOrdinal]', this).val(), 10),
					sortDirection: Number.parseInt($('select[name=sortDirection]', this).val(), 10),
					category: cat.categoryID,
					ordinal: columnOrdinal
				};

				toReturn.columns.push(col);
				columnOrdinal += 1;
			});

			toReturn.categories.push(cat);
			categoryOrdinal += 1;
		});
		
		toReturn.permissions = gridPermissions;

		return toReturn;
	}

	$(document).ready(function() {
		
		$(".visibilityRestrictions").click(function(){loadAndOpenRestrictionDialog.apply(this, [openVisibilityRestrictionDialog])});
	
		$(document).on('click', '.addCategoryBtn', function() {
			var categoryName = $('.addCategory[name="categoryName"]').val();
			var l2CategoryName = $('.addCategory[name="l2CategoryName"]').val();

			var newGridCategory = $('div.gridCategory').first().clone();

			newGridCategory.data('categoryId', uuid());
			$('input[name="categoryName"]', newGridCategory).val(categoryName);
			$('input[name="l2CategoryName"]', newGridCategory).val(l2CategoryName);

			$('tr.gridColumn', newGridCategory).remove();

			$('.sortColumnsTable', newGridCategory).sortable({
				handle : '.moveGrip',
				connectWith : '.sortColumnsTable'
			});

			newGridCategory.appendTo('.mainContent');
		});

		$(document).on('click', '.changeCategoryOrder.down', function() {
			var categoryId = $(this).closest('.gridCategory').data('categoryId');
			shift(categoryId, 'down');
		});

		$(document).on('click', '.changeCategoryOrder.up', function() {
			var categoryId = $(this).closest('.gridCategory').data('categoryId');
			shift(categoryId, 'up');
		});

		$('.sortColumnsTable').sortable({
			handle : '.moveGrip',
			connectWith : '.sortColumnsTable'
		});

		$(document).on('click', '.saveColumnManager', function() {
			var gridDefinition = getGridDefinition();
			orbisAppSr.buildForm({
				action : '<o:encrypt action="saveColumnManager" />',
				gridID : gridID,
				contentItemId : contentItemId,
				gridDefinition : JSON.stringify(gridDefinition)
			}).submit();
		});

		$(document).on('click', '.resetColumnManager', function() {
			orbisAppSr.buildForm({
				action : '<o:encrypt action="resetColumnManager" />',
				gridID : gridID,
				contentItemId : contentItemId
			}).submit();
		});

		$(document).on('change', 'select.sortDirection', function() {
			var $this = $(this);
			var row = $this.closest('tr.gridColumn');
			$('input[name=sortOrdinal]', row).attr('disabled', $this.val() === '-1');
		});
		
		
		//Permissions
		$("#gridPersonGroup").click(function(){
			var pg = $(this).val();
			$(".permDetail").each(function(){
				var permDetail = $(this);
				permDetail.prop("checked",gridPermissions[pg][permDetail.attr("id")]);
			});
		});
		
		$(".permDetail").click(function(){
			var el = $(this);
			var permDetail = el.attr("id");
			var gridPersonGroup = $("#gridPersonGroup option:selected");
			var pg = gridPersonGroup.val();
			gridPermissions[pg][permDetail] = el.prop("checked");
		});
		
		$("#personGroup").on("click", function(){
			var selected = $(this).find("option:selected");
			var pg = selected.val();
			gridPermissions[pg] = {};
			gridPermissions[pg]["CAN_EXPORT"			] =	false; 
			gridPermissions[pg]["CAN_FILTER"			] =	false; 
			gridPermissions[pg]["CAN_SAVE_FILTERS"		] =	false; 
			gridPermissions[pg]["CAN_APPLY_DEFAULT_FILTER"] =	false;
			
			$("#gridPersonGroup option").removeAttr("selected");
			$("#gridPersonGroup ").append(selected.clone());
			$("#gridPersonGroup option").last().prop("selected",true);
			$("#gridPersonGroup").trigger("click");
			selected.remove();
			
		});	
		
		$(document).on("click", "input.currentRestrictions", function(){
			var pg = $(this).data("pg").replace(/[^a-zA-Z\d]/g, "");
			var ug = $(this).data("ug").replace(/[^a-zA-Z\d]/g, "");
			$("#" + pg + ug).prop("checked", $(this).prop("checked"));
		});
		
		$(document).on("click", "button.gridContentColourPicker", function(){
			$("button.gridContentColourPicker").removeClass("active gridContentColourPickerSelected");
			$("button.gridContentColourPicker").html("");
			$(this).addClass("active gridContentColourPickerSelected");
			$(this).html("&#10003;");
		});
		
		$("#gridContentLabel").tooltip();
		$("#lockedColumnsLabel").tooltip();
		$("#lockHeadersLabel").tooltip();
	});
	
	function saveColumnVisibilityRestrictions()
	{
		var request = $("form#visRestrictions").serializeFormToObject();
		var totalSelected = $("input.visRestrictionBox:checked").length;
		
		$("span#restrictionsCount" + request.columnId).text(totalSelected);
		$("#visibilityRestrictionsDialog").uiHide();
		
		$.post(controllerPath, request, function(data, status, xhr) {
			if (orbisAppSr.checkAjaxResponse(xhr)) {
				orbisAppSr.showNotification("<orbis:message javaScriptEscape="true"  code="i18n.grid_columnManager.Restrictio6284348092448385" />", "success");
			}
		}, "json");
	}
	
	function loadAndOpenRestrictionDialog(callback)
	{
		if($("#restrictionDialogPlaceholder").data("loaded") == false)
		{
			var request = {
				action : '<o:encrypt action="ajaxGridVisibilityRestrictions" />',
				rand : Math.floor(Math.random() * 100000)
			};
			var el = this;
			$("div#restrictionDialogPlaceholder").load(controllerPath, request, function(responseText, textStatus, xhr) {
				if (orbisAppSr.checkAjaxResponse(xhr)) {
					callback.apply(el, []);
				}
			});
			$("#restrictionDialogPlaceholder").data("loaded", "true");
			$("#visibilityRestrictionsDialog").uiShow();
		}
		else
		{
			callback.apply(this, []);
		}
	}
	
	function openVisibilityRestrictionDialog()
	{
		var checkboxGroupTitle = $(this).parents("tr:first").find("input#columnName").val();
		
		console.log('Test');
		var request = {
			action : '<o:encrypt action="loadColumnPermissions" />',
			columnId : $(this).parents("tr:first").data('columnId'),
			gridId : gridID,
			rand : Math.floor(Math.random() * 100000)
		};
		
		$(".visRestrictionBox").prop("checked", false);

		$("div#currentRestrictionsTab").load(controllerPath, request, function(responseText, textStatus, xhr) {
			if (orbisAppSr.checkAjaxResponse(xhr)) {
				console.log($(this));
				$("#restrictionsCheckboxGroup > h4").html("'" + checkboxGroupTitle + "'  column restrictions");
			}
		});
	}
	
	function numberPrimaryGroupTabs()
	{
		$("a.tabAnchor").each(function(){
			var permissionsChecked = $($(this).attr("href")).find(".checkboxGroupBody :checked").length;
			$(this).find("span.groupCount").html(permissionsChecked);
		});
	}
</script>

<ui:navBack>
	<ui:navBackItem action="displayGridColumnConfiguration"><orbis:message code="i18n.grid_columnManager.Back" /></ui:navBackItem>
</ui:navBack>
<ui:header i18n_subtitle="${not empty grid.i18nCode ? grid.i18nCode : grid.gridID }">
	<orbis:message code="i18n.grid_columnManager.GridColumnManager" />
</ui:header>

<c:if test="${not empty successMessage}">
	<ui:notification type="success">
		${successMessage}
	</ui:notification>
</c:if>

<c:if test="${not empty errorMessage}">
	<ui:notification type="error">
		${errorMessage}
	</ui:notification>
</c:if>

<div class="margin--b--m">
	<ui:button classes="saveColumnManager" type="success" size="large"><orbis:message code="i18n.grid_columnManager.Save" /></ui:button>
	<ui:button classes="resetColumnManager" type="info" size="large"><orbis:message code="i18n.grid_columnManager.Reset" /></ui:button>
</div>

<%@ page import="java.util.List" %>
<%@ page import="java.util.Map" %>
<%
int columnCount = 0;
{
	Map tmp = (Map) request.getAttribute("gridColumnsMapped");

	if (tmp != null) {
		for (Object o : tmp.values()) {
			List l = (List) o;
			columnCount += l.size();
		}
	}
}
%>

<ui:panel classes="margin--b--m">
	<div class="margin--b--xxxl">
		<ui:checkbox id="lockHeaders" name="lockHeaders" checked="${grid.lockHeaders}">
			<orbis:message code = "i18n.grid_columnManager.LockHeaders" />
			<ui:helpTip i18n_text="i18n.grid_columnManager.LockHeadersTooltip"/>
		</ui:checkbox>
	</div>
	
	<ui:textbox type="number" id="lockedColumns" min="0" max="10" name="lockedColumns" i18n_helpText="i18n.grid_columnManager.LockedColumnsTooltip" value="${grid.lockedColumns}" css="margin-bottom:16px !important;">
		<orbis:message code = "i18n.grid_columnManager.LockedColumns" />
	</ui:textbox>

	<ui:checkbox id="wrapHeaders" name="wrapHeaders" checked="${grid.wrapHeaders}">
		<orbis:message code = "i18n.grid_columnManager.WrapHeaders" />
	</ui:checkbox>
</ui:panel>

<div class="panel mainContent">
	<c:forEach var="entry" items="${gridColumnsMapped}">
		<c:set var="gridCategory" value="${entry.key}" />


		<div class="gridCategory" data-category-id="${gridCategory.categoryID}">
			<fieldset>
				<c:if test="${siteInMultiLingualMode}">
					<legend class="label margin--b--l">Category Name</legend>
				</c:if>
				
				<div class="display--flex align--top margin--b--m">
					<ui:textbox name="categoryName" value="${gridCategory.categoryName}" css="margin-bottom:0px !important;" classes="margin--r--m">${siteInMultiLingualMode ? 'en' : 'Category Name'}</ui:textbox>
					<c:if test="${siteInMultiLingualMode}">
						<div class="margin--r--m">
							|
						</div>
						<ui:textbox name="l2CategoryName" css="margin-bottom:0px !important;" value="${gridCategory.l2CategoryName}">fr</ui:textbox>
					</c:if>
				</div>
			</fieldset>

			<div class="margin--b--s"> 
				<ui:button classes="changeCategoryOrder up" type="info"><orbis:message code="i18n.grid_columnManager.Up" /></ui:button>
				<ui:button classes="changeCategoryOrder down" type="info"><orbis:message code="i18n.grid_columnManager.Down" /></ui:button>
			</div>			
			<div class="container__table">
				<table class="table zebra width--100">
					<caption class="table__caption">
						<orbis:message code="i18n.grid_columnManager.GridColumn7702585659317923" />			
					</caption>
					<thead class="table__header">			 	
						<tr class="table__row--header">
							<th class="table__heading"></th>
							<th class="table__heading">
								<orbis:message code="i18n.grid_columnManager.ColumnName.${orbisDefaultLocale}" />
								<c:if test="${siteInMultiLingualMode }">
									|
									<orbis:message code="i18n.grid_columnManager.ColumnName.${orbisSecondaryLocale}" />
								</c:if>
							</th>
							<th class="table__heading">
								<orbis:message code="i18n.grid_columnManager.Parallelized" />
								<ui:helpTip i18n_text="i18n.grid_columnManager.virtualColumnsOnly" />
							</th>
							<th class="table__heading"><orbis:message code="i18n.grid_columnManager.Visible" /></th>
							<th class="table__heading"><orbis:message code="i18n.grid_columnManager.Visibility1229754612134597" /></th>
							<th class="table__heading"><orbis:message code="i18n.grid_columnManager.Width" /></th>
							<th class="table__heading"><orbis:message code="i18n.grid_columnManager.SortOrdinalDefault" /></th>
							<th class="table__heading"><orbis:message code="i18n.grid_columnManager.SortDirectionDefault" /></th>
						</tr>
					</thead>
					<tbody class="sortColumnsTable gridColumns" data-category-id="${gridCategory.categoryID}">
						<c:forEach var="gridColumn" items="${entry.value}">
							<tr class="table__row--body gridColumn" data-col-id="${gridColumn.colID}" data-column-id="${gridColumn.id}">
								<td class="table__value"><i class="material-icons moveGrip" style="cursor:move;">drag_indicator</i></td>
								<td class="table__value display--flex">
									<ui:textbox classes="margin--r--s" value="${gridColumn.columnName}" css="margin-bottom:0px !important;" name="columnName" id="columnName"></ui:textbox>
									<c:if test="${siteInMultiLingualMode }">
										<div classes="margin--r--s">|</div>
										<ui:textbox value="${gridColumn.l2ColumnName}" css="margin-bottom:0px !important;" name="l2ColumnName"></ui:textbox>
									</c:if>
								</td>
								<td class="table__value">
									<c:if test="${gridColumn.virtual}">
										<ui:checkbox hideLabel="true" name="parallelized" value="true" checked="${gridColumn.parallelized}"><orbis:message code="i18n.grid_columnManager.Parallelized" /></ui:checkbox>
									</c:if>
								</td>
								<td class="table__value">
									<ui:checkbox hideLabel="true" name="visible" value="true" checked="${gridColumn.visible}"><orbis:message code="i18n.grid_columnManager.Visible" /></ui:checkbox>
								</td>
								<td class="table__value">
									<ui:button classes="visibilityRestrictions" type="info">
										<span id="restrictionsCount${gridColumn.id}">${columnPermissionCounts[gridColumn.id]}</span> <orbis:message code="i18n.grid_columnManager.Restrictions" />
									</ui:button>
								</td>
								<td class="table__value">
									<ui:textbox name="gridColumnWidth" css="margin-bottom:0px !important;" value="${gridColumn.gridColumnWidth}"></ui:textbox>
								</td>
								<td class="table__value">
									<label class="label in--table">
										<orbis:message code="i18n.grid_columnManager.SortOrdinalDefault" />
									</label>
									<div class="select">
										<select name="sortOrdinal" class="sortOrdinal js--ui-select">
											<option value="-1" ${gridColumn.sortOrdinal == -1 ? "selected" : "" }><orbis:message code="i18n.grid_columnManager.Disabled" /></option>
											<% for (int i = 0; i < columnCount; i++) {
												pageContext.setAttribute("sortOrdinalOptionIndex", i + 1);
											%>
											<option value="${sortOrdinalOptionIndex}" ${gridColumn.sortOrdinal == sortOrdinalOptionIndex ? "selected" : "" }>${sortOrdinalOptionIndex}</option>
											<% } %>
										</select>
										<div class="select__arrow"></div>
									</div>
								</td>
								<td class="table__value">
									<ui:select name="sortDirection" class="sortDirection" i18n_title="i18n.grid_columnManager.SortDirectionDefault" inTable="true">
										<ui:selectItem value="1" selected="${gridColumn.sortDirection == 1}"><orbis:message code="i18n.grid_columnManager.Ascending" /></ui:selectItem>
										<ui:selectItem value="2" selected="${gridColumn.sortDirection == 2}"><orbis:message code="i18n.grid_columnManager.Descending" /></ui:selectItem>
									</ui:select>
								</td>
							</tr>
						</c:forEach>
					</tbody>
					<tfoot>
					    <tr class="table__row--footer">
					        <td colspan="7" class="table__value--footer"><orbis:message code="i18n.grid_columnManager.GridColumn7702585659317923" /></td>
					    </tr>
					</tfoot>
				</table>
			</div>
		</div>
	</c:forEach>
</div>
		
<ui:panel classes="margin--t--m margin--b--m">
	<fieldset>
		<c:if test="${siteInMultiLingualMode}">
			<legend class="label margin--b--l"><orbis:message code="i18n.grid_columnManager.AddCategory" /></legend>
		</c:if>
		<ui:textbox name="categoryName" classes="addCategory">${siteInMultiLingualMode ? 'en' : 'Category Name'}</ui:textbox>
		<c:if test="${siteInMultiLingualMode}">
			<ui:textbox name="l2CategoryName" classes="addCategory" css="margin-bottom:16px !important;">fr</ui:textbox>
		</c:if>
	</fieldset>
	<ui:button type="info" classes="addCategoryBtn"><orbis:message code="i18n.grid_columnManager.Add" /></ui:button>
</ui:panel>

<ui:panel classes="margin--b--m">
	<ui:panelTitle><orbis:message code="i18n.grid_columnManager.Permissions" /></ui:panelTitle>
	<ui:grid>
		<ui:gridCol width="4">
			<ui:select name="personGroup" size="8" id="personGroup" i18n_title="i18n.grid_columnManager.strongAvai8549477331590638">
				<c:forEach var="pg" items="${personGroupMap }">
					<ui:selectItem value="${pg.key}">${pg.key}</ui:selectItem>
				</c:forEach>
			</ui:select>
	 	</ui:gridCol>
	 	
	 	<ui:gridCol width="4">
			<ui:select name="gridPersonGroup" id="gridPersonGroup" i18n_title="i18n.grid_columnManager.strongConf4967268415218091" size="8" >
				<c:forEach var="pg" items="${gridPermissions }">
					<ui:selectItem value="${pg.key}" >${pg.key}</ui:selectItem> 
				</c:forEach>
			</ui:select>
	 	</ui:gridCol>
	 	
	 	<ui:gridCol width="4">
			<ui:checkboxGroup i18n_title="i18n.grid_columnManager.strongPerm38538766187555773" name="permissionDetails">
				<c:forEach var="permissions" items="${permissionsMap}">
					<ui:checkboxGroupItem classes="permDetail" name="permissionDetails" value="${permissions.value}" id="${permissions.key}">
						<c:set var="i18nPermDetail">
							i18n.grid_columnManager.PermDetail.${permissions.key}
						</c:set>
						<orbis:message code="${i18nPermDetail}" />								
					</ui:checkboxGroupItem>
				</c:forEach>
			</ui:checkboxGroup>
		</ui:gridCol>
	</ui:grid>
</ui:panel>
		
<ui:panel classes="margin--b--m">
	<ui:panelTitle><orbis:message code="i18n.grid_columnManager.Content" /><ui:helpTip i18n_text="i18n.grid_columnManager.WhatIsContent"/></ui:panelTitle>			
	<div>
		<ui:textarea type="richBasic" name="gridContent" i18n_title="i18n.grid_columnManager.English">${grid.content}</ui:textarea>
		<ui:textarea type="richBasic" name="gridL2Content" i18n_title="i18n.grid_columnManager.Multilingual" classes="${fn:length(orbisSupportedLanguages) > 1 ? '' : 'display--none'}">${grid.l2Content}</ui:textarea>
	</div>
	<div class="label">
		<orbis:message code = "i18n.grid_columnManager.Colour" />
	</div>
	<button type="button" value="info" style="width:50px; height:50px;" class="btn btn-info color--bg--info gridContentColourPicker ${empty grid.content || not empty grid.content && fn:contains(grid.content, 'alert alert-info') ? 'active gridContentColourPickerSelected' : ''}">
		${empty grid.content || not empty grid.content && fn:startsWith(grid.content, '<div class="alert alert-info">') ? '&#10003;' : ''}
	</button>
	<button type="button" value="danger" style="width:50px; height:50px;" class="btn btn-danger color--bg--error gridContentColourPicker ${not empty grid.content && fn:contains(grid.content, 'alert alert-danger') ? 'active gridContentColourPickerSelected' : ''}">
		${not empty grid.content && fn:startsWith(grid.content, '<div class="alert alert-danger">') ? '&#10003;' : ''}
	</button>
	<button type="button" value="warning" style="width:50px; height:50px;" class="btn btn-warning color--bg--warning gridContentColourPicker ${not empty grid.content && fn:contains(grid.content, 'alert alert-warning') ? 'active gridContentColourPickerSelected' : ''}">
		${not empty grid.content && fn:startsWith(grid.content, '<div class="alert alert-warning">') ? '&#10003;' : ''}
	</button>
	<button type="button" style="width:50px; height:50px;" class="btn btn-default color--bg--default color--font--white gridContentColourPicker ${not empty grid.content && fn:contains(grid.content, 'alert alert-default') ? 'active gridContentColourPickerSelected' : ''}">
		${not empty grid.content && !fn:startsWith(grid.content, '<div class="alert alert-') ? '&#10003;' : ''}
	</button>
</ui:panel>

<ui:modal i18n_title="Visibility Restrictions" id="visibilityRestrictionsDialog" classes="hide" >
	<div id="restrictionDialogPlaceholder" data-loaded="false">
	</div>
	<ui:button type="success" onclick="saveColumnVisibilityRestrictions()"><orbis:message code="i18n.grid_columnManager.SetRestrictions" /></ui:button>
</ui:modal>

<div class="margin--b--m">
	<ui:button classes="saveColumnManager" type="success" size="large"><orbis:message code="i18n.grid_columnManager.Save" /></ui:button>
	<ui:button classes="resetColumnManager" type="info" size="large"><orbis:message code="i18n.grid_columnManager.Reset" /></ui:button>
</div>