<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>

<%@ include file="/WEB-INF/spiralRobot/jsp/grid/grid_paging.jsp"%>

<ui:form id="editQuestionsByAdminForm" action="displayEditQuestions">
	<input type="hidden" name="guId" id="guIdByAdmin" value="">
</ui:form>

<table class="table hover">
	<thead class="table__header">
		<tr class="tablesorter-headerRow table__row--header">
			<orbis:gridTh property="gu.user.firstName"><orbis:message code="i18n.grad_gradYearGradUsersAjax.FirstName" /></orbis:gridTh>
			<orbis:gridTh property="gu.user.lastName"><orbis:message code="i18n.grad_gradYearGradUsersAjax.LastName" /></orbis:gridTh>
			<orbis:gridTh property="gu.user.username"><orbis:message code="i18n.grad_gradYearGradUsersAjax.UserName" /></orbis:gridTh>
			<orbis:gridTh property="gu.user.emailAddress"><orbis:message code="i18n.grad_gradYearGradUsersAjax.Email" /></orbis:gridTh>
			<orbis:gridTh property="gu.status"><orbis:message code="i18n.grad_gradYearGradUsersAjax.Status" /></orbis:gridTh>
			<orbis:gridTh property="gu.dateCreated"><orbis:message code="i18n.grad_gradYearGradUsersAjax.DateCreated" /></orbis:gridTh>
			<orbis:gridTh property="gu.dateUpdated"><orbis:message code="i18n.grad_gradYearGradUsersAjax.DateUpdated" /></orbis:gridTh>
			<th class="table__heading"><orbis:message code="i18n.grad_gradYearGradUsersAjax.Actions" /></th>
		</tr>
	</thead>
	<tbody>
		<c:if test="${numberOfRecords > 0}">
			<c:forEach var="g" items="${gridData}">
				<tr class="table__row--body">
					<td class="table__value">${g[1]}</td>
					<td class="table__value">${not empty g[2] ? g[2] : g[7]}</td>
					<td class="table__value">${not empty g[3] ? g[3] : g[8]}</td>
					<td class="table__value">${g[4]}</td>
					<td class="table__value"><orbis:message code="i18n.GradUser.status.${g[5]}"/></td>
					<td class="table__value"><orbis:formatDate value="${g[9]}" pattern="${orbisDateShort}" /></td>
		   			<td class="table__value"><orbis:formatDate value="${g[10]}" pattern="${orbisDateShort}" /></td>
					<td class="table__value" style="text-align:center;">
						<c:if test="${userTypeKey == 'Admin'}">
			   				<a href="javascript:void(0);" class="removeUser" onclick="orbisAppSr.showConfirmModal('<spring:message code="i18n.grad_unassignedProfilesManagement.Areyousure3853946888630525" />', function(){<o:nav action="deleteGradUser" guId="${g[0]}" gyId="${g[6]}" />});">
			   					<img src="${RESOURCES_URL}/core/images/icons/icon-delete.png" title="<orbis:message code="i18n.grad_massAssignStudents.RemoveStudent" />" alt="<orbis:message code="i18n.grad_gradYearGradUsersAjax.RemoveStudent" />" />
			   				</a>
			   				<a href="javascript:void(0)" onclick="document.getElementById('guIdByAdmin').value='${g[0]}'; $('#editQuestionsByAdminForm').submit();" >
			   					<img src="${RESOURCES_URL}/core/images/icons/icon-edit.png" title="<orbis:message code="i18n.grad_massAssignStudents.EditProfile" />" alt="<orbis:message code="i18n.grad_gradYearGradUsersAjax.EditProfile" />" />
			   				</a>
		   				</c:if>
		   				<c:if test="${userTypeKey == 'Staff'}">
		   					<a href="javascript:void(0)" class="btn btn-primary btn-mini" onclick="document.getElementById('guIdByAdmin').value='${g[0]}'; $('#editQuestionsByAdminForm').submit();" >
								<orbis:message code="i18n.grad_gradYearGradUsersAjax.View" />	
							</a>
		   				</c:if>
		   			</td>
				</tr>
			</c:forEach>
		</c:if>
		<c:if test="${numberOfRecords == 0}">
			<tr class="table__row--body">
				<td class="table__value" colspan="14">
					<ui:note type="info">
						<orbis:message code="i18n.grad_gradYearGradUsersAjax.NoRecordsFound" />
					</ui:note>
				</td>
			</tr>
		</c:if>
	</tbody>
</table>
