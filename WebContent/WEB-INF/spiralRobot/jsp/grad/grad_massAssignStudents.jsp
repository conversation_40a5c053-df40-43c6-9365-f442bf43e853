<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>

<ui:navBack>
	<ui:navBackItem action="displayHome">
		<orbis:message code="i18n.common.backToHome" />
	</ui:navBackItem>
</ui:navBack>
<ui:header>
	${navbarTitle}
</ui:header>

<ui:form id="editQuestionsByAdminForm" action="displayEditQuestions">
	<input type="hidden" name="guId" id="guIdByAdmin" value="">
</ui:form>

<orbis:addComponent component="jqueryValidate" version="1.11.1" />
<script type="text/javascript">
	var controllerPath = "${siteElement.fullPath}.htm";

	
	$(document).ready(function(){
		/* orbisAppSr.setUpRegularDialog("div#erroneousUsernamesDialog", null, {width: "300px"}); */
		
		$("a.viewErroneousUsernames").click(function(){$("div#erroneousUsernamesDialog").modal("show");});
		
		$("input#lookUpStudent").autocomplete({
			source : controllerPath + "?action=<o:encrypt action="lookupStudents" />",
			minLength : 2,
			select : function(event, ui){cleanTextarea(); $("textarea#textAreaTop").val($("textarea#textAreaTop").val() + ($("textarea#textAreaTop").val() ? "\n" : "") + ui.item.id);},
			close : function(){$("input#lookUpStudent").val("");}
		});
	});
	
	function cleanTextarea()
	{
		var textAreaItems = $("textarea#textAreaTop").val().split("\n");
		var removeThese = new Array();
		for(var i = 0; i < textAreaItems.length; i++)
		{
			if(!orbisAppSr.trim(textAreaItems[i], " "))
			{
				removeThese.push(i);
			}
		}
		
		for(var i = 0; i < removeThese.length; i++)
		{
			textAreaItems.splice(removeThese[i] - i, 1);
		}
		
		$("textarea#textAreaTop").val(textAreaItems.join("\n"));
	}
</script>



<c:if test="${saved}">
	<c:if test="${numOfSuccessfull > 0}">
		<c:set var="itsANew39"><spring:message code="i18n.grad_massAssignStudents.shave" /></c:set>
		<c:if test="${numOfSuccessfull == 1}">
			<c:set var="itsANew39"> <spring:message code="i18n.grad_massAssignStudents.has" /></c:set>
		</c:if>
		<ui:notification type="success">
			<orbis:message code="i18n.grad_massAssignStudents.numOfSucce14294146299119315" arguments="${numOfSuccessfull}, ${itsANew39}" />
		</ui:notification>
	</c:if>
	<c:if test="${fn:length(nonExistantUsernames) > 0}">
		<c:set var="itsANew90">s</c:set>
		<c:if test="${fn:length(nonExistantUsernames) == 1}">
			<c:set var="itsANew90"></c:set>
		</c:if>
		<ui:notification type="error" duration="10000">
			<orbis:message code="i18n.grad_massAssignStudents.Failedtoad3582645457857069" arguments="${fn:length(nonExistantUsernames)}, ${itsANew90}" />
			<ui:button show="erroneousUsernamesDialog" size="small"><orbis:message code="i18n.common.view" /></ui:button> 
		</ui:notification>
	</c:if>
</c:if>

<orbis:validate formId="massAssignStudentsForm" />

<c:if test="${userTypeKey == 'Admin'}">
	<ui:panel>
		<ui:panelTitle>
			<orbis:message code="i18n.grad_massAssignStudents.ManageUser25137329065413805" />
		</ui:panelTitle>	
		
		<ui:form id="massAssignStudentsForm" action="massAssignStudents" gyId="${gyId}">
			<ui:textbox name="lookUpStudent" i18n_helpText="i18n.grad_massAssignStudents.Lookup"><orbis:message code="i18n.grad_massAssignStudents.AddaStudent" /></ui:textbox> 
			<ui:textarea name="usernames" i18n_title="i18n.grad_massAssignStudents.or" required="true" i18n_helpText="i18n.grad_massAssignStudents.OneperLine"></ui:textarea>
			<ui:button classes="margin--b--s" action="displayImportUsersToYear" importUsecase="GradYearImport_${gyId}" gyId="${gyId}"><orbis:message code="i18n.grad_massAssignStudents.ImportUsers" /></ui:button><br>
			<ui:button buttonType="submit" type="success"><orbis:message code="i18n.grad_massAssignStudents.Addall" /></ui:button>
		</ui:form>
	</ui:panel>
</c:if>									


<ui:panel>
   	<ui:panelTitle>
		<orbis:message code="i18n.grad_massAssignStudents.ExistingUs48195059513627303" /> 
	</ui:panelTitle>
	
		<c:if test="${empty gridID}"><c:set var="gridID" value="grad_massAssignStudents" scope="request" /></c:if>
		<jsp:include page="/WEB-INF/spiralRobot/jsp/grid/grid_initGridInstance.jsp" />
		<c:set var="gridLoadAjaxMethodName" value="ajaxLoadGradYearGradUsers" />
		<c:set var="gridAdditionalParams" value="{gyId:'${gyId}'}" />
		<c:set var="gridLoadOnDocumentReady" value="true" />
		
		<c:set var="gridDefaultOrderBy" value="user.lastName asc" />
		<c:set var="gridFilters">
			<orbis:gridThTextFilter property="gu.user.firstName" />
			<orbis:gridThTextFilter property="gu.user.lastName" />
			<orbis:gridThTextFilter property="gu.user.username" />
			<orbis:gridThTextFilter property="gu.user.emailAddress" />
			<orbis:gridThSelectFilter property="gu.status" useHtmlForDisplay="true">
				<orbis:gridOption selected="${preFilter == 0 ? 'selected' : ''}" value="0"><orbis:message code="i18n.grad_massAssignStudents.Incomplete" /></orbis:gridOption>
				<orbis:gridOption selected="${preFilter == 1 ? 'selected' : ''}" value="1"><orbis:message code="i18n.grad_massAssignStudents.Unassigned" /></orbis:gridOption>
				<orbis:gridOption selected="${preFilter == 2 ? 'selected' : ''}" value="2"><orbis:message code="i18n.grad_massAssignStudents.Complete" /></orbis:gridOption>
			</orbis:gridThSelectFilter>
			<orbis:gridThTextFilter property="gu.dateCreated" />
			<orbis:gridThTextFilter property="gu.dateUpdated" />
			<orbis:gridThNoFilter/>
		</c:set>

		<%@ include file="/WEB-INF/spiralRobot/jsp/grid/grid_placeHolder.jsp"%>
	
</ui:panel>

<ui:modal i18n_title="i18n.grad_massAssignStudents.FailedUsernames" id="erroneousUsernamesDialog">
	<ui:note i18n_title="i18n.grad_massAssignStudents.Failedtoad23745606811147568">
		<ul>
			<c:forEach var="u" items="${nonExistantUsernames}">
				<li>${u}</li>
			</c:forEach>
		</ul>	
	</ui:note>
</ui:modal>