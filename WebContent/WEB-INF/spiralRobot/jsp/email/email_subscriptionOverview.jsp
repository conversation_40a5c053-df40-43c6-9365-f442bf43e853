<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<%-- <ui:isConverted type="complete"></ui:isConverted>   --%>

<c:if test="${not empty archiveSuccess}">
	<ui:notification type="success">
		<orbis:message code="i18n.email_subscriptionOverview.SubscriptionHasBeen123456789" />
	</ui:notification>
</c:if>
<c:if test="${not empty unarchiveSuccess}">
	<ui:notification type="success">
		<orbis:message code="i18n.email_subscriptionOverview.SubscriptionHasBeen987654321" />
	</ui:notification>
</c:if>

<ui:navBack>
	<ui:navBackItem action="displayHome"><orbis:message code="i18n.email_subscriptionOverview.EmailCampa4382932064050252" /></ui:navBackItem>
	<ui:navBackItem action="displaySubscriptions"><orbis:message code="i18n.email_subscriptionOverview.Subscripti6559784276163697" /></ui:navBackItem>
</ui:navBack>
<ui:header>${isL1 ? siteElement.elementTitle : siteElement.elementTitle2}</ui:header>

<c:set var="currentTab" value="overview" />
<%@ include file="email_subscriptionTabs.jsp"%>

<ui:grid>
	<ui:gridCol width="8">
		
		<ui:panel classes="margin--b--m">
			<ui:panelTitle>Subscription Detail</ui:panelTitle>
			<h2>${subscription.name}</h2>
			<p>${subscription.description}</p>
		</ui:panel>

		<table class="table width--100 zebra">
			<caption class="table__caption"><orbis:message code="i18n.email_subscriptionOverview.Administra9057108291754584" /></caption>
			<tbody>
				<tr class="table__row--body">
					<td class="table__value"><orbis:message code="i18n.email_subscriptionOverview.Title" />:</td>
					<td class="table__value">${subscription.name}</td>
				</tr>
				<tr class="table__row--body">
					<td class="table__value"><orbis:message code="i18n.email_subscriptionOverview.DateCreated" />:</td>
					<td class="table__value"><fmt:formatDate value="${subscription.dateCreated}" pattern="${orbisDateAndTime}" /></td>
				</tr>
				<tr class="table__row--body">
					<td class="table__value"><orbis:message code="i18n.email_subscriptionOverview.CreatedBy" /></td>
					<td class="table__value">${subscription.user.firstName} ${subscription.user.lastName }</td>
				</tr>
				<tr class="table__row--body">
					<td class="table__value"><orbis:message code="i18n.email_subscriptionOverview.ListAdministrators" />:</td>
					<td class="table__value">
						<c:forEach var="admin" items="${admins}"> 
							${admin.user.firstName} ${admin.user.lastName}
						</c:forEach>
					</td>
				</tr>
			</tbody>
			<tfoot>
			    <tr class="table__row--footer">
			        <td colspan="7" class="table__value--footer"><orbis:message code="i18n.email_subscriptionOverview.Administra9057108291754584" /></td>
			    </tr>
			</tfoot>
		</table>
	</ui:gridCol>
	
	<ui:gridCol width="4">
		<ui:note type="info">
			<c:if test="${!subscription.archived}">
				<ui:button classes="margin--b--m" type="error" size="large" action="archiveSubscription" subscriptionId="${subscription.id}" i18n_confirmOnclick="i18n.email_subscriptions.ArchivingASubscrip123456789"><orbis:message code="i18n.email_subscriptionOverview.Archive" /></ui:button>
			</c:if>
			
			<c:if test="${subscription.archived}">
				<ui:button classes="margin--b--m" size="large" action="unarchiveSubscription" subscriptionId="${subscription.id}"><orbis:message code="i18n.email_subscriptionOverview.UnArchive" /></ui:button>
			</c:if>
										
			<table class="table width--100  zebra">
				<caption class="table__caption"><orbis:message code="i18n.email_subscriptionOverview.Stats" /></caption>
				<tbody>
					<tr class="table__row--body">
						<td class="table__value"><orbis:message code="i18n.email_subscriptionOverview.Subscribers" /></td>
						<td class="table__value">${subscriptionCount}</td>
					</tr>
					<tr class="table__row--body">
						<td class="table__value"><orbis:message code="i18n.email_subscriptionOverview.UnSubscribers" /></td>
						<td class="table__value">${unSubscriptionCount}</td>
					</tr>
					<tr class="table__row--body">
						<td class="table__value"><orbis:message code="i18n.email_subscriptionOverview.NeverSubscribed" /></td>
						<td class="table__value">${subscriptionsOff}</td>
					</tr>
				</tbody>
				<tfoot>
				    <tr class="table__row--footer">
				        <td colspan="7" class="table__value--footer"><orbis:message code="i18n.email_subscriptionOverview.Stats" /></td>
				    </tr>
				</tfoot>
			</table>
		</ui:note>
	</ui:gridCol>
</ui:grid>