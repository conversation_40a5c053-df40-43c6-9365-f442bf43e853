<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<%-- <ui:isConverted type="complete" />   --%>

<c:if test="${not empty subscriptionDeleted}">
	<ui:notification type="success">
		<orbis:message code="i18n.email_subscriptions.SubscriptionDeleted" />
	</ui:notification>
</c:if>

<c:if test="${not empty archiveSuccess}">
	<ui:notification type="success">
		<orbis:message code="i18n.email_subscriptionOverview.SubscriptionHasBeen123456789" />
	</ui:notification>
</c:if>
<c:if test="${not empty unarchiveSuccess}">
	<ui:notification type="success">
		<orbis:message code="i18n.email_subscriptionOverview.SubscriptionHasBeen987654321" />
	</ui:notification>
</c:if>

<ui:navBack>
	<ui:navBackItem action="displayHome"><orbis:message code ="i18n.email_subscriptions.1238071772028967" /></ui:navBackItem>
</ui:navBack>

<ui:header><orbis:message code="i18n.email_subscriptions.Subscriptions" /></ui:header>

<ui:actionsGroup i18n_title="i18n.common.actions" id="email_interactions" >
	<ui:actionsGroupItem action="displaySubscriptionEdit"><orbis:message code="i18n.email_subscriptions.AddNewSubscription" /></ui:actionsGroupItem>
	<c:if test="${empty showArchived}">
		<ui:actionsGroupItem action="displaySubscriptions" showArchived="true"><orbis:message code="i18n.email_subscriptions.ShowArchivedSubscriptions" /></ui:actionsGroupItem>
	</c:if>
	<c:if test="${not empty showArchived}">
		<ui:actionsGroupItem action="displaySubscriptions"><orbis:message code="i18n.email_subscriptions.ShowUnArchivedSubscriptions" /></ui:actionsGroupItem>
	</c:if>
</ui:actionsGroup>

<c:if test="${not empty subscriptions}">
	<div class="container--table"> 
		<table class="table width--100 is--list">
			<caption class="table__caption">
				<c:if test="${empty showArchived}">
					<orbis:message code="i18n.email_subscriptions.Subscriptions" />
				</c:if>
				<c:if test="${not empty showArchived}">
					<orbis:message code="i18n.email_subscriptions.ArchivedSubscriptions" />
				</c:if>
			</caption>
			<thead class="table__header">
				<tr class="table__row--header">
					<th class="table__heading"></th>
					<th class="table__heading"><orbis:message code="i18n.email_subscriptions.Subscription" /></th>
					<th class="table__heading"><orbis:message code="i18n.email_subscriptions.CreatedBy" /></th>
					<th class="table__heading"><orbis:message code="i18n.email_subscriptions.DateCreated" /></th>
					<th class="table__heading"><orbis:message code="i18n.email_subscriptions.AllowedGroups" /></th>
				</tr>
			</thead>
			<tbody>
				<c:forEach var="g" items="${subscriptions}">
					<tr class="table__row--body">
						<td class="table__value"></td>
						<td class="table__value">${g.name}: ${g.description}</td>
						<td class="table__value">${g.user.firstName} ${g.user.lastName}</td>
						<td class="table__value"><fmt:formatDate value="${g.dateCreated}" pattern="${orbisDateAndTime}" /></td>
						<td class="table__value">
							<c:forEach var="sug" items="${subscriptionUserGroups[g.id]}" varStatus="i">
								${sug}${i.last ? '' : ', '}
							</c:forEach>
						</td>
						
						<td class="table__value">
							<ui:button type="info" action="displaySubscriptionOverview" subscriptionId="${g.id}"><orbis:message code="i18n.email_subscriptions.View" /></ui:button>
							<c:if test="${!g.archived}">
								<ui:button type="info" i18n_confirmOnclick="i18n.email_subscriptions.ArchivingASubscrip123456789" action="archiveSubscription" subscriptionId="${g.id}" gridView="true"><orbis:message code="i18n.email_subscriptionOverview.Archive" /></ui:button>
							</c:if>							
							<c:if test="${g.archived}">
								<ui:button type="info" action="unarchiveSubscription" subscriptionId="${g.id}" gridView="true" showArchived="true"><orbis:message code="i18n.email_subscriptionOverview.UnArchive" /></ui:button>
							</c:if>
							<c:if test="${empty notDeletable[g.id]}">
								<ui:button type="error" style="plain" i18n_confirmOnclick="i18n.email_subscriptions.Deletingth5164624772936593" action="deleteSubscription" subscriptionId="${g.id}" showArchived="${showArchived}"><orbis:message code="i18n.email_subscriptions.Delete" /></ui:button>
							</c:if>
						</td>					
					</tr>
				</c:forEach>
			</tbody>
			<tfoot>
			    <tr class="table__row--footer">
			        <td colspan="7" class="table__value--footer">
			        	<c:if test="${empty showArchived}">
							<orbis:message code="i18n.email_subscriptions.Subscriptions" />
						</c:if>
						
						<c:if test="${not empty showArchived}">
							<orbis:message code="i18n.email_subscriptions.ArchivedSubscriptions" />
						</c:if>
					</td>
			    </tr>
			</tfoot>
		</table>
	</div>
</c:if>

<c:if test="${empty subscriptions}">
	<ui:note type="info">
		<orbis:message code="i18n.email_subscriptions.NoSubscriptionsFound" />
	</ui:note>
</c:if>
