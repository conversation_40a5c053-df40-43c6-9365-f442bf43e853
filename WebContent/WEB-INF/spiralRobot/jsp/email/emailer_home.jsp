<%@ include file="/WEB-INF/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>

<orbis:addComponent component="lodash" callback="intializeDebounce();" />

<c:if test="${not empty errors}">
	<c:set var="msgs" value="${errors}"/>		
	<%@ include file="/WEB-INF/spiralRobot/jsp/i18nMsgs.jsp" %>
</c:if>

<c:if test="${not empty isShortCircuitView}">
	<%@ include file="/WEB-INF/spiralRobot/jsp/linkedResources.jsp" %>
</c:if>

<form enctype="multipart/form-data" id="cancelForm" method="POST" action="${siteElement.fullPath}.htm">
	<c:if test="${empty emailModel.additionalParams['overrideAction']}">
		<o:encrypt input="true" action="displayEmailCancelled" subAction="displayEmailCancelled" />
	</c:if>
	
	<c:if test="${not empty emailModel.additionalParams['overrideAction']}">
		<o:encrypt input="true" action="${emailModel.additionalParams['overrideAction']}" subAction="displayEmailCancelled" />
	</c:if>
	
	<c:forEach var="m" items="${emailModel.additionalParams}">
		<c:choose>
			<c:when test="${m.key == 'allRecipients' }">
				<input type="hidden" class="${m.key }" name="${m.key}" value="NOTE_will_be_replaced_on_jquery_load">
			</c:when>
			<c:when test="${m.key!='emailSignature' && !fn:contains(m.key, '@')}">
				<input type="hidden" name="${m.key}" value='${m.value}'>
			</c:when>		
		</c:choose>
	</c:forEach>
</form>

<orbis:addComponent component="tagsinput" />
<script type="text/javascript">
	$(document).ready(function() {
		<c:if test="${not empty emailModel.additionalParams['additionalColumn']}">
			$("#recipients").siblings(".checkboxWidgetHeader").append("<span style='float: right;'>${emailModel.additionalParams['additionalColumn']}</span>")
		</c:if>


		$(".allRecipients").val(JSON.stringify(${emailModel.additionalParams.allRecipients}));
	});
	
	var checkFormAndSubmit;
	
	function intializeDebounce(){
	 	 checkFormAndSubmit = _.debounce(function(){
			if($("#massEmailerForm").valid()){
				orbisApp.showLoadingOverlay('<spring:message code="i18n.emailer_home.PleaseWait" javaScriptEscape="true"/>');
				$('#massEmailerForm').submit();
			}		
		}, 3000, {leading:true,trailing:false});  
		
	 	 
		checkFormAndSubmitAjax = _.debounce(function(){
			if($("#massEmailerForm").valid()){
				orbisApp.openTempMessageDialog('<spring:message code="i18n.emailer_home.PleaseWait" javaScriptEscape="true" />');
				$.post(controllerPath,
						$.extend({action: '<o:encrypt action="${emailModel.actionName}" subAction="sendEmail" />', 
							rand: Math.floor(Math.random()*100000)}, 
							$('form#massEmailerForm').serializeFormToObject()), 
							function(data, status, xhr) {
									if (data.emailSent) {
			              				<o:nav action="${emailModel.ajaxActionName}" />
									}
							}, 'json');
				}
			}, 3000, {leading:true,trailing:false}); 
	}
	
	function deleteAttachment() {
		$("#attachmentDiv").html("<INPUT type='file' name='fileupload' size='50' style='width: 500px; border-color: black;'>");
	}
	
	$(document).ready(function() {
      $('#bcc').tagsInput({
        defaultText: '<orbis:message code="i18n.emailer_home.addrecipie5851079532083109" javaScriptEscape="true" />',
        width:'auto'
      });
	});
	
	function emailProcessing() {
		orbisApp.showNotification('<orbis:message code="i18n.emailer_home.Emailproce4184460067812476" javaScriptEscape="true" />', "info")
	}
</script>


<c:set var="submitOnclick" value="${empty emailModel.ajaxActionName ? 'checkFormAndSubmit()' : 'checkFormAndSubmitAjax()'}" />
<c:if test="${'true' == emailModel.additionalParams['threadRunning'] }">
	<c:set var="submitOnclick" value="emailProcessing()" />
	<script type="text/javascript">
		emailProcessing();
	</script>
</c:if>

<ui:userProfileHeader user="${currentUser}" excludeImageSection="true" overrideTitle="true">
	<ui:section key="title">
		<c:if test = "${empty emailModel.customFormTitle}">
			<orbis:message code="i18n.emailer_home.SendEmail" /> 
		</c:if>
		
		<c:if test = "${not empty emailModel.customFormTitle}">
			${emailModel.customFormTitle}
		</c:if>
	</ui:section>			
</ui:userProfileHeader>

<c:set var="cancelOnclick" value="$('#cancelForm').submit();" />
<ui:formPage formId="massEmailerForm" cancelOnclick="${!hideCancelButton ? cancelOnclick : ''}" saveOnclick="${submitOnclick}" i18n_saveLabel="i18n.emailer_home.SendEmail">

	<c:if test="${empty emailModel.ajaxActionName}">
		<o:encrypt input="true" action="${emailModel.actionName}" subAction="sendEmail" />
	</c:if>
	
	<c:forEach var="m" items="${emailModel.additionalParams}">
		<c:choose>
			<c:when test="${m.key == 'allRecipients'}">
				<input type="hidden" class="${m.key}" name="${m.key}" value="NOTE_will_be_replaced_on_jquery_load">
			</c:when>
			<c:when test="${m.key!='emailSignature' && !fn:contains(m.key, '@')}">
				<input type="hidden" name="${m.key}" value='${m.value}'>
			</c:when>		
		</c:choose>
	</c:forEach>
				
	<ui:note>
		<orbis:message code="i18n.emailer_home.Fieldswith9468609275859281" />
	</ui:note>
	
	<ui:textbox name="from" id="emailModel_fromAddress" value="${emailModel.fromAddress}" required="true">
		<orbis:message code="i18n.emailer_home.FromAddress" />
	</ui:textbox>
	
	<ui:checkboxGroup i18n_title="i18n.emailer_home.EmailRecipients0" i18nArgs_title="${fn:length(emailModel.emailRecipients)}">
		<c:forEach items="${emailModel.emailRecipients}" var="r">
			<ui:checkboxGroupItem disabled="${!r.validEmail}" checked="${r.selected && r.validEmail}" name="${r.validEmail ? 'to' : ''}">
				${r.name}
				<c:if test="${r.validEmail}">
					(<span style="font-weight:bold;">${r.email}</span>)
				</c:if>
				<c:if test="${!r.validEmail}">
					(<span style="color:red;"><orbis:message code="i18n.emailer_home.invalidemailaddress" /></span>)
				</c:if>
				<c:if test="${not empty emailModel.additionalParams['additionalColumn']}">
					<c:set var="additionalColKey" value="${r.email}" />
					<c:if test="${not empty r.entityLink}">
						<c:set var="additionalColKey">${additionalColKey}${r.entityLink}</c:set>
					</c:if>
					<span style="font-weight:bold;">
						${emailModel.additionalParams[additionalColKey]}
					</span>
				</c:if>
			</ui:checkboxGroupItem>
		</c:forEach>
	</ui:checkboxGroup>

	<c:if test="${!hideAdditionalRecipients}">
		<c:choose>
			<c:when test="${not empty emailModel.additionalParams['taxCreditEmailer'] && fn:length(emailModel.emailRecipients)>1 && not empty emailModel.emailRecipients[0].entityLink}">
				<ui:textbox name="disabled_bcc" value="Function not available when more than one tax credit letter is being sent." maxLength="255" disabled="true">
					<orbis:message code="i18n.emailer_home.AdditionalRecipients" />
				</ui:textbox>
				<input type="hidden" name="bcc" value="Function not available when more than one tax credit letter is being sent."/>
			</c:when>
			<c:otherwise>
				<input type="text" id="bcc" name="bcc" value="${emailModel.bcc}" maxlength="255">
			</c:otherwise>
		</c:choose>
	</c:if>
	
	<fieldset>
		<legend>
			<orbis:message code="i18n.emailer_home.Subject" />
		</legend>
		
		<ui:textbox name="subject" value="${nameText}" maxlength="255">
			${langLabelL1}
			<orbis:message var="nameText" text="${emailModel.subject}" htmlEscape="true" />
		</ui:textbox>
		
		<ui:textbox name="l2Subject" classes="${isBilingualModeOn ? '' : 'display--none'}" value="${l2NameText}">
			${langLabelL2}
			<orbis:message var="l2NameText" text="${emailModel.l2Subject}" htmlEscape="true" />
		</ui:textbox>
	</fieldset>

	<c:if test="${empty emailModel.emailAttachment}">
		<ui:fileUpload uploadDirectory="/content/private/email/" name="attachment">
			<orbis:message code="i18n.emailer_home.Attachment" />
		</ui:fileUpload>
	</c:if>
	<c:if test="${not empty emailModel.emailAttachment}">
		<ui:note i18n_title="Current Attachment">
			${emailModel.emailAttachment.name} (${emailModel.emailAttachment.niceFileSize})
			<ui:button onclick="deleteAttachment();" aria-label="i18n.emailer_home.removeattachment">
				<i class="material-icons">delete</i>
			</ui:button>
		</ui:note>

		<input type="hidden" name="sendPreviousAttachment" value="true">
	</c:if>
	
	<fieldset>
		<legend>
			<orbis:message code="i18n.emailer_home.Body" />
		</legend>
		
		<ui:textarea name="emailBody" type="richBasic" i18n_title="${langLabelL1}">${emailModel.body}</ui:textarea>
		<div class="${isBilingualModeOn ? '' : 'display--none'}">
			<ui:textarea name="l2EmailBody" type="richBasic" i18n_title="${langLabelL2}">${emailModel.l2Body}</ui:textarea>
		</div>
	</fieldset>
	
	<ui:textarea name="emailSignature" type="rich" i18n_title="i18n.emailer_home.EmailSignature">${emailModel.additionalParams['emailSignature']}</ui:textarea>
</ui:formPage>
