<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="orbis" uri="/WEB-INF/tlds/orbis-taglib.tld" %>
<%-- <ui:isConverted type="complete" />   --%>
<c:if test="${empty childNumber}">
	<c:set var="childNumber" scope="request" value="0" />
</c:if>
<c:if test="${empty menuLevel}">
	<c:set var="menuLevel" scope="request" value="0" />
</c:if>
<orbis:message var="dropdownLabelText" code="i18n.menu_view_menuItems.ToggleDropdown" htmlEscape="true" />
<ul class="nav__list ${menuLevel > 0 ? 'is--child' : ''}">
	<c:forEach items="${menuItems}" var="currentItem">
		<c:set var = "selTestMenu"  value = "${currentItem.title}"/>
		<c:set  var="selMenuViewMenuItems" value=""/>
		<c:if test="${not empty currentItem.url && !currentItem.hideInMenu}">
			<c:set var="hasChildren" value="${not empty currentItem.children}" />
			<li class="nav__list-item ${hasChildren ? 'is--parent' : ''} ${currentItem.offline ? 'is--offline' : ''} ${currentItem.active ? 'is--expanded' : ''}">
				
				<c:if test="${hasChildren}">
					<button type="button" aria-label="${dropdownLabelText}" class="btn__default--text btn--info plain js--btn--nav-dropdown-arrow ${currentItem.active ? 'is--expanded' : ''}">
						<i class="material-icons">keyboard_arrow_down</i>
					</button>
				</c:if>
				
				<c:choose>
				 <c:when test="${selTestMenu == 'Appointments'}">
				 <c:set var = "selMenuViewMenuItems" value="sel_menu_appointments" />
				 </c:when>
				 <c:when test="${selTestMenu == 'Dashboard' }">
				  <c:set var = "selMenuViewMenuItems" value="sel_menu_dashboard" />
				 </c:when>
				 <c:when test="${selTestMenu == 'Events & Workshops' }">
				  <c:set var = "selMenuViewMenuItems" value="sel_menu_EventsWorkshops" />
				 </c:when>
				 <c:when test="${selTestMenu == 'Co-Curricular & Volunteer' }">
				 <c:set var = "selMenuViewMenuItems" value="sel_menu_CCV" />
				 </c:when>
				  <c:when test="${selTestMenu == 'Request Position' }">
				 <c:set var = "selMenuViewMenuItems" value="sel_ChildMenu_RequestPosition" />
				 </c:when>
				 <c:when test="${selTestMenu == 'Co-op Rank Match' }">
				 <c:set var = "selMenuViewMenuItems" value="sel_CO-OPRankMatchTest" />
				 </c:when>
				  <c:when test="${selTestMenu == 'Co-op Rank Offer' }">
				 <c:set var = "selMenuViewMenuItems" value="sel_CO-OPRankOfferTest" />
				 </c:when>
				  <c:when test="${selTestMenu == 'Job Postings' }">
				 <c:set var = "selMenuViewMenuItems" value="sel_JobPostingstest" />
				 </c:when>
				 <c:when test = "${selTestMenu == 'Staff' }">
				 <c:set  var= "selMenuViewMenuItems"  value="sel_Staff_test" />
				 </c:when>
				 <c:when test = "${selTestMenu == 'Portal Administration' }">
				 <c:set  var= "selMenuViewMenuItems"  value="sel_PortalAdmin_test" />
				 </c:when>
				</c:choose>
				<a class="clickGuard ${currentItem.active ? 'is--active' : ''} ${selMenuViewMenuItems}" href="${currentItem.url}" ${currentItem.externalUrl == true ? 'target="_blank"' : ''}>${currentItem.title}</a>
				<c:if test="${hasChildren}">
					<c:set var="childNumber" scope="request" value="${childNumber + 1}" />
					<c:set var="menuItems" scope="request" value="${currentItem.children}" />
					<c:set var="menuLevel" scope="request" value="${menuLevel + 1}" />
					<jsp:include page="/WEB-INF/spiralRobot/jsp/menu/menu_view_menuItems.jsp">
						<jsp:param name="menuLevel" value="${menuLevel}" />
						<jsp:param name="childNumber" value="${childNumber}" />
					</jsp:include>
					<c:set var="menuLevel" scope="request" value="${menuLevel - 1}" />
				</c:if>
				
			</li>
		</c:if>
	</c:forEach>
</ul>