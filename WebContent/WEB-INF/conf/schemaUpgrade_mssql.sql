--Sample schemaUpdate format:yyyymmdd_JIRAID
?20230106_OUTCOME-10706
insert portal_migration_history (migrationKey, dateRun, success)  values ('sample1' ,dbo.fn_currentDate(), 1) 
?202301013_OUTCOME-11141
INSERT INTO user_group (name,category,externalGroup,primaryGroup,description) SELECT 'Can View Satisfaction Survey Report', 'Appointment Booking', 0, 0, 'This permission controls who can view the satisfaction survey report within the reports tab of each appointments module' EXCEPT SELECT name, 'Appointment Booking', 0, 0, 'This permission controls who can view the satisfaction survey report within the reports tab of each appointments module' FROM user_group WHERE name='Can View Satisfaction Survey Report'; --NOFAIL
?20230120_OUTCOME-10990
DELETE nwtra2 FROM interaction_note_work_term_record_assigned nwtra JOIN coop_wtr_main wtr ON wtr.id= nwtra.workTermRecordd AND wtr.jobPosting IS NOT NULL JOIN interaction_note_work_term_record_assigned nwtra2 ON nwtra2.note= nwtra.note AND nwtra2.id> nwtra.id AND nwtra2.workTermRecordd= wtr.jobPosting; --NOFAIL
?20230106_OUTCOME-11067
update lcol set lcol.visible=1, lcol.ordinal=5 from grid g inner join grid_category gcat on gcat.grid=g.id inner join grid_column lcol on gcat.id=lcol.category where g.gridID='qf_studentQualificationsWaitingForApproval' and lcol.colID='qualified' --NOFAIL
update lcol set lcol.ordinal=5 from grid g inner join grid_category gcat on gcat.grid=g.id inner join grid_column lcol on gcat.id=lcol.category where g.gridID='qf_studentQualificationsWaitingForApproval' and lcol.colID='uploadedDocumentId' --NOFAIL
insert into portal_migration_history(migrationKey, dateRun, success) values ('OUTCOME-11067_qf_studentQualificationsWaitingForApproval', dbo.fn_currentDate(), 1)
?20230123_OUTCOME-10955
insert into coop_currency (name, l2Name, module, ordinal) select 'Canadian', 'Canadien', m.id, 1 from coop_module m except select c.name, 'Canadien', c.module, 1 from coop_currency c where c.name='Canadian'; --NOFAIL
insert into coop_currency (name, l2Name, module, ordinal) select 'US', 'US', m.id, 2 from coop_module m except select c.name, 'US', c.module, 2 from coop_currency c where c.name='US'; --NOFAIL
insert into coop_currency (name, l2Name, module, ordinal) select 'Other', 'Autre', m.id, 3 from coop_module m except select c.name, 'Autre', c.module, 3 from coop_currency c where c.name='Other'; --NOFAIL
?20230322_OUTCOME-11323
JAVA upgradePortalTo20230322_OUTCOME11323
?20230316_OUTCOME-11125
JAVA upgradePortalTo20230316_OUTCOME_11125
?20230406_OUTCOME-11388
UPDATE insights_module SET enableSpiralRobot = 1; --NOFAIL
?20230410_OUTCOME-11406
update lv_lastViewed set insightFilters = null where insightFilters is not null; --NOFAIL
?20230220_OUTCOME-11161
update exp_posting set numberOfRecords = (select count (r.id) from exp_record r where r.posting=exp_posting.id); --NOFAIL
?20230413_OUTCOME-10447
JAVA updatePortalTo20230413_OUTCOME_10447
?20230420_OUTCOME-11450
with currencyOrder as (select ordinal, row_number() over (partition by [module] order by id asc) as newOrder from coop_currency) update currencyOrder set ordinal=newOrder; --NOFAIL
?20230427_OUTCOME-11484
DELETE qm FROM acrm_registration_question_df_question_mapping qm JOIN acrm_registration_question_df_question_mapping qm2 ON qm2.dfQuestion=qm.dfQuestion AND qm2.regQuestion=qm.regQuestion AND qm2.id!=qm.id AND qm2.mappingType=qm.mappingType WHERE qm.module IS NULL AND qm2.module IS NOT NULL AND qm.mappingType='com.orbis.web.content.acrm.AcrmRegistrationQuestionWorkTermMapping'; --NOFAIL
DELETE qm FROM st_rank_work_term_mapping qm JOIN st_rank_work_term_mapping qm2 ON qm2.dfQuestion=qm.dfQuestion AND qm2.rankingField=qm.rankingField AND qm2.id!=qm.id WHERE qm.module IS NULL AND qm2.module IS NOT NULL; --NOFAIL
DELETE qm FROM acrm_division_question_df_question_mapping qm JOIN acrm_division_question_df_question_mapping qm2 ON qm2.dfQuestion=qm.dfQuestion AND qm2.divQuestion=qm.divQuestion AND qm2.id!=qm.id WHERE qm.module IS NULL AND qm2.module IS NOT NULL; --NOFAIL
DELETE qm FROM acrm_org_question_df_question_mapping qm JOIN acrm_org_question_df_question_mapping qm2 ON qm2.dfQuestion=qm.dfQuestion AND qm2.orgQuestion=qm.orgQuestion AND qm2.id!=qm.id WHERE qm.module IS NULL AND qm2.module IS NOT NULL; --NOFAIL
UPDATE qm SET qm.module=cwmm.coopModule FROM acrm_registration_question_df_question_mapping qm JOIN df_question q ON q.id=qm.dfQuestion JOIN df_category dc ON dc.id=q.category JOIN coop_wtr_model_main cwmm ON cwmm.DFModel=dc.model WHERE qm.module IS NULL AND qm.mappingType='com.orbis.web.content.acrm.AcrmRegistrationQuestionWorkTermMapping'; --NOFAIL
UPDATE qm SET qm.module=cwmm.coopModule FROM st_rank_work_term_mapping qm JOIN df_question q ON q.id=qm.dfQuestion JOIN df_category dc ON dc.id=q.category JOIN coop_wtr_model_main cwmm ON cwmm.DFModel=dc.model WHERE qm.module IS NULL; --NOFAIL
UPDATE qm SET qm.module=cwmm.coopModule FROM acrm_division_question_df_question_mapping qm JOIN df_question q ON q.id=qm.dfQuestion JOIN df_category dc ON dc.id=q.category JOIN coop_wtr_model_main cwmm ON cwmm.DFModel=dc.model WHERE qm.module IS NULL; --NOFAIL
UPDATE qm SET qm.module=cwmm.coopModule FROM acrm_org_question_df_question_mapping qm JOIN df_question q ON q.id=qm.dfQuestion JOIN df_category dc ON dc.id=q.category JOIN coop_wtr_model_main cwmm ON cwmm.DFModel=dc.model WHERE qm.module IS NULL; --NOFAIL
?20230426_OUTCOME-11370
JAVA upgradePortalTo20230426_OUTCOME_11370
?20230404_OUTCOME-10941
insert into portal_config(orbisKey, orbisValue) values('ALLOWED_EMAIL_SENDER_DOMAIN', ''); --NOFAIL
?20230314_OUTCOME-11241
insert into portal_config (orbisKey, orbisValue) values ('SMTP_CONNECTION_CACHE_MESSAGE_COUNT', '1'); --NOFAIL
?20230314_OUTCOME-11338
JAVA upgradePortalTo20230316_OUTCOME11338
?20230509_OUTCOME-11487
insert into portal_config(orbisKey, orbisValue) values('SECRET_CONFIGS', ''); --NOFAIL
?20230508_OUTCOME-10974
insert into portal_config(orbisKey, orbisValue) values('GTSM_RACE_OR_ETHNICITY', 'i18n.PortalConfig.GTSM.RaceOrEthnicity'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_YEAR_OF_STUDY', 'i18n.PortalConfig.GTSM.YearOfStudy'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_ACADEMIC_PROGRAM_OF_STUDY', 'i18n.PortalConfig.GTSM.AcademicProgramOfStudy'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_FINANCIAL_AID', 'i18n.PortalConfig.GTSM.FinancialAid'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_ACCESSIBILITY', 'i18n.PortalConfig.GTSM.Accessibility'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_CITIZENSHIP_INDICATOR', 'i18n.PortalConfig.GTSM.CitizenshipIndicator'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_RESIDENCY_INDICATOR', 'i18n.PortalConfig.GTSM.ResidencyIndicator'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_GENDER_IDENTITY', 'i18n.PortalConfig.GTSM.GenderIdentity'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_STUDENT_CONDUCT', 'i18n.PortalConfig.GTSM.StudentConduct'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_INTERESTED_SERVICES', 'i18n.PortalConfig.GTSM.InterestedServices'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_INDUSTRY_CLASSIFICATION', 'i18n.PortalConfig.GTSM.IndustryClassification'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_ORGANIZATION_SIZE', 'i18n.PortalConfig.GTSM.OrganizationSize'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_ORGANIZATION_TYPE', 'i18n.PortalConfig.GTSM.OrganizationType'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_WORK_SAFETY_COMPLIANCE', 'i18n.PortalConfig.GTSM.WorkSafetyCompliance'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_INSURANCE', 'i18n.PortalConfig.GTSM.Insurance'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_EMPLOYER_LOCATION', 'i18n.PortalConfig.GTSM.EmployerLocation'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_EMPLOYER_CONDUCT', 'i18n.PortalConfig.GTSM.EmployerConduct'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_CUSTOM_MAPPING_1', 'i18n.PortalConfig.GTSM.CustomMapping1'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_CUSTOM_MAPPING_2', 'i18n.PortalConfig.GTSM.CustomMapping2'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_CUSTOM_MAPPING_3', 'i18n.PortalConfig.GTSM.CustomMapping3'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_CUSTOM_MAPPING_4', 'i18n.PortalConfig.GTSM.CustomMapping4'); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('GTSM_CUSTOM_MAPPING_5', 'i18n.PortalConfig.GTSM.CustomMapping5'); --NOFAIL
?20230515_OUTCOME-11499
update df_question_site_mapping set siteMapping = (select pc.id from portal_config pc where pc.orbisKey = 'DFQSM_JOB_LOCATION_TYPE') where id in (select dqsm.id from df_model dm join df_category dc on dm.id = dc.model join df_question dq on dc.id = dq.category join df_question_site_mapping dqsm on dq.id = dqsm.dfQuestion where dm.modelEntityClassName = 'com.orbis.web.content.exp.EXPTypeRecordModel' and dq.answerField1 = 's9'); --NOFAIL
INSERT INTO df_question_site_mapping (dfQuestion, siteMapping) select dq.id, (select pc.id from portal_config pc where pc.orbisKey = 'DFQSM_JOB_LOCATION_TYPE') from df_model dm join df_category dc on dm.id = dc.model join df_question dq on dc.id = dq.category where dm.modelEntityClassName = 'com.orbis.web.content.exp.EXPTypeRecordModel' and dq.answerField1 = 's9'; --NOFAIL
?20230515_OUTCOME-11505
JAVA upgradePortalTo20230515_OUTCOME11505
?20230510_OUTCOME-11488
JAVA upgradePortalTo20230510_OUTCOME_11488
insert into portal_config(orbisKey, orbisValue) values('DIGITARY_URL', ''); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('DIGITARY_USERNAME', ''); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('DIGITARY_PASSWORD', ''); --NOFAIL
insert into portal_config(orbisKey, orbisValue) values('DIGITARY_CERTIFICATION_KEY', ''); --NOFAIL
?20230523_OUTCOME-11559
UPDATE npp SET npp.crmOrganization=o.id, npp.organization=o.name FROM np_posting npp JOIN company comp ON comp.id = npp.crmCompany join organization o on o.id=comp.organization WHERE npp.crmOrganization is NULL; --NOFAIL
UPDATE cwm SET cwm.organization = npp.crmOrganization FROM coop_wtr_main cwm JOIN np_posting npp ON cwm.jobPosting = npp.id WHERE cwm.organization is NULL; --NOFAIL
?20230606_OUTCOME-11480
UPDATE exp_type_workflow_template SET integrationCreateRecords = IIF(importDontCreateRecords = 1, 0, 1); --NOFAIL
DECLARE @command VARCHAR(255) = 'ALTER TABLE exp_type_workflow_template DROP CONSTRAINT ' + (SELECT TOP 1 c.name FROM sys.default_constraints c JOIN sys.columns col ON col.column_id = c.parent_column_id AND col.object_id=c.parent_object_id WHERE col.name='importDontCreateRecords' AND c.parent_object_id=OBJECT_ID('exp_type_workflow_template')); EXEC (@command); --NOFAIL
ALTER TABLE exp_type_workflow_template DROP COLUMN IF EXISTS importDontCreateRecords; --NOFAIL
?20230615_OUTCOME-11540
INSERT INTO portal_config (orbisKey, orbisValue) SELECT 'INCLUDE_PROSPECTS_IN_GLOBAL_SEARCH','0' EXCEPT SELECT orbisKey, '0' FROM portal_config WHERE orbisKey='INCLUDE_PROSPECTS_IN_GLOBAL_SEARCH'; --NOFAIL
?20230703_OUTCOME-11625
IF NOT EXISTS (SELECT name FROM sys.indexes WHERE name = N'idx_code_unique_notnull' AND object_id = OBJECT_ID(N'interview_interviewer', N'U')) CREATE UNIQUE NONCLUSTERED INDEX idx_code_unique_notnull ON interview_interviewer(code) WHERE code IS NOT NULL; --NOFAIL
?20230712_OUTCOME-11488
insert into portal_config(orbisKey, orbisValue) values('DIGITARY_DOC_ID', ''); --NOFAIL
?20230522_OUTCOME-11449
JAVA upgradePortalTo20230522_OUTCOME_11449
?20230720_OUTCOME_11555
UPDATE event_letter_template SET defaultForType = 0 WHERE type = 0; --NOFAIL
JAVA upgradePortalTo20230720_OUTCOME_11555
?20230727_OUTCOME-11702
JAVA upgradePortalTo20230801_OUTCOME_11702
?20230802_OUTCOME-11644
JAVA upgradePortalTo20230802_OUTCOME_11644
?20230828_OUTCOME-11649
JAVA upgradePortalTo20230828_OUTCOME_11649
?20230831_OUTCOME-11124
JAVA upgradePortalTo20230831_OUTCOME_11124
?20230905_OUTCOME-11497
update dashboard_message set legacy = 1; --NOFAIL
?20230928_OUTCOME-11845
JAVA upgradePortalTo20230928_OUTCOME_11845
?20231204_OUTCOME-11992
insert into portal_config(orbisKey, orbisValue) values('GOOGLE_ANALYTICS_MEASUREMENT_ID', ''); --NOFAIL
?20231017_OUTCOME-11842
JAVA upgradePortalTo20231017_OUTCOME_11842
?20231114_OUTCOME-11964
INSERT INTO portal_config (orbisKey, orbisValue) VALUES ('COMPETENCY_DISPLAY_SETTING', '1'); --NOFAIL
?20230729_OUTCOME-11682
UPDATE portal_config SET orbisKey = 'DIGITARY_DOC_TYPE' WHERE orbisKey = 'DIGITARY_DOC_ID'; --NOFAIL
?20240102_OUTCOME-12068
insert into portal_config(orbisKey, orbisValue) values('DIGITARY_IDP', ''); --NOFAIL
?20240111_OUTCOME-11987
DELETE FROM df_question_site_mapping WHERE siteMapping IN (SELECT id FROM portal_config WHERE orbisKey = 'DFQSM_L2_APPLICATION_INSTRUCTION'); --NOFAIL
DELETE FROM portal_config WHERE orbisKey = 'DFQSM_L2_APPLICATION_INSTRUCTION'; --NOFAIL
DELETE FROM df_question_site_mapping WHERE siteMapping IN (SELECT id FROM portal_config WHERE orbisKey = 'DFQSM_L2_DESCRIPTION'); --NOFAIL
DELETE FROM portal_config WHERE orbisKey = 'DFQSM_L2_DESCRIPTION'; --NOFAIL
DELETE FROM df_question_site_mapping WHERE siteMapping IN (SELECT id FROM portal_config WHERE orbisKey = 'DFQSM_L2_TITLE'); --NOFAIL
DELETE FROM portal_config WHERE orbisKey = 'DFQSM_L2_TITLE'; --NOFAIL
INSERT INTO portal_config (orbisKey, orbisValue) values ('DFQSM_PLACEMENT_METHOD', 'i18n.PortalConfig.DFQSM.PlacementM1125259841418166'); --NOFAIL
?20240116_OUTCOME-12093
INSERT INTO portal_config (orbisKey, orbisValue) values ('DFQSM_PAYMENT_NAME', 'i18n.PortalConfig.DFQSM.PaymentNam1418040940091500'); --NOFAIL
INSERT INTO portal_config (orbisKey, orbisValue) values ('DFQSM_PAYMENT_VALUE', 'i18n.PortalConfig.DFQSM.PaymentVal7634117895411759'); --NOFAIL
INSERT INTO portal_config (orbisKey, orbisValue) values ('DFQSM_WORKPLACE_TYPE', 'i18n.PortalConfig.DFQSM.WorkplaceT1544261977268078'); --NOFAIL
?20231019_OUTCOME-11382
JAVA upgradePortalTo20231019_OUTCOME_11382
?20230920_OUTCOME-11794
JAVA upgradePortalTo20230920_OUTCOME_11794
?20240221_OUTCOME-12154
JAVA upgradePortalTo20240221_OUTCOME_12154
update dashboard_module set studentTabsOrder = studentTabsOrder + ',st16'; --NOFAIL
update dashboard_module set portalStaffTabsOrder = portalStaffTabsOrder + ',pt24'; --NOFAIL
?20230804_OUTCOME-11709
JAVA upgradePortalTo20230804_OUTCOME11709
?20230804_OUTCOME-11751
UPDATE interaction_form SET draftSubmittedOn = dateCreated WHERE draftSubmittedOn IS NULL AND draft = 0; --NOFAIL
?20230810_OUTCOME-11711
JAVA upgradePortalTo202308810_OUTCOME_11711
?20231111_OUTCOME-11585
update doc_type set maxDefaultDocs=1 where allowDefault=1; --NOFAIL
?20240222_OUTCOME-12177
DELETE FROM portal_config WHERE orbisKey='SHOW_CAMPUS_LINK_TAB'; --NOFAIL
?20240319_OUTCOME-12208
DECLARE @command varchar(255) = 'ALTER TABLE np_posting DROP CONSTRAINT ' + (select top 1 c.name from sys.default_constraints c join sys.columns col on col.column_id = c.parent_column_id and col.object_id=c.parent_object_id where col.name='featuredPaymentApproved'); exec (@command); --NOFAIL
ALTER TABLE np_posting DROP COLUMN IF EXISTS featuredPaymentApproved; --NOFAIL
?20240409_OUTCOME-12229
JAVA upgradePortalTo20240409_OUTCOME_12229
UPDATE user_group SET name='Experiential - Faculty View Access' WHERE name='Experiential Faculty Advisor - Can View'; --NOFAIL
UPDATE user_group SET name='Faculty Course Management' WHERE name='Experiential Faculty Advisor - Can Manage'; --NOFAIL
?20240405_OUTCOME-12057
JAVA upgradePortalTo20240405_OUTCOME_12057
?20240501_OUTCOME-12226
DELETE FROM portal_config WHERE orbisKey='VC_BROADCAST_MESSAGE_ACTIVE';
?20240722_OUTCOME-12508
UPDATE np_module SET savedSearchNumDays = IIF(jobSearchLifespan = 0 OR savedSearchNumDays < jobSearchLifespan, savedSearchNumDays, jobSearchLifespan);
DECLARE @command VARCHAR(255) = 'ALTER TABLE np_module DROP CONSTRAINT ' + (SELECT TOP 1 c.name FROM sys.default_constraints c JOIN sys.columns col ON col.column_id = c.parent_column_id AND col.object_id=c.parent_object_id WHERE col.name='jobSearchLifespan' AND c.parent_object_id=OBJECT_ID('np_module')); EXEC (@command); --NOFAIL
ALTER TABLE np_module DROP COLUMN IF EXISTS jobSearchLifespan; --NOFAIL
?20240819_OUTCOME_12576
JAVA upgradePortalTo20240819_OUTCOME_12576
?20241007_OUTCOME_12556
JAVA upgradePortalTo20241007_OUTCOME_12556
?20250310_OUTCOME-13532
INSERT INTO portal_config (orbisKey, orbisValue) values ('LOADING_OVERLAY_SHOW_TIME', '1000'); --NOFAIL
?20250527_OUTCOME_13902
JAVA upgradePortalTo20250527_OUTCOME_13902
?20250730_OUTCOME_13919
DROP VIEW IF EXISTS dbo.v_np_application_job;
CREATE VIEW dbo.v_np_application_job WITH SCHEMABINDING AS select na.job, na.status, na.anyPositionAccepted, count_big(*) appCount from dbo.np_application na group by na.job, na.status, na.anyPositionAccepted;
CREATE UNIQUE CLUSTERED INDEX ix_v_np_application_job ON dbo.v_np_application_job (job, status, anyPositionAccepted);
create nonclustered index ix_v_np_application_job_job on dbo.v_np_application_job (job) include (appCount);
create nonclustered index ix_v_np_application_job_job_status on dbo.v_np_application_job (job, status) include (appCount);
create nonclustered index ix_v_np_application_job_job_anyPositionAccepted on dbo.v_np_application_job (job, anyPositionAccepted) include (appCount);
DROP VIEW IF EXISTS dbo.v_interaction_note_notification;
CREATE VIEW dbo.v_interaction_note_notification WITH SCHEMABINDING AS SELECT inn.id, inn.notificationType, inn.note, inn.dateAcknowledged, inn.dateViewed, inn.role, inn.teamMember, inn.uzer, n.noteType, n.requireAcknowledgement, n.priorityy, n.system FROM dbo.interaction_note_notification inn join dbo.interaction_note as n on n.id=inn.note where inn.dateAcknowledged is null or inn.dateViewed is null;
CREATE UNIQUE CLUSTERED INDEX ix_v_interaction_note_notification ON dbo.v_interaction_note_notification (id);
CREATE NONCLUSTERED INDEX ix_v_interaction_note_notification_role_requireAcknowledgement_noteType ON dbo.v_interaction_note_notification (role, requireAcknowledgement, noteType) include (note, dateAcknowledged, dateViewed);
DROP VIEW IF EXISTS dbo.v_interview_entry_general;
CREATE VIEW dbo.v_interview_entry_general WITH SCHEMABINDING AS select ie.id entryId, ie.interviewee, isnp.job, isnp.interviewSchedule, slot.id slotId, slot.fromDate, slot.toDate, slot.interviewDay, interviewDay.interviewer from dbo.interview_entry ie join dbo.interview_slot slot on ie.interviewSlot = slot.id join dbo.interview_day interviewDay on interviewDay.id=slot.interviewDay join dbo.interview_interviewer interviewer on interviewDay.interviewer = interviewer.id join dbo.interview_schedule_np_posting isnp on interviewer.interviewSchedule = isnp.interviewSchedule;
CREATE UNIQUE CLUSTERED INDEX v_interview_entry_general ON dbo.v_interview_entry_general (entryId, job);
create nonclustered index ix_v_interview_entry_general_date on dbo.v_interview_entry_general (interviewee, fromDate, toDate, slotId) include (job);
DROP VIEW IF EXISTS dbo.v_interaction_message_recipient;
CREATE VIEW dbo.v_interaction_message_recipient WITH SCHEMABINDING AS SELECT imr.id, imr.recipientType, imr.message, imr.dateViewed, imr.dateResponded, imr.dateMarkedAsRead, imr.dateAcknowledged, imr.deleted, imr.archived, imr.copied, imr.teamMember, imr.uzer, imr.role, im.createdBy, im.dateCreated, im.priority FROM dbo.interaction_message_recipient imr join dbo.interaction_message as im on im.id=imr.message WHERE imr.dateMarkedAsRead is null;
CREATE UNIQUE CLUSTERED INDEX ix_v_interaction_message_recipient ON dbo.v_interaction_message_recipient (id);
CREATE NONCLUSTERED INDEX ix_v_interaction_message_recipient_archived_recipientType_message_createdBy ON dbo.v_interaction_message_recipient (archived, recipientType) INCLUDE (message, createdBy, priority);
CREATE NONCLUSTERED INDEX ix_v_interaction_message_recipient_role_archived_message_createdBy ON dbo.v_interaction_message_recipient (role, archived) INCLUDE (message, createdBy, priority);
CREATE NONCLUSTERED INDEX ix_v_interaction_message_recipient_archived_role_priority ON dbo.v_interaction_message_recipient (archived,role,priority) INCLUDE (message,createdBy);
DROP VIEW IF EXISTS dbo.v_interaction_team_member_role;
CREATE VIEW dbo.v_interaction_team_member_role WITH SCHEMABINDING AS SELECT tmr.id, tmr.role, tmr.teamMember, tmr.active tmrActive, tm.member, u.firstName, u.lastName, tm.active tmActive FROM dbo.interaction_team_member_role tmr join dbo.interaction_team_member as tm on tm.id=tmr.teamMember join dbo.user_details u on u.user_details_id=tm.member;
CREATE UNIQUE CLUSTERED INDEX ix_v_interaction_team_member_role ON dbo.v_interaction_team_member_role (id);
CREATE NONCLUSTERED INDEX ix_v_interaction_team_member_role_role ON v_interaction_team_member_role (role) include (firstName, lastName, tmrActive, tmActive);
DROP VIEW IF EXISTS dbo.v_interview_interviewee_job;
CREATE VIEW dbo.v_interview_interviewee_job WITH SCHEMABINDING AS select na.job, count_big(*) intervieweeCount from dbo.interview_interviewee iee join dbo.np_application na on na.id=iee.application group by na.job;
CREATE UNIQUE CLUSTERED INDEX ix_v_interview_interviewee_job ON dbo.v_interview_interviewee_job (job);
?20250801_OUTCOME_13919
DROP VIEW IF EXISTS dbo.v_np_application_studentApplications;
CREATE VIEW dbo.v_np_application_studentApplications WITH SCHEMABINDING AS SELECT cas.id casId, COUNT_BIG(*) AS applicationCount FROM dbo.coop_admission_seq cas JOIN dbo.coop_term actualTerm ON actualTerm.id = cas.actualTerm JOIN dbo.coop_admission admission ON admission.id = cas.admission JOIN dbo.np_application na ON na.uzer = admission.student JOIN dbo.np_posting p ON p.id = na.job AND p.term = actualTerm.jobPostingTerm where admission.active=1 and cas.termType='Work' GROUP BY cas.id;
CREATE UNIQUE CLUSTERED INDEX ix_v_np_application_studentApplications ON dbo.v_np_application_studentApplications (casId);
DROP VIEW IF EXISTS dbo.v_interview_entry_studentInterviews;
CREATE VIEW dbo.v_interview_entry_studentInterviews WITH SCHEMABINDING AS select seq.id as seqId, ie.id, count_big(*) dupeCount from dbo.interview_entry ie join dbo.interview_slot slot on ie.interviewSlot = slot.id join dbo.interview_day interviewDay on interviewDay.id=slot.interviewDay join dbo.interview_interviewer interviewer on interviewDay.interviewer = interviewer.id join dbo.interview_schedule_np_posting isnp on interviewer.interviewSchedule = isnp.interviewSchedule join dbo.np_posting job on isnp.job = job.id join dbo.coop_term actualTerm on actualTerm.jobPostingTerm= job.term join dbo.coop_admission ca on ca.student=ie.interviewee join dbo.coop_admission_seq seq on seq . admission = ca . id and seq . actualTerm = actualTerm . id where ca.active=1 and seq.termType='Work' group by seq.id, ie.id;
CREATE UNIQUE CLUSTERED INDEX ix_v_interview_entry_studentInterviews ON dbo.v_interview_entry_studentInterviews (seqId, id);
?20250806_OUTCOME_14136
UPDATE interaction_note set dateUpdated = dateCreated where dateUpdated is null and dateCreated >= cast('2025-07-01 00:00:00.0000000' as datetime2); --NOFAIL
UPDATE interaction_message set dateUpdated = dateCreated where dateUpdated is null and dateCreated >= cast('2025-07-01 00:00:00.0000000' as datetime2); --NOFAIL
UPDATE interaction_task set dateUpdated = dateCreated where dateUpdated is null and dateCreated >= cast('2025-07-01 00:00:00.0000000' as datetime2); --NOFAIL
UPDATE interaction_form set dateUpdated = dateCreated where dateUpdated is null and dateCreated >= cast('2025-07-01 00:00:00.0000000' as datetime2); --NOFAIL
UPDATE interaction_engagement set dateUpdated = dateCreated where dateUpdated is null and dateCreated >= cast('2025-07-01 00:00:00.0000000' as datetime2); --NOFAIL
--make sure this comment is at the End of File, empty lines break schema upgrade