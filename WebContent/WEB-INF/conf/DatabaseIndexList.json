[
  {
    "includeColumns": [],
    "indexColumns": ["dateLastSaved"],
    "tableName": "np_posting"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "id",
      "NPostingModule",
      "status",
      "submitForApproval",
      "position",
      "crmCompany",
      "l2Position",
      "makePostingLiveId"
    ],
    "tableName": "np_posting"
  },
  {
    "includeColumns": ["name"],
    "indexColumns": [
      "id",
      "organization"
    ],
    "tableName": "company"
  },
  {
    "includeColumns": ["id"],
    "indexColumns": [
      "name",
      "deleted",
      "organization"
    ],
    "tableName": "company"
  },
  {
    "includeColumns": [],
    "indexColumns": ["dateApplied"],
    "tableName": "np_application"
  },
  {
    "includeColumns": [],
    "indexColumns": ["dateCreated"],
    "tableName": "email_log"
  },
  {
    "includeColumns": [],
    "indexColumns": ["insertionBatchId"],
    "tableName": "interaction_note"
  },
  {
    "includeColumns": [
      "deleted",
      "className",
      "temprary",
      "userStatus",
      "emailAddress",
      "company"
    ],
    "indexColumns": [
      "lastName",
      "firstName",
      "username",
      "USER_DETAILS_ID"
    ],
    "tableName": "user_details"
  },
  {
    "includeColumns": [
      "className",
      "deleted"
    ],
    "indexColumns": [
      "emailAddress",
      "username",
      "userLocale"
    ],
    "tableName": "user_details"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "name",
      "deleted",
      "userStatus",
      "id"
    ],
    "tableName": "organization"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "id",
      "name"
    ],
    "tableName": "organization"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "id",
      "name"
    ],
    "tableName": "np_module"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "t1",
      "t9"
    ],
    "tableName": "integration_t8"
  },
  {
    "includeColumns": [],
    "indexColumns": ["dateViewed"],
    "tableName": "np_posting_view"
  },
  {
    "includeColumns": [],
    "indexColumns": ["calendarName"],
    "tableName": "sa_module"
  },
  {
    "includeColumns": [],
    "indexColumns": ["saModuleName"],
    "tableName": "integration_migration_slot_type"
  },
  {
    "includeColumns": [],
    "indexColumns": ["legacyId"],
    "tableName": "sa_type"
  },
  {
    "includeColumns": [],
    "indexColumns": ["legacyId"],
    "tableName": "integration_migration_slot_term"
  },
  {
    "includeColumns": [],
    "indexColumns": ["saModuleName"],
    "tableName": "integration_migration_slot_term"
  },
  {
    "includeColumns": [],
    "indexColumns": ["saModuleName"],
    "tableName": "integration_migration_slot"
  },
  {
    "includeColumns": [],
    "indexColumns": ["providerLegacyId"],
    "tableName": "integration_migration_slot"
  },
  {
    "includeColumns": [],
    "indexColumns": ["customerLegacyId"],
    "tableName": "integration_migration_slot"
  },
  {
    "includeColumns": [],
    "indexColumns": ["bookedByLegacyId"],
    "tableName": "integration_migration_slot"
  },
  {
    "includeColumns": [],
    "indexColumns": ["cancelledByLegacyId"],
    "tableName": "integration_migration_slot"
  },
  {
    "includeColumns": [],
    "indexColumns": ["selectedTypeLegacyId"],
    "tableName": "integration_migration_slot"
  },
  {
    "includeColumns": [],
    "indexColumns": ["adminLegacyId"],
    "tableName": "integration_migration_slot_member_admin"
  },
  {
    "includeColumns": [],
    "indexColumns": ["saModuleName"],
    "tableName": "integration_migration_slot_member_admin"
  },
  {
    "includeColumns": [],
    "indexColumns": ["saModuleName"],
    "tableName": "integration_migration_slot_member_clerk"
  },
  {
    "includeColumns": [],
    "indexColumns": ["clerkLegacyId"],
    "tableName": "integration_migration_slot_member_clerk"
  },  
  {
    "includeColumns": [],
    "indexColumns": ["eventType", "l2EventType"],
    "tableName": "portal_log"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "dateCreated",
      "seq",
      "id",
      "jobPosting"
    ],
    "tableName": "coop_wtr_main"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "id",
      "student"
    ],
    "tableName": "coop_admission"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "actualTerm",
      "admission",
      "id"
    ],
    "tableName": "coop_admission_seq"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "roundRunIn",
      "rankAndMatchEnabled",
      "status",
      "id"
    ],
    "tableName": "np_posting"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "eventType",
      "jobPosting",
      "rankingRound"
    ],
    "tableName": "portal_log"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "message",
      "recipientType",
      "archived",
      "uzer",
      "teamMember",
    ],
    "tableName": "interaction_message_recipient"
  },
  {
    "includeColumns": ["active"],
    "indexColumns": [
      "member",
      "team"
    ],
    "tableName": "interaction_team_member"
  },
  {
    "includeColumns": [
      "id",
      "noteType",
      "createdBy",
      "subCategory",
      "dateCreated",
      "category",
      "priorityy"
    ],
    "indexColumns": [
      "parent",
      "system"
    ],
    "tableName": "interaction_note"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "recordType",
      "competency",
      "ccModule"
    ],
    "tableName": "competency_anticipated"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "userStatus",
      "major1Code"
    ],
    "tableName": "user_details"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "student",
      "tct"
    ],
    "tableName": "exp_student_experience"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "eventUser",
      "eventDate",
      "application"
    ],
    "tableName": "portal_log"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "noteType",
      "requireAcknowledgement"
    ],
    "tableName": "interaction_note"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "requireAcknowledgement"
    ],
    "tableName": "interaction_note"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "dateUpdated"
    ],
    "tableName": "interaction_note"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "createdBy", "dateUpdated"
    ],
    "tableName": "interaction_note"
  },
  {
    "includeColumns": ["dateAcknowledged", "dateViewed"],
    "indexColumns": [
      "note", "role"
    ],
    "tableName": "interaction_note_notification"
  },
  {
    "includeColumns": ["interviewSchedule"],
    "indexColumns": [
      "messageType"
    ],
    "tableName": "interaction_message"
  },
  {
    "includeColumns": ["createdBy"],
    "indexColumns": [
      "priority"
    ],
    "tableName": "interaction_message"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "dateUpdated"
    ],
    "tableName": "interaction_message"
  },
  {
    "includeColumns": ["message"],
    "indexColumns": [
      "dateMarkedAsRead", "role", "archived"
    ],
    "tableName": "interaction_message_recipient"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "uzer", "message"
    ],
    "tableName": "interaction_message_recipient"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "taskType"
    ],
    "tableName": "interaction_task"
  },
  {
    "includeColumns": ["startDate"],
    "indexColumns": [
      "taskType", "statuss"
    ],
    "tableName": "interaction_task"
  },
  {
    "includeColumns": ["statuss"],
    "indexColumns": [
      "taskType", "priorityy", "startDate"
    ],
    "tableName": "interaction_task"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "dateUpdated"
    ],
    "tableName": "interaction_task"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "formType"
    ],
    "tableName": "interaction_form"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "formType", "draft", "statuss"
    ],
    "tableName": "interaction_form"
  },
  {
    "includeColumns": ["statuss"],
    "indexColumns": [
      "formType", "priority", "draft"
    ],
    "tableName": "interaction_form"
  },
  {
    "includeColumns": [],
    "indexColumns": [
      "dateUpdated"
    ],
    "tableName": "interaction_form"
  },
  {
    "includeColumns": ["tag","wtr","sequence","uzer"],
    "indexColumns": [
      "assignType"
    ],
    "tableName": "tag_assign"
  },
  {
    "includeColumns": ["NPostingModule", "crmCompany", "postedBy", "internalStatus", "datePosted", "dateDeadline", "submitForApproval", "rankPositionsLeft", "rankPositionsFilled", "division", "organization", "city", "jobType", "position", "sector", "competitionNumber", "dateLive", "numberOfPositions", "roundRunIn", "crmOrganization"],
    "indexColumns": [
      "term", "status"
    ],
    "tableName": "np_posting"
  },
  {
    "includeColumns": ["NPostingModule", "postedBy", "submitForApproval"],
    "indexColumns": [
      "rankingsFinalized", "status"
    ],
    "tableName": "np_posting"
  },
  {
    "includeColumns": ["job", "candidate"],
    "indexColumns": [
      "rankByEmployer", "finalizedOn"
    ],
    "tableName": "placement_ranking"
  },
  {
    "includeColumns": ["job", "finalizedOn"],
    "indexColumns": [
      "directPlaced"
    ],
    "tableName": "placement_ranking"
  }
]