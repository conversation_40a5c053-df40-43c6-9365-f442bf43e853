package com.orbis.web.content.clients.queens;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.multiaction.MultiActionController;
import org.springframework.web.servlet.view.RedirectView;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.security.AuthenticationUtils;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.np.NHelper;

import net.sf.acegisecurity.AuthenticationManager;
import net.sf.acegisecurity.providers.AuthenticationProvider;
import net.sf.acegisecurity.providers.encoding.PasswordEncoder;

public class QueensBusinessLoginController extends MultiActionController
        implements InitializingBean
{
    private PasswordEncoder passwordEncoder;

    private String homeUrl;

    private String invalidPage;

    private String preshared = "!%Sv9L×";

    @Override
    public void afterPropertiesSet() throws Exception
    {
    }

    public String getInvalidPage()
    {
        return invalidPage;
    }

    public void setInvalidPage(String invalidPage)
    {
        this.invalidPage = invalidPage;
    }

    public void setPasswordEncoder(PasswordEncoder passwordEncoder)
    {
        this.passwordEncoder = passwordEncoder;
    }

    public void setHomeUrl(String homeUrl)
    {
        this.homeUrl = homeUrl;
    }

    public void setAuthenticationManager(
            AuthenticationManager authenticationManager)
    {
    }

    public void setAuthenticationProvider(
            AuthenticationProvider authenticationProvider)
    {
    }

    /**
     * Default request handling method
     * 
     * @param request
     * @param response
     * @return
     */
    public ModelAndView displayHome(HttpServletRequest request,
            HttpServletResponse response)
    {
        String url = "https://" + PortalUtils.getSiteBaseUrl();
        url += homeUrl;

        ModelAndView mv = new ModelAndView(new RedirectView(url));

        return mv;
    }

    public ModelAndView login(HttpServletRequest request,
            HttpServletResponse response)
    {
        if (!validRequest(request))
        {
            logFailure(request, request.getParameter("studentNumber"));
            ModelAndView mv = new ModelAndView(this.getInvalidPage());
            return mv;
        }

        try
        {

            String studentId = request.getParameter("studentNumber");

            UserDetailsImpl user = UserDetailsHelper.getUserByUsername(studentId);

            String pwd = UserDetailsHelper.makePassword(user.getUsername(),
                    passwordEncoder);
            user.setCleartextPassword(pwd);

            AuthenticationUtils.setUserLoggedIn(request, user, response);

        }
        catch (Exception e)
        {
            logFailure(request, request.getParameter("studentNumber"));
            ModelAndView mv = new ModelAndView(this.getInvalidPage());
            return mv;
        }

        return displayHome(request, response);
    }

    private boolean validRequest(HttpServletRequest request)
    {
        String challenge = request.getParameter("challenge");
        String studentNumber = request.getParameter("studentNumber");

        if (challenge == null || studentNumber == null)
        {
            return false;
        }

        String checkString = passwordEncoder
                .encodePassword(studentNumber + preshared, null);

        if (!checkString.equalsIgnoreCase(challenge))
        {
            return false;
        }

        return true;
    }

    private void logFailure(HttpServletRequest request, String username)
    {

    }

    public ModelAndView toggleFullScreenMode(HttpServletRequest request,
            HttpServletResponse response)
    {
        if (request.getSession().getAttribute("orbisFullScreenMode") != null)
        {
            request.getSession().removeAttribute("orbisFullScreenMode");
        }
        else
        {
            request.getSession().setAttribute("orbisFullScreenMode",
                    request.getParameter("menu").replaceAll("\\r", "")
                            .replaceAll("\\n", "").replaceAll("\"", "'"));
        }
        return NHelper.AJAXResponse("");
    }
}
