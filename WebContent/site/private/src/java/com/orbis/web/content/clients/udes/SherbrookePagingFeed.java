package com.orbis.web.content.clients.udes;

import static com.orbis.utils.LambdaExceptionUtil.rethrowConsumer;
import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.lang.StringUtils.leftPad;
import static org.apache.commons.lang.StringUtils.rightPad;
import static org.apache.commons.lang.StringUtils.substring;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.text.StrSubstitutor;
import org.springframework.web.servlet.ModelAndView;

import com.google.common.collect.ImmutableMap;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.DBUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.OrbisController;
import com.orbis.web.content.clients.waterloo.WaterlooPagingFeed;
import com.orbis.web.content.interview.PagingFeedInterface;

public final class SherbrookePagingFeed implements PagingFeedInterface
{
    /**
     * - a .csv file will be generated. The file name would be formatted this
     * way: YYYYMMDD.csv
     * 
     * - The file would be dropped on a UdeS file server:
     * \\content/private/exports
     * 
     * - Each line in the file has 9 columns. Each column is surrounded with
     * double-quotes and a semi-colon is in between each column also.
     * 
     * - The file will be processed only if on every line in the file the 4th
     * character is ':' AND the 12th character is ':' and the 26th character is
     * either 'E', 'I', 'A' or 'T'
     * 
     * - Interview schedule start time: 5 characters (padded left with a space
     * if necessary)
     * 
     * - Interview schedule end time: 5 characters (padded left with a space if
     * necessary)
     * 
     * - Room number: 5 characters (4 last characters of actual room number,
     * padded with a zero (0)).
     * 
     * - Interview type: 1 character ("E" for interview or "I" for Information
     * session)
     * 
     * - Legacy Organization code: 4 characters (4 first characters, padded with
     * spaces if necessary)
     * 
     * - Team number: 2 characters (Deprecated: "00")
     * 
     * - Company name short: 40 characters (Name truncated if necessary +
     * padding with spaces if necessary)
     * 
     * - Company name long: 60 characters (Name truncated if necessary + padding
     * with spaces if necessary)
     * 
     * - Room number: 10 characters (Room number + padding with spaces)
     * 
     * Example of a line: " 9:30";"11:00";"01068";"E";"BIG0";"01";"Biogénie
     * S.R.D.C. inc. " ;"Biogénie S.R.D.C. inc. ";"B6-1068 "
     */
    private static final String FILENAME_DATE_FORMAT = DateUtils.DF_SHORT_DATE_NOSPACES;

    private static final String TASK_ID = "generatePagingFeedFilesSherbrooke";

    public SherbrookePagingFeed()
    {
    }

    public ModelAndView doGeneratePagingFeed(OrbisController controller,
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = null;

        final Date date = getPagingFeedDate(request);
        String filename = DateUtils.formatDate(date, FILENAME_DATE_FORMAT, null)
                + ".csv";

        try
        {
            final List<String> pagingFeedLines = getPagingFeedLines(date);

            if (!pagingFeedLines.isEmpty())
            {
                mv = controller.getDownloadView(filename,
                        getPagingFeedBytes(pagingFeedLines));
            }
            else
            {
                mv = controller.displayHome(request, response);
                mv.addObject("errorMessage",
                        "Failed to generate feed. No interviews found.");
            }
        }
        catch (Exception e)
        {
            mv = controller.displayHome(request, response);
            mv.addObject("errorMessage", "Failed to generate feed.");
            e.printStackTrace();
        }

        return null;
    }

    private static List<String> getPagingFeedLines(Date date)
    {
        final String pagingFeedQuery = getPagingFeedQuery(date);
        final List<Map<String, Object>> pagingFeed = PortalUtils.getJt()
                .queryForList(pagingFeedQuery);
        final List<String> pagingFeedLines = pagingFeed.stream().map(row -> {
            return Arrays
                    .asList(leftPad(String.valueOf(row.get("StartTime")), 5),
                            leftPad(String.valueOf(row.get("EndTime")), 5),
                            leftPad(getRoomTruncated(row), 5, "0"),
                            "0".equals(row.get("Type").toString())
                                    ? "E"
                                    : "1".equals(row.get("Type").toString()) ? "I"
                                            : "",
                            rightPad(String.valueOf(row.get("LegacyOrg")), 4),
                            String.valueOf(row.get("ScheduleId")),
                            rightPad(
                                    substring(String.valueOf(
                                            row.get("Nom_Organisation1")), 0, 40),
                                    40),
                            rightPad(substring(
                                    String.valueOf(row.get("Nom_Organisation2")), 0,
                                    60), 60),
                            rightPad(String.valueOf(row.get("Room")), 10))
                    .stream().map(str -> String.format("\"%s\"", str))
                    .collect(joining(";"));
        }).collect(toList());
        return pagingFeedLines;
    }

    public static String getRoomTruncated(Map<String, Object> row)
    {
        String room = String.valueOf(row.get("Room"));
        if (!StringUtils.isEmpty(room) && room.toCharArray().length > 4)
        {
            room = room.substring(room.length() - 4);
        }
        return room;
    }

    private static ByteArrayOutputStream getPagingFeedBytes(
            List<String> pagingFeedLines)
    {
        final ByteArrayOutputStream baos = new ByteArrayOutputStream();
        final byte[] lineSeparator = System.lineSeparator().getBytes();
        pagingFeedLines.forEach(rethrowConsumer(line -> {
            baos.write(line.getBytes());
            baos.write(lineSeparator);
        }));
        return baos;
    }

    private static String getPagingFeedQuery(Date date)
    {

        StringBuilder sb = new StringBuilder();
        sb.append(" with z ");
        sb.append(" as ( ");
        sb.append("    select s.[fromDate] as StartTime ");
        sb.append("       ,s.[toDate] as EndTime ");
        sb.append("       ,coalesce(b.name, s.otherRoom, '') as Room ");
        sb.append("       ,sch.type as Type ");
        sb.append("       ,isnull(o.legacyid, '') as LegacyOrg ");
        sb.append("       ,'00' as ScheduleID ");
        sb.append("       ,isnull(p.organization, '') as 'Nom_Organisation1' ");
        sb.append("       ,isnull(p.organization, '') as 'Nom_Organisation2' ");
        sb.append("    from interview_slot s ");
        sb.append("    left join res_book r on s.roombooking = r.id ");
        sb.append("    left join res_res b on r.res = b.id ");
        sb.append("    left join interview_entry e on s.id = e.interviewslot ");
        sb.append(
                "    left join interview_interviewee i on e.id = i.interviewEntry ");
        sb.append("    left join interview_schedule sch on i.schedule = sch.id ");
        sb.append("    left join np_application a on i.application = a.id ");
        sb.append("    left join np_posting p on a.job = p.id ");
        sb.append("    left join company c on c.id = p.CRMcompany ");
        sb.append("    left join organization o on o.id = c.organization ");
        sb.append("    where sch.status in (0, 1, 2, 4)");
        sb.append("    ) ");
        sb.append(" select FORMAT(MIN(z.StartTime), 'HH:mm') as StartTime ");
        sb.append("    ,FORMAT(MAX(z.EndTime), 'HH:mm') as EndTime ");
        sb.append("    ,RIGHT(z.Room, 4) as Room ");
        sb.append("    ,z.Type ");
        sb.append("    ,LEFT(z.LegacyOrg, 4) as LegacyOrg ");
        sb.append("    ,z.ScheduleId ");
        sb.append("    ,LEFT(z.Nom_Organisation1, 40) as Nom_Organisation1 ");
        sb.append("    ,LEFT(z.Nom_Organisation2, 60) as Nom_Organisation2 ");
        sb.append("    ,Room ");
        sb.append(" from z ");
        sb.append(" where z.StartTime >= '${startDate}' ");
        sb.append("    and z.EndTime <= '${endDate}' ");
        sb.append(" group by z.Room ");
        sb.append("    ,z.type ");
        sb.append("    ,z.LegacyOrg ");
        sb.append("    ,z.scheduleid ");
        sb.append("    ,z.nom_organisation1 ");
        sb.append("    ,z.nom_organisation2 ");

        String startDateStr = DateUtils.formatDate(DateUtils.getStartDate(date),
                DBUtils.DB_DATE_TIME_FORMAT, null);
        String endDateStr = DateUtils.formatDate(DateUtils.getEndDate(date),
                DBUtils.DB_DATE_TIME_FORMAT, null);
        Map valueMap = ImmutableMap.of("startDate", startDateStr, "endDate",
                endDateStr);
        String query = new StrSubstitutor(valueMap).replace(sb);

        return query;
    }

    private static File getExportDirectory()
    {
        return new File(PortalUtils.getRealPath("content/private/exports"));
    }

    @Override
    public boolean generatePagingFeedFiles(HttpServletRequest request)
    {
        boolean ok = false;
        final Date date = getPagingFeedDate(request);
        String filename = DateUtils.formatDate(date, FILENAME_DATE_FORMAT, null)
                + ".csv";

        final File exportDirectory = getExportDirectory();
        if (!exportDirectory.exists())
        {
            exportDirectory.mkdirs();
        }

        final File pagingFeedFile = new File(exportDirectory, filename);
        final List<String> pagingFeedLines = getPagingFeedLines(date);

        if (!pagingFeedLines.isEmpty())
        {
            final ByteArrayOutputStream pagingFeedBytes = getPagingFeedBytes(
                    pagingFeedLines);

            FileOutputStream fos = null;
            try
            {
                fos = new FileOutputStream(pagingFeedFile, false);
                fos.write(pagingFeedBytes.toByteArray());
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
            finally
            {
                ok = true;
                IOUtils.closeQuietly(fos);
            }
        }

        return ok;
    }

    public Date getPagingFeedDate(HttpServletRequest request)
    {
        return request != null
                && !StringUtils.isEmpty(request.getParameter("pagingFeedDate"))
                        ? DateUtils.getDatepickerVal(request, "pagingFeedDate")
                        : new Date();
    }
}
