package com.orbis.web.content.clients.moncton;

//test
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.StringReader;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;

import org.apache.commons.io.IOUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.orm.hibernate.HibernateTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.xml.sax.InputSource;

import com.orbis.acegi.providers.dao.hibernate.PersonGroupHelper;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.FilePathUtils;
import com.orbis.utils.GenderUtils;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.doc.Doc;
import com.orbis.web.content.doc.DocHelper;
import com.orbis.web.content.doc.DocModule;
import com.orbis.web.content.doc.DocOwner;
import com.orbis.web.content.doc.DocType2;
import com.orbis.web.content.file.FilePath;
import com.orbis.web.content.registrar.RegistrarDAO;
import com.orbis.web.content.registrar.RegistrarUser;
import com.orbis.web.content.transcript.TranscriptEntry;
import com.orbis.web.report.JasperController;

import net.sf.hibernate.SessionFactory;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.xml.JRXmlLoader;

public class MonctonDAO implements RegistrarDAO, InitializingBean
{
    private static final Log logger = LogFactory.getLog(MonctonDAO.class);

    private static final String IMPORTED_XML_FOLDER = "/content/private/import/";

    private static final String TRANSCRIPT_FOLDER = "/content/private/transcripts/";

    private static String TRANSCRIPT_JRXML_FILENAME = "MonctonTranscript.jrxml";

    private static HibernateTemplate ht;

    private static File xmlTranscriptList = null;

    private static Document doc = null;

    private String transcriptListFilename = "SZVORCH.xml";

    String reportXmlPath;

    public void setTranscriptListFilename(String transcriptListFilename)
    {
        this.transcriptListFilename = transcriptListFilename;
    }

    public void setSessionFactory(SessionFactory sessionFactory)
    {
    }

    @Override
    public void afterPropertiesSet() throws Exception
    {
        this.ht = PortalUtils.getHt();
    }

    @Override
    public RegistrarUser getUserByUserId(String userId)
    {
        MonctonUser ret = null;

        try
        {
            List users = ht.find("from UserDetailsImpl u where u.username=?",
                    userId);
            if (users.size() == 1)
            {
                ret = new MonctonUser();

                // ret.setRow(row);
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return ret;
    }

    @Override
    public UserDetailsImpl synchronizeUserData(RegistrarUser ru,
            HttpServletRequest request)
    {
        UserDetailsImpl user = null;

        MonctonUser lu = (MonctonUser) ru;

        if (lu != null)
        {
            logger.info("synchronizeUserData() start: " + lu.getUsername());
            user = synchronizeUserDetailsImpl(lu);
            logger.info("synchronizeUserData() finish: " + lu.getUsername());
        }

        return user;
    }

    @Override
    public RegistrarUser authenticateUser(String id, String password)
    {
        // Moncton authenticates via ldap
        return null;
    }

    public String getUserIdForUsername(String username)
    {
        return (String) ht
                .find("select u.username from UserDetailsImpl u where u.s27='"
                        + username + "'")
                .get(0);
    }

    private static UserDetailsImpl synchronizeUserDetailsImpl(MonctonUser lu)
    {
        UserDetailsImpl user = null;

        try
        {
            user = UserDetailsHelper.getUserByUsername(lu.getId());

            if (user == null)
            {
                user = new UserDetailsImpl();
                user.setDateCreated(new Date());
            }

            user.setUsername(lu.getId());
            user.setSalutation(lu.getFirstNamePrefix());
            user.setFirstName(lu.getFirstName());
            user.setLastName(lu.getLastName());
            user.setGender(GenderUtils.getGenderLabel(lu.getSex()));
            user.setDob(DateUtils.parseDate(lu.getBirthDate(), "yyyy-MM-dd",
                    Locale.CANADA_FRENCH));
            user.setEmailAddress(lu.getStudentEmail());
            user.setAlternateEmail(lu.getPersonalEmail());

            user.setPhoneNumber(lu.getHomePhoneNumber());
            user.setOtherPhone(lu.getStudentPhoneNumber());
            user.setPhoneCell(lu.getCellPhoneNumber());

            user.setCitizenStatus("CA".equals(lu.getCitizenCode()) ? "YES" : "NO");
            user.setCitizenship(lu.getCitizenDesc());
            user.setS2(lu.getCitizenCode());

            // student address
            user.setStreet1(lu.getStudentAddressStreetLine1());
            user.setStreet2(lu.getStudentAddressStreetLine2());
            user.setS21(lu.getStudentAddressStreetLine3());
            user.setCity(lu.getStudentAddressCity());
            user.setProvince(lu.getStudentAddressProv());
            user.setPostalCode(lu.getStudentAddressPostalCode());
            user.setCountry(lu.getStudentAddressCountry());

            user.setAltStreet1(lu.getHomeAddressStreetLine1());
            user.setAltStreet2(lu.getHomeAddressStreetLine2());
            user.setS22(lu.getHomeAddressStreetLine3());
            user.setAltCity(lu.getHomeAddressCity());
            user.setAltProvince(lu.getHomeAddressProv());
            user.setAltPostalCode(lu.getHomeAddressPostalCode());
            user.setAltCountry(lu.getHomeAddressCountry());

            user.setMajor1Code(lu.getProgram());
            user.setMajor1Descr(lu.getProgramDesc());
            user.setS1(lu.getCampus());

            user.setS23(lu.getProgramGPA());
            user.setS24(lu.getProgramCredits());
            user.setS25(lu.getTotalGPA());
            user.setS26(lu.getTotalCredits());
            user.setS27(lu.getUsername()); // LDAP username

            String pwd = UserDetailsHelper.makePassword(user.getUsername(),
                    PortalUtils.getPasswordEncoder());
            user.setPassword(
                    PortalUtils.getPasswordEncoder().encodePassword(pwd, null));
            user.setCleartextPassword(pwd);

            UserDetailsHelper.addUserGroup(user, PersonGroupHelper.STUDENT);

            user.setEnabled(true);
            user.setAccountNonExpired(true);
            user.setCredentialsNonExpired(true);
            user.setApprovalStatus(UserDetailsImpl.APPROVAL_STATUS_APPROVED);

            user.setDateUpdated(new Date());
            user.setLastUpdatedBy("Integration");

            ht.saveOrUpdate(user);

        }
        catch (Exception e)
        {
            e.printStackTrace();
            logger.error("synchronizeUserDetailsImpl: " + e.getMessage());
        }

        return user;
    }

    public void parseTranscriptXML()
    {
        logger.info("parseTranscriptXML(): Transcript sync started");

        try
        {
            DocumentBuilderFactory dbFactory = DocumentBuilderFactory.newInstance();
            DocumentBuilder dBuilder = dbFactory.newDocumentBuilder();

            InputStream inputStream = new FileInputStream(xmlTranscriptList);
            Reader reader = new InputStreamReader(inputStream, "ISO-8859-1");
            String readerString = IOUtils.toString(reader);
            readerString = readerString.replace("&", "&amp;");
            reader = new StringReader(readerString);
            InputSource is = new InputSource(reader);

            is.setEncoding("ISO-8859-1");

            doc = dBuilder.parse(is);

            doc.getDocumentElement().normalize();

            NodeList nList = doc.getElementsByTagName("StudentCourses");
            Map<String, List<TranscriptEntry>> studentTranscripts = new LinkedHashMap<String, List<TranscriptEntry>>();
            for (int temp = 0; temp < nList.getLength(); temp++)
            {
                Node nNode = nList.item(temp);

                if (nNode.getNodeType() == Node.ELEMENT_NODE)
                {
                    Element eElement = (Element) nNode;
                    String username = eElement.getElementsByTagName("ID").item(0)
                            .getTextContent();

                    logger.debug("parseTranscriptXML(): Building map for student "
                            + username);

                    TranscriptEntry entry = new TranscriptEntry();
                    entry.setTerm(eElement.getElementsByTagName("TERM").item(0)
                            .getTextContent());
                    entry.setCourseCode(eElement.getElementsByTagName("COURSE")
                            .item(0).getTextContent());
                    entry.setCourseName(
                            eElement.getElementsByTagName("COURSE_TITLE").item(0)
                                    .getTextContent());
                    entry.setCourseGrade(eElement.getElementsByTagName("GRADE")
                            .item(0).getTextContent());
                    entry.setCrn(eElement.getElementsByTagName("CRN").item(0)
                            .getTextContent());
                    entry.setCredits(eElement.getElementsByTagName("CREDITS")
                            .item(0).getTextContent());

                    if (null == studentTranscripts.get(username))
                    {
                        List<TranscriptEntry> transcriptEntries = new LinkedList<TranscriptEntry>();
                        transcriptEntries.add(entry);
                        studentTranscripts.put(username, transcriptEntries);
                    }
                    else
                    {
                        studentTranscripts.get(username).add(entry);
                    }

                }
            }
            Iterator it = studentTranscripts.entrySet().iterator();
            while (it.hasNext())
            {
                Map.Entry pairs = (Map.Entry) it.next();

                ByteArrayOutputStream output = new ByteArrayOutputStream();
                it.remove(); // avoids a ConcurrentModificationException
                String reportXMLPath = JasperController.getSiteReportPath()
                        + TRANSCRIPT_JRXML_FILENAME;
                InputStream input = new FileInputStream(new File(reportXMLPath));
                JasperDesign design = JRXmlLoader.load(input);
                JasperReport report = JasperCompileManager.compileReport(design);

                UserDetailsImpl student = UserDetailsHelper
                        .getUserByUsername((String) pairs.getKey());

                if (student != null)
                {
                    logger.debug("parseTranscriptXML(): Student "
                            + student.getUsername() + " exists");

                    Map parameters = new HashMap();
                    parameters.put("studentName", student.getFullName());
                    parameters.put("studentId", student.getUsername());
                    parameters.put("programOfStudy", student.getMajor1Code());
                    parameters.put("totalGpa", student.getS25());
                    parameters.put("totalCredits", student.getS26());
                    parameters.put("programGpa", student.getS23());
                    parameters.put("programCredits", student.getS24());
                    parameters.put("transcriptDate", DateUtils.formatDate(
                            new Date(), "dd-MM-yyyy", Locale.CANADA_FRENCH));
                    parameters.put("reportPath",
                            JasperController.getSiteReportPath());

                    JasperPrint jasperPrint = JasperFillManager.fillReport(report,
                            parameters, new JRBeanCollectionDataSource(
                                    (List) pairs.getValue()));

                    JasperExportManager.exportReportToPdfStream(jasperPrint,
                            output);

                    File dir = new File(PortalUtils.getServletContext()
                            .getRealPath(TRANSCRIPT_FOLDER));
                    dir.mkdirs();

                    String transcriptFileName = "TX" + student.getUsername()
                            + ".pdf";
                    String transcriptUrl = TRANSCRIPT_FOLDER + transcriptFileName;
                    String transcriptFullPath = PortalUtils.getServletContext()
                            .getRealPath(transcriptUrl);

                    // Write Transcript PDF to disk...
                    output.writeTo(new FileOutputStream(transcriptFullPath));

                    updateStudentTranscriptDocs(student, transcriptFileName,
                            transcriptUrl);
                }
                else
                {
                    logger.debug("parseTranscriptXML(): Student "
                            + (String) pairs.getKey() + " does not exist");
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        logger.info("parseTranscriptXML(): Transcript sync complete");
    }

    public void updateStudentTranscriptDocs(UserDetailsImpl student,
            String transcriptFileName, String transcriptUrl)
    {
        DocModule docModule = (DocModule) PortalUtils.getHt().get(DocModule.class,
                1);
        if (docModule != null)
        {
            DocType2 transcriptDocType = getTranscriptDocType(docModule);
            DocOwner docOwner = DocHelper.getOwner(student, docModule);

            List<Doc> docs = ht.find("from Doc d where d.docType=? and d.owner=?",
                    new Object[] { transcriptDocType, docOwner });

            Doc doc = null;

            if (docs.isEmpty())
            {
                logger.info(
                        "updateStudentTranscriptDocs(): Creating transcript for student "
                                + student.getUsername());

                // Create new transcript document...
                doc = new Doc();
                doc.setDocType(transcriptDocType);
                doc.setPackage(false);
                doc.setOwner(docOwner);
                doc.setApprovalStatus(Doc.APPROVAL_STATUS_APPROVED);
                doc.setFileStatus(Doc.FILE_STATUS_READY);
                doc.setName("Relevé de notes UdeM");
                doc.setDescription("Relevé de notes UdeM");
                doc.setFolder("transcripts");
            }
            else
            {
                logger.info(
                        "updateStudentTranscriptDocs(): Updating transcript for student "
                                + student.getUsername());

                doc = docs.get(0);

                String path = doc.getUrl().getExistingFilePath();

                // delete the previous transcript PDF (if necessary)...
                if (path != null && !transcriptUrl.equals(path))
                {
                    doc.getUrl().deleteWithFile();
                }
            }

            FilePath fp = FilePathUtils.getOrCreateFilePath(transcriptUrl);

            doc.setFile(transcriptFileName);
            doc.setPdfUrl(fp);
            doc.setUrl(fp);
            doc.setCreated(new Date());

            PortalUtils.getHt().saveOrUpdate(doc);
        }
        else
        {
            logger.info("updateStudentTranscriptDocs(): Doc Module Not Found! ");
        }
    }

    private DocType2 getTranscriptDocType(DocModule docModule)
    {
        List<DocType2> dts = PortalUtils.getHt().find(
                "from DocType2 dt where dt.docHandlerId=? and dt.module=?",
                new Object[] { DocType2.HANDLER_TRANSCRIPT, docModule });

        DocType2 dt = null;

        if (dts.isEmpty())
        {
            dt = new DocType2();
            dt.setModule(docModule);
            dt.setDocHandlerId(DocType2.HANDLER_TRANSCRIPT);
            dt.setName("Student Transcript");
            dt.setL2Name("Relevé de notes");
            PortalUtils.getHt().save(dt);
        }
        else
        {
            dt = dts.get(0);
        }

        return dt;
    }

    void loadTranscriptXML()
    {
        xmlTranscriptList = new File(PortalUtils.getServletContext()
                .getRealPath(IMPORTED_XML_FOLDER + transcriptListFilename));
    }

    void deleteTrancriptXML()
    {
        xmlTranscriptList.delete();
    }

    @Override
    public String getUserProfileImage(UserDetailsImpl user,
            HttpServletRequest request)
    {
        // TODO Auto-generated method stub
        return null;
    }
}
