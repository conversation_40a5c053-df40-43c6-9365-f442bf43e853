package com.orbis.web.content.clients.purdue;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.orm.hibernate.HibernateTemplate;
import org.springframework.web.servlet.ModelAndView;

import com.google.common.collect.Lists;
import com.orbis.acegi.providers.dao.hibernate.PersonGroupHelper;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;
import com.orbis.portal.PortalUtils;
import com.orbis.security.AuthenticationUtils;
import com.orbis.utils.CollectionUtils;
import com.orbis.utils.RequestUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.OrbisController;
import com.orbis.web.content.acrm.AcrmHelper;
import com.orbis.web.content.acrm.AcrmRegModuleTagCategory;
import com.orbis.web.content.acrm.AcrmRegistrationController;
import com.orbis.web.content.acrm.AcrmRegistrationModule;
import com.orbis.web.content.acrm.AcrmTag;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.coop.CoopModule;
import com.orbis.web.content.coop.CoopProgram;
import com.orbis.web.content.coop.CoopProgramEligibilityTag;
import com.orbis.web.content.coop.CoopStatus;
import com.orbis.web.content.coop.CoopTerm;
import com.orbis.web.content.coop.CoopTermProgram;
import com.orbis.web.content.integration.CustomIntegrationController;
import com.orbis.web.content.integration.IntegrationTable1;
import com.orbis.web.content.integration.IntegrationTable4;
import com.orbis.web.content.integration.IntegrationTable5;
import com.orbis.web.content.interaction.InteractionHelper;
import com.orbis.web.content.interaction.InteractionModule;
import com.orbis.web.content.interaction.InteractionRole;
import com.orbis.web.content.interaction.InteractionTeam;
import com.orbis.web.content.interaction.InteractionTeamMember;
import com.orbis.web.content.interaction.InteractionTeamMemberRole;
import com.orbis.web.content.np.NCluster;
import com.orbis.web.content.np.NPostingModule;
import com.orbis.web.content.np.NProgram;
import com.orbis.web.content.np.NProgramAcrmTag;
import com.orbis.web.content.np.NTerm;
import com.orbis.web.content.np.NTermClusterProgram;
import com.orbis.web.content.portal.DeletionNodeHelper;

import net.sf.acegisecurity.AuthenticationManager;

public class PurdueController extends OrbisController
        implements CustomIntegrationController
{
    private PurdueDAO registrarDAO;

    public static final String E_5_SESSION = "5-Session";

    public static final String E_3_SESSION = "3-Session";

    public static final String E_IIP = "IIP";

    public static final String E_IP = "IP";

    public static final String E_GEARE = "GEARE";

    public static final String E_FLEX = "Flex Co-op";

    private String service = "https://devopportunity.opp.purdue.edu/purdueLogin.htm?action=login";

    public void setService(String service)
    {
        this.service = service;
    }

    public void setAuthenticationManager(
            AuthenticationManager authenticationManager)
    {
    }

    public void setRegistrarDAO(PurdueDAO registrarDAO)
    {
        this.registrarDAO = registrarDAO;
    }

    @Override
    public ModelAndView displayHome(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(RequestUtils.getRedirectView(request));
        return mv;
    }

    public ModelAndView runScheduledTasks(HttpServletRequest request,
            HttpServletResponse response)
    {
        if ("syncAdmissions".equals(request.getParameter("option")))
        {
            synchAdmissions(request, true);
        }
        else if ("syncStudent".equals(request.getParameter("option")))
        {
            synchStudents(request, request.getParameter("userId"));
        }
        else
        {
            runScheduledTasks();
        }

        return displayHome(request, response);
    }

    public void runScheduledTasks()
    {
        AcrmHelper.createPersonGroupIfNonExistent(
                PersonGroupHelper.DATABASE_UNVERIFIED_STUDENT_ACCOUNT, null);
        AcrmHelper.createPersonGroupIfNonExistent(
                PurdueAccountVerifierController.COOP_APPLIED, null);

        createTerms();

        createUserTags();

        createPrograms();

        synchStaff();

        synchStudents(null, null);
    }

    public ModelAndView synchAdmissions(HttpServletRequest request,
            HttpServletResponse response)
    {
        synchAdmissions(null, false);
        return displayHome(request, response);
    }

    public ModelAndView login(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = null;

        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-store");
        response.setDateHeader("Expires", -1);

        String ticket = request.getParameter("ticket");

        PurdueAuth auth = new PurdueAuth();
        boolean authSuccess = auth.authenticate(ticket, service);
        if (authSuccess)
        {
            UserDetailsImpl au = UserDetailsHelper
                    .getUserByUsername(auth.getUcid());

            if (au == null)
            {
                au = registrarDAO.createUser(auth.getUcid(), auth.getFirstName(),
                        auth.getLastName(), auth.getEmail(),
                        PersonGroupHelper.PORTAL_USER_GROUP);
            }

            String clearPwd = UserDetailsHelper.makePassword(au.getUsername(),
                    PortalUtils.getPasswordEncoder());
            au.setCleartextPassword(clearPwd);
            String encodedPwd = PortalUtils.getPasswordEncoder()
                    .encodePassword(clearPwd, null);
            if (!encodedPwd.equals(au.getPassword()))
            {
                au.setPassword(encodedPwd);
                PortalUtils.getHt().update(au);
            }
            mv = new ModelAndView(RequestUtils.getRedirectView(request));
            AuthenticationUtils.setUserLoggedIn(request, au, response);
        }
        else
        {
            System.out.println("no success " + ticket);
        }

        return mv;
    }

    private void createTerms()
    {
        logger.info("Purdue - Creating Terms.");

        HibernateTemplate ht = PortalUtils.getHt();

        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());

        List<CoopModule> coopModules = ht.loadAll(CoopModule.class);
        for (CoopModule cm : coopModules)
        {
            int seq = 1;
            for (int i = cal.get(Calendar.YEAR) - 35; i <= cal.get(Calendar.YEAR)
                    + 35; i++)
            {
                CoopTerm spring = createCoopTerm(cm, i + " - Spring",
                        i + " - Printemps", seq++);
                CoopTerm summer = createCoopTerm(cm, i + " - Summer", i + " - été",
                        seq++);
                CoopTerm fall = createCoopTerm(cm, i + " - Fall", i + " - Automne",
                        seq++);

                if (cm.getPostingModule() != null)
                {
                    NTerm nspring = createNTerm(cm.getPostingModule(),
                            spring.getName(), spring.getL2Name(), spring.getSeq());
                    spring.setJobPostingTerm(nspring);
                    PortalUtils.getHt().saveOrUpdate(spring);

                    NTerm nsummer = createNTerm(cm.getPostingModule(),
                            summer.getName(), summer.getL2Name(), summer.getSeq());
                    summer.setJobPostingTerm(nsummer);
                    PortalUtils.getHt().saveOrUpdate(summer);

                    NTerm nfall = createNTerm(cm.getPostingModule(), fall.getName(),
                            fall.getL2Name(), fall.getSeq());
                    fall.setJobPostingTerm(nfall);
                    PortalUtils.getHt().saveOrUpdate(fall);
                }
            }
        }

        List<NPostingModule> npmodules = ht
                .find("from NPostingModule npm where npm.coOpJobModule=0");
        for (NPostingModule npm : npmodules)
        {
            int ordinal = 1;
            for (int i = cal.get(Calendar.YEAR) - 35; i <= cal.get(Calendar.YEAR)
                    + 35; i++)
            {
                createNTerm(npm, i + " - Spring", i + " - Printemps", ordinal++);
                createNTerm(npm, i + " - Summer", i + " - été", ordinal++);
                createNTerm(npm, i + " - Fall", i + " - Automne", ordinal++);
            }
        }

        logger.info("Purdue - Finished Creating Terms.");
    }

    private NTerm createNTerm(NPostingModule postingModule, String title,
            String l2title, int ordinal)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        NTerm t = null;
        List<NTerm> terms = null;

        if (!StringUtils.isEmpty(title))
        {
            terms = ht.find("from NTerm t where t.title=? and t.NPostingModule=?",
                    new Object[] { title, postingModule });
        }
        else
        {
            terms = ht.find("from NTerm t where t.l2title=? and t.NPostingModule=?",
                    new Object[] { StringUtils.quoteReplacement(l2title, "'", "''"),
                            postingModule });

        }
        if (terms.isEmpty())
        {
            t = new NTerm();
            t.setOrdinal(ordinal);
            t.setTitle(title);
            t.setL2Title(l2title);
            t.setNPostingModule(postingModule);
            PortalUtils.getHt().saveOrUpdate(t);
        }
        else
        {
            t = terms.get(0);
        }

        return t;
    }

    private CoopTerm createCoopTerm(CoopModule cm, String name, String l2Name,
            int sequence)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        CoopTerm t = null;
        List<CoopTerm> terms = null;

        if (!StringUtils.isEmpty(name))
        {
            terms = ht.find("from CoopTerm t where t.name=? and t.module=?",
                    new Object[] { name, cm });
        }
        else if (!StringUtils.isEmpty(l2Name))
        {
            terms = ht.find("from CoopTerm t where t.l2Name=? and t.module=?",
                    new Object[] { StringUtils.quoteReplacement(l2Name, "'", "''"),
                            cm });
        }
        if (terms == null || terms.isEmpty())
        {
            t = new CoopTerm();
            t.setSeq(sequence);
            t.setName(name);
            t.setL2Name(l2Name);
            t.setModule(cm);
            ht.saveOrUpdate(t);
        }
        else
        {
            t = terms.get(0);
        }

        addStatuses(t);

        return t;
    }

    private void addStatuses(CoopTerm t)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        List<CoopStatus> statuses = ht.find("from CoopStatus s where s.term=?", t);
        if (statuses.isEmpty())
        {
            CoopStatus first = new CoopStatus();
            first.setFirstStatus(true);
            first.setTerm(t);
            first.setName("Co-op Record Created");
            ht.save(first);

            CoopStatus last = new CoopStatus();
            last.setLastStatus(true);
            last.setTerm(t);
            last.setName("Co-op Record Approved");
            ht.save(last);
        }
    }

    private void synchStaff()
    {
        logger.info("Purdue - Portal Staff Synchronization Started.");

        HibernateTemplate ht = PortalUtils.getHt();

        resetNProgramServiceTeamMembers();

        List<Integer> ids = ht.find("select t.id from IntegrationTable4 t ");

        InteractionModule m = InteractionHelper.getInteractionModule();
        InteractionTeam team = InteractionHelper.getTeam("Coordinators", m);

        Map<Integer, InteractionRole> rolesMap = InteractionHelper
                .getRolesMapKeyedByLevel();
        InteractionRole c1 = rolesMap.get(InteractionRole.ROLE_ONE);
        InteractionRole c2 = rolesMap.get(InteractionRole.ROLE_TWO);
        InteractionRole c3 = rolesMap.get(InteractionRole.ROLE_THREE);
        InteractionRole c4 = rolesMap.get(InteractionRole.ROLE_FOUR);

        for (Integer id : ids)
        {
            try
            {
                IntegrationTable4 t = (IntegrationTable4) getHt()
                        .load(IntegrationTable4.class, id);

                UserDetailsImpl au = UserDetailsHelper.getUserByUsername(t.getT1());

                if (au == null)
                {
                    au = registrarDAO.createUser(t.getT1(), t.getT2(), t.getT3(),
                            t.getT4(), PersonGroupHelper.PORTAL_STAFF_GROUP);
                }
                else
                {
                    au.setFirstName(t.getT2());
                    au.setLastName(t.getT3());
                    au.setEmailAddress(t.getT4());
                }
                au.setS35(t.getT5());
                PortalUtils.getHt().update(au);

                InteractionTeamMember itm = InteractionHelper.getTeamMember(team,
                        au);
                if (itm == null)
                {
                    itm = new InteractionTeamMember();
                    itm.setTeam(team);
                    itm.setMember(au);
                    PortalUtils.getHt().save(itm);
                }

                processInteractionTeamMemberRole(c1, itm);
                processInteractionTeamMemberRole(c2, itm);
                processInteractionTeamMemberRole(c3, itm);
                processInteractionTeamMemberRole(c4, itm);

                processProgramServiceTeamMember(team, au, t.getT5());
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }

        logger.info("Purdue - Portal Staff Synchronization Finished.");

    }

    private void resetNProgramServiceTeamMembers()
    {
        getJt().execute("delete from interaction_np_program_stmm");
    }

    private void processProgramServiceTeamMember(InteractionTeam team,
            UserDetailsImpl au, String t5)
    {
        InteractionRole role = InteractionHelper.getRole("Coordinator");
        InteractionTeamMemberRole tmr = InteractionHelper
                .getTeamMemberRole(InteractionHelper.getTeamMember(team, au), role);

        if (tmr != null && role.isOnJob())
        {
            try
            {
                List<Integer> programIds = getHt().find(
                        "select p.id from NProgram p where p.title like ?",
                        "%-" + t5);
                for (Integer pid : programIds)
                {
                    if ((Integer) getHt().find(
                            "select count(stm) from InteractionNProgramServiceTeamMember stm where stm.program=? and stm.teamMemberRole=?",
                            new Object[] { pid, tmr }).get(0) == 0)
                    {
                        getJt().execute(
                                "insert into interaction_np_program_stmm (program, teamMemberRole) values ("
                                        + pid + ", " + tmr.getId() + ")");
                    }
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
    }

    private InteractionTeamMemberRole processInteractionTeamMemberRole(
            InteractionRole c1, InteractionTeamMember itm)
    {
        InteractionTeamMemberRole r1 = InteractionHelper.getTeamMemberRole(itm, c1);
        if (r1 == null)
        {
            r1 = new InteractionTeamMemberRole();
            r1.setTeamMember(itm);
            r1.setRole(c1);
            PortalUtils.getHt().save(r1);
        }
        return r1;
    }

    private void synchStudents(HttpServletRequest request, String userId)
    {
        logger.info("Purdue - Student Synchronization Started.");

        String singleRunHql = Optional.ofNullable(userId)
                .map(s -> " and t.t2='".concat(s).concat("' ")).orElse("");

        String t99Clause = StringUtils.isEmpty(singleRunHql) && "1".equals(
                PortalConfigHelper.getPortalConfig(PortalConfig.ORBIS_CLIENT_3)
                        .getOrbisValue()) ? " and t.t99='U' " : "";

        HibernateTemplate ht = PortalUtils.getHt();
        List<Integer> ids = ht
                .find("select t.id from IntegrationTable1 t where t.id > 0 "
                        + t99Clause + singleRunHql + " order by t.id");

        for (Integer id : ids)
        {
            try
            {
                IntegrationTable1 t = (IntegrationTable1) getHt()
                        .load(IntegrationTable1.class, id);

                PurdueUser nu = (PurdueUser) registrarDAO
                        .getUserByUserId(t.getT2());
                registrarDAO.synchronizeUserData(nu, request);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }

        getJt().execute("UPDATE u SET u.major2code = t.l1Name FROM user_details u"
                + " INNER JOIN tag_assign aut ON aut.assignType = 'com.orbis.web.content.acrm.AcrmUserTag'"
                + " AND aut.uzer = u.USER_DETAILS_ID"
                + " INNER JOIN tag_tag t ON aut.tag = t.id"
                + " INNER JOIN tag_category tc ON tc.categoryType='com.orbis.web.content.acrm.AcrmRegModuleAcademicTagCategory' and tc.id = t.category"
                + " WHERE u.major1Code = 'First Year Engineering' AND tc.l1Name = '5-Session'");

        logger.info("Purdue - Student Synchronization Finished.");
    }

    // private void updateInactiveStudentStatuses()
    // {
    // getJt().execute("update u set u.enabled=0, u.userStatus='"
    // + UserDetailsImpl.USER_STATUS_INACTIVE + "', u.s24='"
    // + UserDetailsImpl.USER_STATUS_INACTIVE
    // + "' from user_details u inner join user_details_groups ug on
    // ug.userDetailsId=u.USER_DETAILS_ID"
    // + " inner join user_group g on ug.userGroupId=g.id and g.name='"
    // + PersonGroupHelper.STUDENT
    // + "' left join integration_t1 t on '0'+t.t2 = u.username "
    // + " where '0'+t.t2 is null");
    // }

    private void createUserTags()
    {
        AcrmRegistrationModule module = AcrmRegistrationController
                .getRegistrationModule(PersonGroupHelper.STUDENT_GROUP);
        AcrmRegModuleTagCategory c = AcrmHelper.getAcrmRegModuleTagCategory(module,
                "Eligibility");

        AcrmHelper.getAcrmTag(c, E_5_SESSION);
        AcrmHelper.getAcrmTag(c, E_3_SESSION);
        AcrmHelper.getAcrmTag(c, E_IP);
        AcrmHelper.getAcrmTag(c, E_IIP);
        AcrmHelper.getAcrmTag(c, E_GEARE);
        AcrmHelper.getAcrmTag(c, E_FLEX);
    }

    private void removeDeprecatedPrograms()
    {
        try
        {
            HibernateTemplate ht = getHt();

            List<Integer> programIds = CollectionUtils.pluck(getJt().queryForList(
                    "select p.id from coop_program p where p.id not in "
                            + " (select distinct cp.id from coop_program cp inner join "
                            + " ( select '%'+t.t2+'%' as col from integration_t2 t ) list on cp.name like list.col) "
                            + " or p.id in "
                            + " ( select cp0.id from coop_program cp0 inner join "
                            + " ( select '5-Session-'+t.t2+'%' as col from integration_t2 t where t.t3='N' ) s5 on cp0.name like s5.col ) "
                            + " or p.id in "
                            + " ( select cp1.id from coop_program cp1 inner join "
                            + " ( select '3-Session-'+t.t2+'%' as col from integration_t2 t where t.t4='N' ) s3 on cp1.name like s3.col ) "
                            + " or p.id in "
                            + " ( select cp2.id from coop_program cp2 inner join "
                            + " ( select 'IIP-'+t.t2+'%' as col from integration_t2 t where t.t5='N' ) siip on cp2.name like siip.col ) "
                            + " or p.id in "
                            + " ( select cp3.id from coop_program cp3 inner join "
                            + " ( select 'IP-'+t.t2+'%' as col from integration_t2 t where t.t6='N' ) sip on cp3.name like sip.col ) "
                            + " or p.id in "
                            + " ( select cp4.id from coop_program cp4 inner join "
                            + " ( select 'GEARE-'+t.t2+'%' as col from integration_t2 t where t.t7='N' ) sg on cp4.name like sg.col ) "),
                    "id");

            for (Integer integer : programIds)
            {
                try
                {
                    CoopProgram program = (CoopProgram) getHt()
                            .load(CoopProgram.class, integer);

                    if ((Integer) ht.find(
                            "select count(ca) from CoopAdmission ca where ca.coopTermProgram.program=?",
                            program).get(0) == 0)
                    {
                        List<AcrmTag> tags = ht.find(
                                "from AcrmTag t where t.l1Name=?",
                                program.getName());
                        for (AcrmTag acrmTag : tags)
                        {
                            AcrmHelper.deleteAcrmTag(acrmTag);
                        }

                        DeletionNodeHelper.deleteContentItem(program);
                    }
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    private void createPrograms()
    {
        logger.info("Purdue - Creating Programs.");

        HibernateTemplate ht = PortalUtils.getHt();

        removeDeprecatedPrograms();
        resetNProgramAcrmTags();

        String[] eligibilities = new String[] { E_5_SESSION, E_3_SESSION, E_IP,
                E_IIP, E_GEARE, E_FLEX };

        AcrmRegistrationModule module = AcrmRegistrationController
                .getRegistrationModule(PersonGroupHelper.STUDENT_GROUP);
        AcrmRegModuleTagCategory c = AcrmHelper.getAcrmRegModuleTagCategory(module,
                E_5_SESSION);
        AcrmRegModuleTagCategory c2 = AcrmHelper.getAcrmRegModuleTagCategory(module,
                E_3_SESSION);
        AcrmRegModuleTagCategory c3 = AcrmHelper.getAcrmRegModuleTagCategory(module,
                E_IP);
        AcrmRegModuleTagCategory c4 = AcrmHelper.getAcrmRegModuleTagCategory(module,
                E_IIP);
        AcrmRegModuleTagCategory c5 = AcrmHelper.getAcrmRegModuleTagCategory(module,
                E_GEARE);
        AcrmRegModuleTagCategory c6 = AcrmHelper.getAcrmRegModuleTagCategory(module,
                E_FLEX);

        for (String e : eligibilities)
        {
            List<Object[]> programs = ht
                    .find("select t.t1, t.t2 from IntegrationTable2 t"
                            + getEligibilityFilterClause(e));

            CoopModule cm = getModuleForEligibility(e);

            for (Object[] p : programs)
            {
                try
                {
                    if (!StringUtils.isEmpty((String) p[0])
                            && !StringUtils.isEmpty((String) p[1]))
                    {
                        String name = e + "-" + p[1] + "-" + p[0];
                        String code = name;
                        AcrmTag t = null;
                        if (E_5_SESSION.equals(e))
                        {
                            t = AcrmHelper.getAcrmTag(c, name);
                        }
                        else if (E_3_SESSION.equals(e))
                        {
                            t = AcrmHelper.getAcrmTag(c2, name);
                        }
                        if (E_IP.equals(e))
                        {
                            t = AcrmHelper.getAcrmTag(c3, name);
                        }
                        if (E_IIP.equals(e))
                        {
                            t = AcrmHelper.getAcrmTag(c4, name);
                        }
                        if (E_GEARE.equals(e))
                        {
                            t = AcrmHelper.getAcrmTag(c5, name);
                        }
                        if (E_FLEX.equals(e))
                        {
                            t = AcrmHelper.getAcrmTag(c6, name);
                        }

                        if (cm != null)
                        {
                            CoopProgram cp = createCoopProgram(cm, code, name);

                            addProgramEligibilityTag(cp, t);

                            if (cm.getPostingModule() != null)
                            {
                                NProgram nProgram = createNPostingPrograms(
                                        cm.getPostingModule(), code, name);
                                NProgramAcrmTag npat = new NProgramAcrmTag();
                                npat.setProgram(nProgram);
                                npat.setTag(t);
                                getHt().save(npat);
                            }
                        }
                    }

                }
                catch (Exception ex)
                {
                    ex.printStackTrace();
                }
            }
        }

        logger.info("Purdue - Finished Creating Programs.");
    }

    private void resetNProgramAcrmTags()
    {
        getJt().execute("delete from np_program_acrm_tag");
    }

    private CoopProgramEligibilityTag addProgramEligibilityTag(CoopProgram cp,
            AcrmTag acrmTag)
    {
        CoopProgramEligibilityTag cpt = null;
        List<CoopProgramEligibilityTag> cpts = getHt().find(
                "select from CoopProgramEligibilityTag cpt where cpt.program = ? and cpt.tag = ?",
                new Object[] { cp, acrmTag });
        if (cpts.isEmpty())
        {
            cpt = new CoopProgramEligibilityTag();
            cpt.setProgram(cp);
            cpt.setTag(acrmTag);
            getHt().save(cpt);
        }
        else
        {
            cpt = cpts.get(0);
        }

        return cpt;
    }

    private String getEligibilityFilterClause(String eligibility)
    {
        String f = "";
        if (E_5_SESSION.equals(eligibility))
            f = " where t.t3 = 'Y' ";
        else if (E_3_SESSION.equals(eligibility))
            f = " where t.t4 = 'Y' ";
        else if (E_IIP.equals(eligibility))
            f = " where t.t5 = 'Y' ";
        else if (E_IP.equals(eligibility))
            f = " where t.t6 = 'Y' ";
        else if (E_GEARE.equals(eligibility))
            f = " where t.t7 = 'Y' ";
        else if (E_FLEX.equals(eligibility))
            f = " where t.t9 = 'Y' ";

        return f;
    }

    public static CoopModule getModuleForEligibility(String eligibility)
    {
        CoopModule cm = null;

        try
        {
            List<CoopModule> modules = PortalUtils.getHt()
                    .loadAll(CoopModule.class);
            if (E_GEARE.equals(eligibility))
                cm = modules.get(1);
            else if (E_IP.equals(eligibility) || E_IIP.equals(eligibility))
                cm = modules.get(2);
            else
                cm = modules.get(0);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return cm;
    }

    private CoopProgram createCoopProgram(CoopModule cm, String code, String name)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        CoopProgram p = null;
        name = StringUtils.isEmpty(name) ? code : name;

        List<CoopProgram> programs = ht.find(
                "from CoopProgram cp where cp.module=? and cp.name=? and cp.code=?",
                new Object[] { cm, name, code });
        if (programs.isEmpty() && !StringUtils.isEmpty(code)
                && !StringUtils.isEmpty(name))
        {
            p = new CoopProgram();
            p.setCode(code);
            p.setName(name);
            p.setL2Name(name);
            p.setModule(cm);
            ht.saveOrUpdate(p);
        }
        else
        {
            p = programs.get(0);
            if (p.getCode().equals(p.getName()))
            {
                p.setName(name);
                p.setL2Name(name);
                ht.saveOrUpdate(p);
            }
        }

        List<CoopTerm> terms = ht.find("from CoopTerm ct where ct.module=? and "
                + " not exists (select ctp.id from CoopTermProgram ctp where ctp.term.id=ct.id and ctp.program=?)",
                new Object[] { cm, p });
        for (CoopTerm ct : terms)
        {
            CoopTermProgram ctp = new CoopTermProgram();
            ctp.setProgram(p);
            ctp.setTerm(ct);
            ht.saveOrUpdate(ctp);
        }

        return p;
    }

    private NProgram createNPostingPrograms(NPostingModule npm, String code,
            String name)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        NProgram p = null;
        name = StringUtils.isEmpty(name) ? code : name;

        List<NProgram> programs = ht.find(
                "from NProgram np where np.NPostingModule=? and np.title=? and np.code=?",
                new Object[] { npm, name, code });
        if (programs.isEmpty())
        {
            p = new NProgram();
            p.setCode(code);
            p.setTitle(name);
            p.setL2Title(name);
            p.setNPostingModule(npm);
            ht.saveOrUpdate(p);

            NCluster nc = getNCluster(npm, "All Programs");
            List<NTerm> nterms = ht.find("from NTerm nt where nt.NPostingModule=?",
                    npm);
            for (NTerm nt : nterms)
            {
                NTermClusterProgram ntcp = new NTermClusterProgram();
                ntcp.setCluster(nc);
                ntcp.setProgram(p);
                ntcp.setTerm(nt);
                ht.saveOrUpdate(ntcp);
            }
        }
        else
        {
            p = programs.get(0);
            if (p.getCode().equals(p.getTitle()))
            {
                p.setTitle(name);
                p.setL2Title(name);
                ht.saveOrUpdate(p);
            }
        }

        return p;
    }

    private NCluster getNCluster(NPostingModule npm, String name)
    {
        NCluster nc = null;

        List<NCluster> ncs = PortalUtils.getHt().find(
                "from NCluster nc where nc.NPostingModule=? and nc.title=?",
                new Object[] { npm, name });
        if (ncs.isEmpty())
        {
            nc = new NCluster();
            nc.setNPostingModule(npm);
            nc.setTitle(name);
            nc.setL2Title(name);
            PortalUtils.getHt().saveOrUpdate(nc);
        }
        else
        {
            nc = ncs.get(0);
        }

        return nc;
    }

    private void synchAdmissions(HttpServletRequest request, boolean testRun)
    {
        String additionHql = testRun
                ? " where t.id = (select min(tt.id) from IntegrationTable5 tt)"
                : "";
        List<Integer> t5s = getHt()
                .find("select t.id from IntegrationTable5 t " + additionHql);

        int totalSynced = 0;

        if (!t5s.isEmpty())
        {
            for (Integer integer : t5s)
            {
                try
                {
                    IntegrationTable5 t5 = (IntegrationTable5) getHt()
                            .load(IntegrationTable5.class, integer);

                    UserDetailsImpl student = UserDetailsHelper
                            .getUserByUsername(t5.getT1());

                    if (student == null)
                    {
                        student = UserDetailsHelper.getOrCreateUser(t5.getT1(),
                                t5.getT1());

                        student.setUserStatus(UserDetailsImpl.USER_STATUS_ACTIVE);

                        student.setFirstName(t5.getT3());
                        student.setMiddleName(t5.getT4());
                        student.setLastName(t5.getT2());
                        // student.setGender(registrarDAO.getGender(t5.getT5()));
                        student.setAlternateEmail(t5.getT58());
                        // student.setS23(t5.getT14());
                        // student.setCitizenStatus(t5.getT19());

                        // if (t5.getT20() != null
                        // && StringUtils.isNumber(t5.getT20()))
                        // {
                        // DecimalFormat df = new DecimalFormat();
                        // df.setRoundingMode(RoundingMode.HALF_UP);
                        // df.setMaximumFractionDigits(2);
                        // student.setGpa(Float.parseFloat(df.format(Float
                        // .parseFloat(t5.getT20()))));
                        // }

                        student.setStreet1(t5.getT47());
                        student.setCity(t5.getT48());
                        student.setProvince(t5.getT49());
                        student.setPostalCode(t5.getT50());
                        student.setCountry("USA");
                        student.setPhoneNumber(t5.getT51());
                        student.setEmailAddress(
                                !StringUtils.isEmpty(t5.getT52()) ? t5.getT52()
                                        : "<EMAIL>");
                        student.setAltStreet1(t5.getT53());
                        student.setAltCity(t5.getT54());
                        student.setAltProvince(t5.getT55());
                        student.setAltCountry("USA");
                        student.setAltPostalCode(t5.getT56());
                        student.setPhoneCell(t5.getT57());

                        UserDetailsHelper.updatePrimaryGroup(student,
                                PersonGroupHelper.STUDENT);

                        getHt().saveOrUpdate(student);
                    }

                    student.setS23(t5.getT14());
                    student.setS19(t5.getT45());
                    student.setS22(t5.getT43());
                    student.setS18(t5.getT46());
                    getHt().saveOrUpdate(student);

                    registrarDAO.setupCoop(student, t5, request);

                    totalSynced++;
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
        }

        logger.info("Total Admissions Synced: " + totalSynced);
    }

    @Override
    public boolean runUserSync(UserType userType, String username,
            HttpServletRequest request)
    {
        if (UserType.STUDENT.equals(userType))
        {
            synchStudents(request, username);
        }

        return true;
    }

    @Override
    public List<CustomAction> getCustomActions()
    {
        return Lists.newArrayList(
                CustomAction.of("Attach Missing Organizations to Placement Records",
                        "fixWtrWithNoOrgs"));
    }

    @Override
    public boolean runCustomAction(HttpServletRequest request)
    {
        String action = request.getParameter("subAction");

        if ("fixWtrWithNoOrgs".equals(action))
        {
            return fixWtrWithNoOrgs(request);
        }

        return false;
    }

    private boolean fixWtrWithNoOrgs(HttpServletRequest request)
    {
        PortalUtils.getJt().update(
                "UPDATE wtr set wtr.organization=org.id, wtr.division=c.id FROM coop_wtr_main wtr "
                        + " LEFT JOIN integration_t6 t6 ON t6.t1=wtr.s11 "
                        + " LEFT JOIN organization org ON org.name=t6.t3 "
                        + " LEFT JOIN company c ON c.organization=org.id and c.name=t6.t4");

        return true;
    }

}