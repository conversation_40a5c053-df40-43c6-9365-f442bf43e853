package com.orbis.web.content.clients.udes;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.orm.hibernate.HibernateTemplate;

import com.orbis.acegi.providers.dao.hibernate.PersonGroup;
import com.orbis.acegi.providers.dao.hibernate.PersonGroupHelper;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.CollectionUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.NumberUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.acrm.AcrmHelper;
import com.orbis.web.content.acrm.AcrmProgram;
import com.orbis.web.content.acrm.AcrmRegModuleTagCategory;
import com.orbis.web.content.acrm.AcrmRegistrationController;
import com.orbis.web.content.acrm.AcrmRegistrationModule;
import com.orbis.web.content.acrm.AcrmTag;
import com.orbis.web.content.acrm.AcrmUserTag;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.coop.CoopAdmission;
import com.orbis.web.content.coop.CoopAdmissionHelper;
import com.orbis.web.content.coop.CoopAdmissionSeq;
import com.orbis.web.content.coop.CoopHelper;
import com.orbis.web.content.coop.CoopModule;
import com.orbis.web.content.coop.CoopProgram;
import com.orbis.web.content.coop.CoopStatus;
import com.orbis.web.content.coop.CoopTerm;
import com.orbis.web.content.coop.CoopTermProgram;
import com.orbis.web.content.coop.CoopWtrMain;
import com.orbis.web.content.integration.IntegrationTable1;
import com.orbis.web.content.integration.IntegrationTable2;
import com.orbis.web.content.integration.IntegrationTable3;
import com.orbis.web.content.integration.IntegrationTable5;
import com.orbis.web.content.integration.IntegrationTable6;
import com.orbis.web.content.integration.IntegrationTable7;
import com.orbis.web.content.interaction.InteractionRole;
import com.orbis.web.content.interaction.InteractionSTPostingServiceTeamMember;
import com.orbis.web.content.interaction.InteractionTeamMember;
import com.orbis.web.content.interaction.InteractionTeamMemberRole;
import com.orbis.web.content.np.NHelper;
import com.orbis.web.content.registrar.DefaultRegistrarDAO;
import com.orbis.web.content.registrar.RegistrarUser;

public class UDESDAO extends DefaultRegistrarDAO implements InitializingBean
{
    private final String GRADUATING_TAG_CATEGORY = "Type of student";

    private final String GRADUATING_TAG = "Graduating";

    @Override
    public void afterPropertiesSet() throws Exception
    {
    }

    public UserDetailsImpl createUser(String username, String firstname,
            String lastname, String email, PersonGroup group)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        UserDetailsImpl ret = null;

        try
        {
            ret = (UserDetailsImpl) PortalUtils.getTransactionTemplate()
                    .execute(status -> {
                        UserDetailsImpl u = UserDetailsHelper
                                .getOrCreateUser(username, username);

                        u.setEmailAddress(email);
                        u.setFirstName(firstname);
                        u.setLastName(lastname);

                        UserDetailsHelper.updatePrimaryGroup(u, group);

                        ht.saveOrUpdate(u);

                        return u;
                    });
        }
        catch (Exception e)
        {
            e.printStackTrace();
            ret = null;
        }

        return ret;
    }

    @Override
    public RegistrarUser getUserByUserId(String uid)
    {
        UDESUser ret = new UDESUser();
        ret.getStudentRecords().addAll(PortalUtils.getHt()
                .find("from IntegrationTable1 t where t.t5=?", uid));
        ret.getPortalStaffRecords().addAll(PortalUtils.getHt()
                .find("from IntegrationTable2 t where t.t5=?", uid));
        ret.getAlumniRecords().addAll(PortalUtils.getHt()
                .find("from IntegrationTable3 t where t.t5=?", uid));
        return ret;
    }

    @Override
    public UserDetailsImpl synchronizeUserData(RegistrarUser ru,
            HttpServletRequest request)
    {
        UserDetailsImpl ret = null;

        if (ru != null)
        {
            List portalStaffRecords = processPortalStaffRecords(
                    ((UDESUser) ru).getPortalStaffRecords());
            List studentRecords = processStudentRecords(
                    ((UDESUser) ru).getStudentRecords(),
                    new Locale(PortalUtils.LANG_EN), request);
            List alumniRecords = processAlumniRecords(
                    ((UDESUser) ru).getAlumniRecords());

            List combined = new ArrayList();
            combined.addAll(portalStaffRecords);
            combined.addAll(studentRecords);
            combined.addAll(alumniRecords);
            if (combined.size() > 0)
            {
                List permutations = CollectionUtils.getPermutation(combined);
                for (Iterator iterator = permutations.iterator(); iterator
                        .hasNext();)
                {
                    Object[] row = (Object[]) iterator.next();
                    NHelper.linkUsers(((UserDetailsImpl) row[0]),
                            ((UserDetailsImpl) row[1]), request);
                }

                for (Iterator iterator = combined.iterator(); iterator.hasNext();)
                {
                    UserDetailsImpl au = (UserDetailsImpl) iterator.next();
                    if (au.isEnabled() && UserDetailsImpl.USER_STATUS_ACTIVE
                            .equals(au.getUserStatus()))
                    {
                        ret = au;
                        break;
                    }
                }
            }
        }

        return ret;
    }

    private List processPortalStaffRecords(List portalStaffRecords)
    {
        List ret = new ArrayList();
        for (Iterator iterator = portalStaffRecords.iterator(); iterator.hasNext();)
        {
            IntegrationTable2 t2 = (IntegrationTable2) iterator.next();
            UserDetailsImpl au = processPortalStaff(t2, t2.getT1());
            if (au != null)
            {
                ret.add(au);
            }
        }
        return ret;
    }

    public UserDetailsImpl processPortalStaff(IntegrationTable2 t, String username)
    {
        UserDetailsImpl ret = null;

        try
        {
            ret = (UserDetailsImpl) PortalUtils.getTransactionTemplate()
                    .execute(status -> {
                        UserDetailsImpl u = UserDetailsHelper
                                .getOrCreateUser(username, null);

                        u.setFirstName(t.getT2());
                        u.setLastName(t.getT3());
                        u.setEmailAddress(t.getT4());

                        UserDetailsHelper.updatePrimaryGroup(u,
                                PersonGroupHelper.PORTAL_STAFF);

                        PortalUtils.getHt().saveOrUpdate(u);

                        return u;
                    });
        }
        catch (Exception e)
        {
            e.printStackTrace();
            ret = null;
        }

        return ret;
    }

    private List processAlumniRecords(List alumniRecords)
    {
        List ret = new ArrayList();
        for (Iterator iterator = alumniRecords.iterator(); iterator.hasNext();)
        {
            IntegrationTable3 t3 = (IntegrationTable3) iterator.next();
            UserDetailsImpl au = processAlumni(t3, t3.getT1());
            if (au != null)
            {
                ret.add(au);
            }
        }
        return ret;
    }

    public UserDetailsImpl processAlumni(IntegrationTable3 t, String username)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        UserDetailsImpl ret = (UserDetailsImpl) PortalUtils.getTransactionTemplate()
                .execute(status -> {
                    UserDetailsImpl u = UserDetailsHelper.getOrCreateUser(username,
                            null);

                    u.setFirstName(t.getT2());
                    u.setLastName(t.getT3());
                    u.setEmailAddress(t.getT4());

                    UserDetailsHelper.updatePrimaryGroup(u,
                            PersonGroupHelper.ALUMNI);

                    try
                    {
                        u.setUserStatus(t.getT18());
                        u.setEnabled("Active".equalsIgnoreCase(t.getT18()));
                        u.setApprovalStatus("Active".equalsIgnoreCase(t.getT18())
                                ? UserDetailsImpl.APPROVAL_STATUS_APPROVED
                                : UserDetailsImpl.APPROVAL_STATUS_DECLINED);
                        u.setSalutation(t.getT6());
                        u.setS1(t.getT7());
                        u.setWebsite(t.getT8());
                        u.setGender(getGender(t.getT9()));
                        u.setDob(getDate(t.getT10()));
                        u.setCountry(t.getT11());
                        u.setPhoneCell(t.getT12());
                        u.setStreet1(t.getT13());
                        u.setStreet2(t.getT14());
                        u.setPostalCode(t.getT15());
                        u.setCity(t.getT16());
                        u.setProvince(t.getT17());
                    }
                    catch (Exception e)
                    {
                        e.printStackTrace();
                    }

                    u.setUserLocale(PortalUtils.LANG_FR);
                    u.setPreferredLanguage(PortalUtils.LANG_FR);

                    ht.saveOrUpdate(u);

                    try
                    {
                        List<IntegrationTable7> t7s = ht.find(
                                "from IntegrationTable7 t where t.t5=? order by t.id desc",
                                u.getUsername());
                        if (!t7s.isEmpty())
                        {
                            IntegrationTable7 t7 = t7s.get(0);

                            u.setMajor1Code(t7.getT6());
                            u.setMajor1Descr(t7.getT7());
                            u.setMajor2Code(t7.getT8());
                            u.setMajor2Descr(t7.getT9());
                            u.setYearLevel(t7.getT10());
                            u.setS3(t7.getT11());
                            u.setS8(t7.getT12());

                            ht.saveOrUpdate(u);
                        }
                    }
                    catch (Exception e)
                    {
                        e.printStackTrace();
                    }

                    t.setT99(new Date().toString());

                    ht.update(t);

                    return u;
                });

        return ret;
    }

    private List processStudentRecords(List studentRecords, Locale locale,
            HttpServletRequest request)
    {
        List ret = new ArrayList();
        for (Iterator iterator = studentRecords.iterator(); iterator.hasNext();)
        {
            IntegrationTable1 t1 = (IntegrationTable1) iterator.next();
            UserDetailsImpl au = processStudent(t1, t1.getT5(), locale, request);
            if (au != null)
            {
                ret.add(au);
            }

        }
        return ret;
    }

    public UserDetailsImpl processStudent(IntegrationTable1 t, String username,
            Locale locale, HttpServletRequest request)
    {
        HibernateTemplate ht = PortalUtils.getHt();
        UserDetailsImpl ret = null;

        ret = (UserDetailsImpl) PortalUtils.getTransactionTemplate()
                .execute(status -> {
                    UserDetailsImpl u = UserDetailsHelper.getOrCreateUser(username,
                            null);

                    u.setFirstName(t.getT7());
                    u.setLastName(t.getT8());
                    u.setEmailAddress(t.getT11());

                    UserDetailsHelper.updatePrimaryGroup(u,
                            PersonGroupHelper.STUDENT);

                    try
                    {
                        u.setS27(t.getT1());
                        u.setUserStatus(t.getT2());
                        u.setEnabled("Active".equalsIgnoreCase(t.getT2()));
                        u.setApprovalStatus("Active".equalsIgnoreCase(t.getT2())
                                ? UserDetailsImpl.APPROVAL_STATUS_APPROVED
                                : UserDetailsImpl.APPROVAL_STATUS_DECLINED);
                        u.setSalutation(t.getT6());
                        u.setGender(getGender(t.getT9()));
                        u.setDob(getDate(t.getT10()));
                        u.setPhoneCell(t.getT12());
                        u.setPhoneNumber(t.getT13());
                        u.setOtherPhone(t.getT14());
                        u.setS3(t.getT15());
                        u.setStreet1(t.getT16());
                        u.setStreet2(t.getT17());
                        u.setS1(t.getT18());
                        u.setCity(t.getT19());
                        u.setProvince(t.getT20());
                        u.setPostalCode(t.getT21());
                        u.setCountry(t.getT22());
                        u.setS7(t.getT23());
                        u.setS8(t.getT24());
                        u.setAltStreet1(t.getT25());
                        u.setAltStreet2(t.getT26());
                        u.setS2(t.getT27());
                        u.setAltCity(t.getT28());
                        u.setAltProvince(t.getT29());
                        u.setAltPostalCode(t.getT30());
                        u.setAltCountry(t.getT31());
                        u.setS9(t.getT32());
                        u.setS10(t.getT33());
                        u.setS4(t.getT34());
                        u.setCitizenStatus(t.getT35());
                        u.setWebsite(t.getT36());
                        u.setGradDegree1(getDate(t.getT37()));
                        u.setS5(t.getT38());
                        u.setS6(t.getT39());
                        u.setS11(t.getT40());
                        u.setS16(t.getT41());
                        u.setS17(t.getT43());
                        u.setS18(t.getT44());
                        u.setS21(t.getT45());
                        u.setS22(t.getT46());
                    }
                    catch (Exception e)
                    {
                        e.printStackTrace();
                    }

                    u.setUserLocale(PortalUtils.LANG_FR);
                    u.setPreferredLanguage(PortalUtils.LANG_FR);

                    ht.saveOrUpdate(u);

                    try
                    {
                        AcrmRegistrationModule registrationModule = AcrmRegistrationController
                                .getRegistrationModule(PersonGroupHelper.STUDENT);
                        AcrmRegModuleTagCategory system = AcrmHelper
                                .getAcrmRegModuleTagCategory(registrationModule,
                                        GRADUATING_TAG_CATEGORY);

                        AcrmHelper.removeUserTag(u, system, GRADUATING_TAG);

                        if ("O".equals(t.getT41()))
                        {
                            AcrmTag tag = AcrmHelper.getAcrmTag(system,
                                    GRADUATING_TAG);
                            if (!AcrmHelper.hasTag(u, tag))
                            {
                                AcrmUserTag ut = new AcrmUserTag();
                                ut.setTag(tag);
                                ut.setUser(u);
                                PortalUtils.getHt().save(ut);
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        e.printStackTrace();
                    }

                    try
                    {
                        List<IntegrationTable6> t6s = ht.find(
                                "from IntegrationTable6 t where t.t5=? and t.t21='O' order by t.t2",
                                u.getUsername());
                        if (!t6s.isEmpty())
                        {
                            IntegrationTable6 t6 = t6s.get(0);

                            u.setS12(t6.getT6());
                            u.setS13(t6.getT7());
                            u.setAlternateEmail(t6.getT8());
                            u.setGpa(StringUtils.isNumber(t6.getT9())
                                    ? NumberUtils.round(new Double(t6.getT9()), 2)
                                    : 0d);
                            u.setS28(t6.getT10());
                            u.setS25(t6.getT10());
                            u.setMajor1Code(t6.getT11());
                            u.setMajor2Code(t6.getT12());
                            u.setMajor1Descr(t6.getT13());
                            u.setMajor2Descr(t6.getT14());
                            u.setS14(t6.getT15());
                            u.setS19(t6.getT16());
                            u.setYearLevel(t6.getT17());
                            u.setS20(t6.getT18());
                            u.setGradDegree1(getDate(t6.getT19()));

                            ht.saveOrUpdate(u);
                        }
                    }
                    catch (Exception e)
                    {
                        e.printStackTrace();
                    }

                    synchronizeAdmission(u, locale, request);

                    t.setT99(new Date().toString());

                    ht.update(t);

                    return u;
                });

        return ret;
    }

    private void synchronizeAdmission(UserDetailsImpl user, Locale locale,
            HttpServletRequest request)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        List<IntegrationTable6> t6s = ht
                .find("from IntegrationTable6 t where t.t5=?", user.getUsername());
        if (!t6s.isEmpty())
        {
            CoopModule cm = (CoopModule) ht.find("from CoopModule c").get(0);

            for (IntegrationTable6 t : t6s)
            {
                CoopProgram p = getProgram(t.getT11(), cm);
                List<IntegrationTable5> t5s = ht
                        .find("from IntegrationTable5 t where t.t24=?", t.getT20());

                CoopAdmission admission = UDESDAO
                        .getCoopAdmission(user.getId().intValue(), t.getT20());
                if (p != null && !t5s.isEmpty())
                {
                    IntegrationTable5 t5 = t5s.get(0);

                    if (admission == null)
                    {
                        CoopTerm at = getTerm(t5.getT5(), cm);

                        if (at != null)
                        {
                            CoopStatus lastStatus = CoopHelper.getLastStatus(at);
                            CoopTermProgram ctp = CoopHelper.getCoopTermProgram(at,
                                    p);
                            if (lastStatus != null && ctp != null)
                            {
                                admission = CoopAdmissionHelper.newInstance();
                                admission.setLegacyS1(t.getT20());
                                admission.setStudent(user);
                                admission = CoopAdmissionHelper
                                        .updateWithExpSnapshot(admission,
                                                admission.getStudent()
                                                        .getMajor1Code(),
                                                admission.getStudent()
                                                        .getYearLevel(),
                                                new Date());
                                admission.setCoopTermProgram(ctp);
                                admission.setCurrentStatus(lastStatus);
                                admission.setActive(
                                        "Active".equalsIgnoreCase(t.getT2()));

                                admission.setCoordinator(
                                        ctp.getProgram().getCoordinator());

                                ht.save(admission);

                                if (admission.getCurrentStatus() != null)
                                {
                                    admission.getCurrentStatus()
                                            .setSendEmail(false);
                                    CoopHelper.updateAdmission(admission, null,
                                            null, request);
                                }

                                CoopHelper.createAdmissionLog(user, admission);
                            }
                        }
                    }
                    else
                    {
                        admission.setActive("Active".equalsIgnoreCase(t.getT2()));
                        ht.update(admission);
                    }

                    synchronizeSequences(admission, t5);
                }
            }
        }
    }

    private void synchronizeSequences(CoopAdmission admission, IntegrationTable5 t)
    {
        if (admission != null && t != null)
        {
            HibernateTemplate ht = PortalUtils.getHt();

            List<String> sequenceList = Arrays.asList(new String[] { t.getT6(),
                    t.getT7(), t.getT8(), t.getT9(), t.getT10(), t.getT11(),
                    t.getT12(), t.getT13(), t.getT14(), t.getT15(), t.getT16(),
                    t.getT17(), t.getT18(), t.getT19(), t.getT20(), t.getT21(),
                    t.getT22(), t.getT23() });

            int seq = 0;
            for (String s : sequenceList)
            {
                CoopTerm term = getTerm(s.substring(0, s.indexOf("-")),
                        admission.getCoopTermProgram().getTerm().getModule());
                if (term != null)
                {
                    CoopAdmissionSeq cas = getSequence(admission, term);
                    if (cas == null)
                    {
                        cas = new CoopAdmissionSeq();
                        cas.setAdmission(admission);
                        cas.setActualTerm(term);
                        cas.setAutoRelease(true);
                        cas.setSeq(seq++);
                    }

                    String label = s.substring(s.indexOf("-") + 1, s.length())
                            .replace("T", "W");

                    cas.setTermType(
                            label.contains("S") ? CoopAdmissionSeq.TERM_TYPE_STUDY
                                    : (label.contains("W")
                                            ? CoopAdmissionSeq.TERM_TYPE_WORK
                                            : CoopAdmissionSeq.TERM_TYPE_NA));
                    cas.setTermLabel(!label.contains("OFF")
                            ? new StringBuilder(label).insert(1, "-").toString()
                            : CoopAdmissionSeq.TERM_LABEL_NA);
                    cas.setCustomTermLabel(label.contains("OFF") ? "OFF" : "N/A");
                    ht.saveOrUpdate(cas);
                }
            }
        }
    }

    private CoopAdmissionSeq getSequence(CoopAdmission admission, CoopTerm term)
    {
        CoopAdmissionSeq seq = null;
        List<CoopAdmissionSeq> sequences = PortalUtils.getHt().find(
                "from CoopAdmissionSeq cas where cas.admission=? and cas.actualTerm=?",
                new Object[] { admission, term });
        if (!sequences.isEmpty())
        {
            seq = sequences.get(0);
        }

        return seq;
    }

    private String getGender(String g)
    {
        String gender = "";
        if ("M".equalsIgnoreCase(g))
        {
            gender = "Male";
        }
        else if ("F".equalsIgnoreCase(g))
        {
            gender = "Female";
        }

        return gender;
    }

    private Date getDate(String d)
    {
        Date date = null;
        try
        {
            Date defaultDate = DateUtils.parseDate("01/01/1800", "dd/MM/yyyy",
                    null);
            date = DateUtils.parseDate(d, "dd/MM/yyyy", null);
            date = date != null ? date
                    : DateUtils.parseDate(d, DateUtils.DF_SHORT_DATE_NOSPACES,
                            null);
            if (DateUtils.isBefore(date, defaultDate))
            {
                date = defaultDate;
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return date;
    }

    public static CoopProgram getProgram(String code, CoopModule module)
    {
        CoopProgram program = null;
        List programs = PortalUtils.getHt().find(
                "from CoopProgram p where p.module=? and p.code=?",
                new Object[] { module, code });
        if (!programs.isEmpty())
        {
            program = (CoopProgram) programs.get(0);
        }

        return program;
    }

    public static CoopTerm getTerm(String code, CoopModule module)
    {
        CoopTerm term = null;

        String sessionSuffix = code.endsWith("1") ? " - Hiver"
                : (code.endsWith("2") ? " - Été" : " - Automne");
        code = code.substring(0, code.length() - 1) + sessionSuffix;

        List terms = PortalUtils.getHt().find(
                "from CoopTerm t where t.name=? and t.module=?",
                new Object[] { code, module });
        if (!terms.isEmpty())
        {
            term = (CoopTerm) terms.get(0);
        }

        return term;
    }

    public static String getTermCode(CoopTerm term)
    {
        String termName = term.getName();
        StringBuilder termCode = new StringBuilder(termName.substring(2, 4));
        if (termName.contains("Winter") || termName.contains("Hiver"))
        {
            termCode.insert(0, "1");
        }
        // Accounts for the fact that some terms were created erroneously with a
        // lower case 'é'
        else if (termName.contains("Summer") || termName.contains("Été")
                || termName.contains("été"))
        {
            termCode.insert(0, "2");
        }
        else if (termName.contains("Fall") || termName.contains("Automne"))
        {
            termCode.insert(0, "3");
        }
        return termCode.toString();
    }

    private static CoopAdmission getCoopAdmission(int studentId, String legacyId)
    {
        CoopAdmission ret = null;
        List as = PortalUtils.getHt().find(
                "from CoopAdmission a where a.legacyS1=? and  a.student.id=?",
                new Object[] { legacyId, studentId });
        if (as.size() > 0)
        {
            ret = ((CoopAdmission) as.get(0));
        }
        return ret;
    }

    public static CoopAdmission getAdmissionForImport(UserDetailsImpl user,
            CoopProgram program, String uniqueProgramCode, CoopModule module)
    {
        CoopAdmission ret = null;

        List<CoopAdmission> admissions = PortalUtils.getHt()
                .find("from CoopAdmission a where a.legacyS1=?", uniqueProgramCode);
        if (admissions.size() == 1)
        {
            ret = admissions.get(0);
        }

        if (ret == null)
        {
            admissions = PortalUtils.getHt().find(
                    "from CoopAdmission a where a.coopTermProgram.program=? and a.student=?",
                    new Object[] { program, user });
            if (admissions.size() == 1)
            {
                ret = admissions.get(0);
            }
            else if (admissions.size() > 1)
            {
                String termCode = uniqueProgramCode
                        .substring(uniqueProgramCode.length() - 5);
                CoopTerm admittedTerm = getTerm(termCode, module);
                if (admittedTerm != null)
                {
                    for (CoopAdmission ca : admissions)
                    {
                        if (ca.getCoopTermProgram().getTerm().getId()
                                .intValue() == admittedTerm.getId().intValue())
                        {
                            ret = ca;
                        }
                    }
                }
            }
        }
        return ret;
    }

    public static void addServiceTeamMemberToWtr(String advisorUsername,
            String programCode, int roleLevel, CoopWtrMain wtr)
    {
        List<InteractionTeamMemberRole> tmr = PortalUtils.getHt().find(
                "select tmr from InteractionTeamMemberRole tmr where tmr.teamMember.member.username=? and tmr.role.level=? and tmr.regionalProgram.code=?",
                new Object[] { advisorUsername, roleLevel, programCode });

        if (!tmr.isEmpty())
        {
            InteractionSTPostingServiceTeamMember istpstm = new InteractionSTPostingServiceTeamMember();
            istpstm.setTeamMemberRole(tmr.get(0));
            istpstm.setWtr(wtr);
            PortalUtils.getHt().save(istpstm);
        }
        else
        {
            List<AcrmProgram> program = PortalUtils.getHt().find(
                    "select p from AcrmProgram p where p.code=?", programCode);
            List<InteractionRole> role = PortalUtils.getHt().find(
                    "select r from InteractionRole r where r.level=?", roleLevel);
            List<InteractionTeamMember> member = PortalUtils.getHt().find(
                    "select m from InteractionTeamMember m where m.member.username=?",
                    advisorUsername);
            if (!program.isEmpty() && !role.isEmpty() && !member.isEmpty())
            {
                InteractionTeamMemberRole newTmr = new InteractionTeamMemberRole();
                newTmr.setRegionalProgram(program.get(0));
                newTmr.setRole(role.get(0));
                newTmr.setTeamMember(member.get(0));
                PortalUtils.getHt().save(newTmr);

                InteractionSTPostingServiceTeamMember istpstm = new InteractionSTPostingServiceTeamMember();
                istpstm.setTeamMemberRole(newTmr);
                istpstm.setWtr(wtr);
                PortalUtils.getHt().save(istpstm);
            }
        }
    }
}
