package com.orbis.web.content.clients.neu;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.orm.hibernate.HibernateTemplate;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import com.orbis.acegi.providers.dao.hibernate.PersonGroupHelper;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.security.AuthenticationUtils;
import com.orbis.utils.RequestUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.OrbisController;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.coop.CoopModule;
import com.orbis.web.content.coop.CoopProgram;
import com.orbis.web.content.coop.CoopTerm;
import com.orbis.web.content.coop.CoopTermProgram;
import com.orbis.web.content.integration.IntegrationTable1;
import com.orbis.web.content.integration.IntegrationTable2;
import com.orbis.web.content.integration.IntegrationTable3;
import com.orbis.web.content.np.NCluster;
import com.orbis.web.content.np.NPostingModule;
import com.orbis.web.content.np.NProgram;
import com.orbis.web.content.np.NTerm;
import com.orbis.web.content.np.NTermClusterProgram;

import net.sf.acegisecurity.AuthenticationManager;
import net.sf.acegisecurity.ui.AbstractProcessingFilter;

public class NEUController extends OrbisController
{
    private NEUDAO registrarDAO;

    public void setAuthenticationManager(
            AuthenticationManager authenticationManager)
    {
    }

    public void setRegistrarDAO(NEUDAO registrarDAO)
    {
        this.registrarDAO = registrarDAO;
    }

    @Override
    public ModelAndView displayHome(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(RequestUtils.getRedirectView(request));
        return mv;
    }

    public ModelAndView runScheduledTasks(HttpServletRequest request,
            HttpServletResponse response)
    {
        runScheduledTasks();
        return displayHome(request, response);
    }

    public void runScheduledTasks()
    {
        logger.info("NEU Import Started.");

        createTerms();
        createPrograms();

        synchAlumni(null);
        synchStudents(null);
        synchPortalStaff(null);

        logger.info("NEU Import Finished.");
    }

    private void createPrograms()
    {
        logger.info("NEU - Creating Programs.");

        HibernateTemplate ht = PortalUtils.getHt();

        List<CoopModule> cmodules = ht.loadAll(CoopModule.class);
        List<NPostingModule> pmodules = ht.loadAll(NPostingModule.class);

        List<Object[]> pls = ht.find(
                "select distinct t.t28, t.t30, t.t31, t.t29 from IntegrationTable1 t where t.t99 is not null");
        for (Object[] p : pls)
        {
            try
            {
                String code = p[0].toString().contains("BSBA")
                        || p[0].toString().contains("BSIB") ? p[0].toString()
                                : p[1].toString();
                String name = (p[0].toString().contains("BSBA")
                        || p[0].toString().contains("BSIB")) && p[3] != null
                                ? p[3].toString()
                                : p[2].toString();

                for (CoopModule cm : cmodules)
                {
                    createCoopProgram(cm, code, name);
                }

                for (NPostingModule npm : pmodules)
                {
                    createNPostingPrograms(npm, code, name);
                }
            }
            catch (Exception e)
            {
            }
        }

        logger.info("NEU - Finished Creating Programs.");
    }

    private NCluster getNCluster(NPostingModule npm, String name)
    {
        NCluster nc = null;

        List<NCluster> ncs = PortalUtils.getHt().find(
                "from NCluster nc where nc.NPostingModule=? and nc.title=?",
                new Object[] { npm, name });
        if (ncs.isEmpty())
        {
            nc = new NCluster();
            nc.setNPostingModule(npm);
            nc.setTitle(name);
            nc.setL2Title(name);
            PortalUtils.getHt().saveOrUpdate(nc);
        }
        else
        {
            nc = ncs.get(0);
        }

        return nc;
    }

    private NProgram createNPostingPrograms(NPostingModule npm, String code,
            String name)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        NProgram p = null;
        name = StringUtils.isEmpty(name) ? code : name;

        List<NProgram> programs = ht.find(
                "from NProgram np where np.NPostingModule=? and np.title=? and np.code=?",
                new Object[] { npm, name, code });
        if (programs.isEmpty())
        {
            p = new NProgram();
            p.setCode(code);
            p.setTitle(name);
            p.setL2Title(name);
            p.setNPostingModule(npm);
            ht.saveOrUpdate(p);

            NCluster nc = getNCluster(npm, "All Programs");
            List<NTerm> nterms = ht.find("from NTerm nt where nt.NPostingModule=?",
                    npm);
            for (NTerm nt : nterms)
            {
                NTermClusterProgram ntcp = new NTermClusterProgram();
                ntcp.setCluster(nc);
                ntcp.setProgram(p);
                ntcp.setTerm(nt);
                ht.saveOrUpdate(ntcp);
            }
        }
        else
        {
            p = programs.get(0);
            if (p.getCode().equals(p.getTitle()))
            {
                p.setTitle(name);
                p.setL2Title(name);
                ht.saveOrUpdate(p);
            }
        }

        return p;
    }

    private CoopProgram createCoopProgram(CoopModule cm, String code, String name)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        CoopProgram p = null;
        name = StringUtils.isEmpty(name) ? code : name;

        List<CoopProgram> programs = ht.find(
                "from CoopProgram cp where cp.module=? and cp.name=? and cp.code=?",
                new Object[] { cm, name, code });
        if (programs.isEmpty())
        {
            p = new CoopProgram();
            p.setCode(code);
            p.setName(name);
            p.setL2Name(name);
            p.setModule(cm);
            ht.saveOrUpdate(p);

            List<CoopTerm> terms = ht.find("from CoopTerm ct where ct.module=?",
                    cm);
            for (CoopTerm ct : terms)
            {
                CoopTermProgram ctp = new CoopTermProgram();
                ctp.setProgram(p);
                ctp.setTerm(ct);
                ht.saveOrUpdate(ctp);
            }
        }
        else
        {
            p = programs.get(0);
            if (p.getCode().equals(p.getName()))
            {
                p.setName(name);
                p.setL2Name(name);
                ht.saveOrUpdate(p);
            }
        }

        return p;
    }

    private void createTerms()
    {
        logger.info("NEU - Creating Terms.");

        HibernateTemplate ht = PortalUtils.getHt();

        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());

        List<CoopModule> coopModules = ht.loadAll(CoopModule.class);
        for (CoopModule cm : coopModules)
        {
            int seq = 1;
            for (int i = cal.get(Calendar.YEAR) - 15; i <= cal.get(Calendar.YEAR)
                    + 15; i++)
            {
                CoopTerm spring = createCoopTerm(cm, i + " - Spring",
                        i + " - Printemps", seq++);
                CoopTerm summer = createCoopTerm(cm, i + " - Summer", i + " - été",
                        seq++);
                CoopTerm fall = createCoopTerm(cm, i + " - Fall", i + " - Automne",
                        seq++);

                if (cm.getPostingModule() != null)
                {
                    NTerm nspring = createNTerm(cm.getPostingModule(),
                            spring.getName(), spring.getL2Name(), spring.getSeq());
                    spring.setJobPostingTerm(nspring);
                    PortalUtils.getHt().saveOrUpdate(spring);

                    NTerm nsummer = createNTerm(cm.getPostingModule(),
                            summer.getName(), summer.getL2Name(), summer.getSeq());
                    summer.setJobPostingTerm(nsummer);
                    PortalUtils.getHt().saveOrUpdate(summer);

                    NTerm nfall = createNTerm(cm.getPostingModule(), fall.getName(),
                            fall.getL2Name(), fall.getSeq());
                    fall.setJobPostingTerm(nfall);
                    PortalUtils.getHt().saveOrUpdate(fall);
                }
            }
        }

        List<NPostingModule> npmodules = ht
                .find("from NPostingModule npm where npm.coOpJobModule=0");
        for (NPostingModule npm : npmodules)
        {
            int ordinal = 1;
            for (int i = cal.get(Calendar.YEAR) - 15; i <= cal.get(Calendar.YEAR)
                    + 15; i++)
            {
                createNTerm(npm, i + " - Spring", i + " - Printemps", ordinal++);
                createNTerm(npm, i + " - Summer", i + " - été", ordinal++);
                createNTerm(npm, i + " - Fall", i + " - Automne", ordinal++);
            }
        }

        logger.info("NEU - Finished Creating Terms.");
    }

    private NTerm createNTerm(NPostingModule postingModule, String title,
            String l2title, int ordinal)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        NTerm t = null;
        List<NTerm> terms = null;

        if (!StringUtils.isEmpty(title))
        {
            terms = ht.find("from NTerm t where t.title=? and t.NPostingModule=?",
                    new Object[] { title, postingModule });
        }
        else
        {
            terms = ht.find("from NTerm t where t.l2title=? and t.NPostingModule=?",
                    new Object[] { StringUtils.quoteReplacement(l2title, "'", "''"),
                            postingModule });

        }
        if (terms.isEmpty())
        {
            t = new NTerm();
            t.setOrdinal(ordinal);
            t.setTitle(title);
            t.setL2Title(l2title);
            t.setNPostingModule(postingModule);
            PortalUtils.getHt().saveOrUpdate(t);
        }
        else
        {
            t = terms.get(0);
        }

        return t;
    }

    private CoopTerm createCoopTerm(CoopModule cm, String name, String l2Name,
            int sequence)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        CoopTerm t = null;
        List<CoopTerm> terms = null;

        if (!StringUtils.isEmpty(name))
        {
            terms = ht.find("from CoopTerm t where t.name=? and t.module=?",
                    new Object[] { name, cm });
        }
        else if (!StringUtils.isEmpty(l2Name))
        {
            terms = ht.find("from CoopTerm t where t.l2Name=? and t.module=?",
                    new Object[] { StringUtils.quoteReplacement(l2Name, "'", "''"),
                            cm });
        }
        if (terms != null)
        {
            if (terms.isEmpty())
            {
                t = new CoopTerm();
                t.setSeq(sequence);
                t.setName(name);
                t.setL2Name(l2Name);
                t.setModule(cm);
                ht.saveOrUpdate(t);
            }
            else
            {
                t = terms.get(0);
            }
        }

        return t;
    }

    private void synchAlumni(HttpServletRequest request)
    {
        logger.info("NEU - Alumni Import Started.");

        HibernateTemplate ht = PortalUtils.getHt();

        List<Integer> ids = ht.find(
                "select t.id from IntegrationTable3 t where t.t99 in ('U', 'D', 'N')");
        for (Integer id : ids)
        {
            try
            {
                IntegrationTable3 t = (IntegrationTable3) getHt()
                        .load(IntegrationTable3.class, id);

                NEUUser nu = (NEUUser) registrarDAO.getUserByUserId(t.getT1());
                registrarDAO.synchronizeUserData(nu, request);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }

        logger.info("NEU - Alumni Import Finished.");
    }

    private void synchStudents(HttpServletRequest request)
    {
        logger.info("NEU - Student Import Started.");

        HibernateTemplate ht = PortalUtils.getHt();

        List<Integer> ids = ht.find(
                "select t.id from IntegrationTable1 t where t.t99 in ('U', 'D', 'N')");
        for (Integer id : ids)
        {
            try
            {
                IntegrationTable1 t = (IntegrationTable1) getHt()
                        .load(IntegrationTable1.class, id);

                NEUUser nu = (NEUUser) registrarDAO.getUserByUserId(t.getT1());
                registrarDAO.synchronizeUserData(nu, request);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }

        // deactivate all students that are no longer in integration_t1
        PortalUtils.getJt().execute(
                "update user_details set enabled=0, userStatus='Inactive', approvalStatus=2001 from user_details inner join user_details_groups on user_details_groups.userDetailsId=user_details.USER_DETAILS_ID inner join user_group on user_details_groups.userGroupId=user_group.id and user_group.name='Student' left join integration_t1 on integration_t1.t1=username where integration_t1.t1 is null");

        // deactivate all students whose status is not 'Active' in
        // integration_t1.t21
        PortalUtils.getJt().execute(
                "update user_details set enabled=0, userStatus='Inactive', approvalStatus=2001 from user_details inner join user_details_groups on user_details_groups.userDetailsId=user_details.USER_DETAILS_ID inner join user_group on user_details_groups.userGroupId=user_group.id and user_group.name='Student' inner join integration_t1 on integration_t1.t1=username where integration_t1.t21 <> 'Active'");

        logger.info("NEU - Student Import Finished.");
    }

    private void synchPortalStaff(HttpServletRequest request)
    {
        logger.info("NEU - Portal Staff Import Started.");

        HibernateTemplate ht = PortalUtils.getHt();

        List<Integer> ids = ht.find(
                "select t.id from IntegrationTable2 t where t.t99 in ('U', 'D', 'N')");
        for (Integer id : ids)
        {
            try
            {
                IntegrationTable2 t = (IntegrationTable2) getHt()
                        .load(IntegrationTable2.class, id);

                NEUUser nu = (NEUUser) registrarDAO.getUserByUserId(t.getT1());
                registrarDAO.synchronizeUserData(nu, request);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }

        logger.info("NEU - Portal Staff Import Finished.");
    }

    public ModelAndView login(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        String r = "/myAccount";
        if (request.getSession().getAttribute(
                AbstractProcessingFilter.ACEGI_SECURITY_TARGET_URL_KEY) != null)
        {
            r = (String) request.getSession().getAttribute(
                    AbstractProcessingFilter.ACEGI_SECURITY_TARGET_URL_KEY);
        }
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-store");
        response.setDateHeader("Expires", -1);

        String nuAffiliation = request.getHeader("NEUAffiliation");
        String givenName = request.getHeader("givenName");
        String email = request.getHeader("mail");
        String nuid = request.getHeader("neuEduNUID");
        String lastName = request.getHeader("sn");

        ModelAndView mv = null;

        if (!StringUtils.isEmpty(nuid))
        {
            UserDetailsImpl au = UserDetailsHelper
                    .getUserByUsername(nuid + NEUDAO.PORTAL_STAFF_SUFFIX);
            au = dontAllowInactive(au);
            if (au == null)
            {
                au = UserDetailsHelper.getUserByUsername(nuid);
                au = dontAllowInactive(au);
            }
            if (au == null)
            {
                au = UserDetailsHelper
                        .getUserByUsername(nuid + NEUDAO.ALUMNI_SUFFIX);
                au = dontAllowInactive(au);
            }
            if (au == null)
            {
                au = UserDetailsHelper
                        .getUserByUsername(nuid + NEUDAO.STAFF_FACULTY_SUFFIX);
                au = dontAllowInactive(au);
            }

            if (au == null)
            {
                au = findAndSynchUser(nuid, request);
            }

            if (au == null && "sponsored-staff".equalsIgnoreCase(nuAffiliation)
                    || "sponsored-faculty".equalsIgnoreCase(nuAffiliation))
            {
                au = registrarDAO.createUser(nuid + NEUDAO.STAFF_FACULTY_SUFFIX,
                        givenName, lastName, email,
                        PersonGroupHelper.STAFF_FACULTY_GROUP);
            }

            if (au == null && "alumni".equalsIgnoreCase(nuAffiliation))
            {
                au = registrarDAO.createUser(nuid + NEUDAO.ALUMNI_SUFFIX, givenName,
                        lastName, email, PersonGroupHelper.ALUMNI_GROUP);
            }

            if (au != null)
            {
                String clearPwd = UserDetailsHelper.makePassword(au.getUsername(),
                        PortalUtils.getPasswordEncoder());
                au.setCleartextPassword(clearPwd);
                AuthenticationUtils.setUserLoggedIn(request, au, response);

                mv = new ModelAndView(new RedirectView(
                        !StringUtils.isEmpty(r) ? r : "/myAccount"));
            }
        }

        return mv;
    }

    private UserDetailsImpl dontAllowInactive(UserDetailsImpl au)
    {
        if (au != null && !(au.isEnabled()
                && UserDetailsImpl.USER_STATUS_ACTIVE.equals(au.getUserStatus())))

        {
            au = null;
        }
        return au;
    }

    private UserDetailsImpl findAndSynchUser(String nuid,
            HttpServletRequest request)
    {
        return registrarDAO.synchronizeUserData(registrarDAO.getUserByUserId(nuid),
                request);
    }

}
