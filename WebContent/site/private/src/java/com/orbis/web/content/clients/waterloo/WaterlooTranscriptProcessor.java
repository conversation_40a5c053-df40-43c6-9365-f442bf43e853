package com.orbis.web.content.clients.waterloo;

import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.sql.Connection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceUtils;
import org.springframework.orm.hibernate.HibernateTemplate;

import com.lowagie.text.Document;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.PageSize;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.ColumnText;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfPageEvent;
import com.lowagie.text.pdf.PdfWriter;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.FilePathUtils;
import com.orbis.utils.FileUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.coop.CoopHelper;
import com.orbis.web.content.coop.CoopModule;
import com.orbis.web.content.coop.CoopTerm;
import com.orbis.web.content.doc.Doc;
import com.orbis.web.content.doc.DocModule;
import com.orbis.web.content.doc.DocOwner;
import com.orbis.web.content.doc.DocType2;
import com.orbis.web.content.file.FilePath;
import com.orbis.web.content.np.NHelper;
import com.orbis.web.report.JasperController;

import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.xml.JRXmlLoader;

public class WaterlooTranscriptProcessor
{
    static protected final Log logger = LogFactory
            .getLog(WaterlooTranscriptProcessor.class);

    private static final String TRANSCRIPT_DIRECTORY = "/content/private/transcripts";

    public static void processTranscripts()
    {
        logger.info("Waterloo transcript processing started.");
        JdbcTemplate sqlJdbcTemplate = PortalUtils.getJt();
        String today = DateUtils.formatDate(new Date(),
                DateUtils.DF_SHORT_DATE_NOSPACES, null);
        List<Map> users = sqlJdbcTemplate.queryForList(
                "SELECT DISTINCT integration_t5.t1 from integration_t5 INNER JOIN integration_t4 ON integration_t5.t3 = integration_t4.t2 and integration_t5.t4 = integration_t4.t3 INNER JOIN integration_t3 ON integration_t5.t1 = integration_t3.t1 "
                        + " INNER JOIN integration_t8 ON integration_t5.t1 = integration_t8.t1 "
                        + " where (integration_t5.t99='" + today
                        + "' or integration_t4.t99='" + today
                        + "' or integration_t3.t99='" + today
                        + "' or integration_t8.t99='" + today
                        + "') and integration_t5.t1 in (select username from user_details)");

        for (Map user : users)
        {
            ByteArrayOutputStream pdfBytes = new ByteArrayOutputStream();
            try
            {
                String studentId = (String) user.get("t1");
                logger.info("Processing transcript for " + studentId);
                UserDetailsImpl u = UserDetailsHelper.getUserByUsername(studentId);

                String jrxmlPath = FileUtils
                        .fixFileName(JasperController.getSiteReportPath()
                                + "/WaterlooTranscript.jrxml");

                InputStream input = new BufferedInputStream(
                        new FileInputStream(new File(jrxmlPath)));
                JasperDesign design = JRXmlLoader.load(input);
                JasperReport report = JasperCompileManager.compileReport(design);

                Map parameters = new HashMap();
                parameters.put("username", studentId);
                parameters.put("firstName", u.getFirstName());
                parameters.put("lastName", u.getLastName());
                parameters.put("level",
                        u.getYearLevel() != null ? u.getYearLevel() : "");
                parameters.put("plan", u.getS10() != null ? u.getS10() : "");
                parameters.put("planB", u.getS11() != null ? u.getS11() : "");

                // required for transaction support
                Connection conn = DataSourceUtils
                        .getConnection(sqlJdbcTemplate.getDataSource());

                JasperPrint jasperPrint = JasperFillManager.fillReport(report,
                        parameters, conn);

                DataSourceUtils.closeConnectionIfNecessary(conn,
                        sqlJdbcTemplate.getDataSource());

                String pdfPath = PortalUtils.getRealPath("/") + TRANSCRIPT_DIRECTORY
                        + "/" + studentId + ".pdf";

                JasperExportManager.exportReportToPdfStream(jasperPrint, pdfBytes);

                if (FileUtils.fileExists(pdfPath))
                {
                    FileUtils.deleteFile(pdfPath);
                }

                if (FileUtils.writeFile(pdfPath, pdfBytes.toByteArray()))
                {
                    updateTranscriptDocsDateCreatedIfExists(studentId);
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }

        // STEP 2
        try
        {

            File sourceDir = null;
            sourceDir = new File(PortalUtils.getRealPath(TRANSCRIPT_DIRECTORY));

            if (sourceDir.exists() && sourceDir.isDirectory())
            {
                File[] files = sourceDir.listFiles();
                List txDocTypes = PortalUtils.getHt()
                        .find("from DocType2 dt where dt.docHandlerId=3");
                for (int i = 0; i < files.length; i++)
                {
                    File file = files[i];
                    String fileName = file.getName();
                    if (!fileName.contains("_"))
                    {
                        String docType = (fileName
                                .substring(fileName.length() - 4));
                        if (docType.equalsIgnoreCase(".pdf"))
                        {
                            String username = (file.getName().substring(0,
                                    (file.getName().length() - 4)));

                            String fullPathPdf = file.getCanonicalPath();
                            String correctedTranscriptFolder = FileUtils
                                    .fixFileName(TRANSCRIPT_DIRECTORY);
                            String relativeUrlPdf = fullPathPdf.substring(
                                    fullPathPdf.indexOf(correctedTranscriptFolder));

                            WaterlooTranscriptProcessor.createDoc(relativeUrlPdf,
                                    username, txDocTypes);
                        }
                    }
                }
            }

        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        logger.info("Waterloo transcript processing finished.");
    }

    public static void processWtrHistoryDocs()
    {
        logger.info("Waterloo processWtrHistoryDocs started.");
        List coopModules = PortalUtils.getHt()
                .find("from CoopModule cm order by cm.id");
        CoopModule coopModule = coopModules.size() > 0
                ? (CoopModule) coopModules.get(0)
                : null;
        if (coopModule != null)
        {
            DocModule docModule = coopModule.getPostingModule().getDocModule();
            List docTypes = PortalUtils.getHt().find(
                    "from DocType2 dt where dt.docHandlerId=0 and dt.module=? and dt.name='University of Waterloo Co-op Work History'",
                    docModule);
            if (docTypes.size() > 0)
            {
                CoopTerm cwt = CoopHelper.getCurrentWorkTerm(coopModule);
                try
                {
                    DocType2 dt = (DocType2) docTypes.get(0);
                    List<Object[]> ids = PortalUtils.getHt().find("select distinct "
                            + " admission.student.id, "
                            + " admission.student.username, "
                            + " admission.student.firstName, "
                            + " admission.student.lastName, "
                            + " admission.student.yearLevel, "
                            + " admission.student.s10, "
                            + " admission.student.s26, "
                            + " admission.student.s27, " + " admission.student.s11 "
                            + " from CoopAdmission admission "
                            + " where admission.coopTermProgram.term.module=? "
                            + " and admission.student.userStatus = ? ",
                            new Object[] { coopModule,
                                    UserDetailsImpl.USER_STATUS_ACTIVE });
                    for (Object[] row : ids)
                    {
                        processWtrHistoryDoc(coopModule, cwt,
                                dt, row);
                    }
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
        }
        logger.info("Waterloo processWtrHistoryDocs finished.");
    }

    public static void processWtrHistoryDoc(CoopModule coopModule, CoopTerm cwt,
            DocType2 dt, UserDetailsImpl student)
    {
        processWtrHistoryDoc(coopModule, cwt, dt,
                new Object[] { student.getId(), student.getUsername(),
                        student.getFirstName(), student.getLastName(),
                        student.getYearLevel(), student.getS10(), student.getS26(),
                        student.getS27(), student.getS11() });
    }

    public static void processWtrHistoryDoc(CoopModule coopModule,
            CoopTerm cwt, DocType2 dt, Object[] row)
    {
        Integer id = (Integer) row[0];
        String username = StringUtils
                .replaceSpecialCharacters((String) row[1]);
        String firstName = StringUtils
                .replaceSpecialCharacters((String) row[2]);
        String lastName = StringUtils
                .replaceSpecialCharacters((String) row[3]);
        String yearLevel = StringUtils
                .replaceSpecialCharacters((String) row[4]);
        String plan = StringUtils
                .replaceSpecialCharacters((String) row[5]);
        String advancedCreditForRequiredWTsUG = StringUtils
                .replaceSpecialCharacters((String) row[6]);
        String advancedCreditForRequiredWTsGR = StringUtils
                .replaceSpecialCharacters((String) row[7]);
        String planB = StringUtils
                .replaceSpecialCharacters((String) row[8]);

        Rectangle pageSize = PageSize.LETTER;
        Document document = new Document(pageSize, 72f, 72f, 72f,
                72f);
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        PdfWriter writer = PdfWriter.getInstance(document, baos);
        document.open();

        writer.setPageEvent(new PdfPageEvent()
        {

            @Override
            public void onEndPage(PdfWriter arg0, Document arg1)
            {
                try
                {
                    String disclaimerText = "Disclaimer: This evaluation does not constitute an employment endorsement or recommendation.  Employer evaluations of student contributions and achievements during the work term are conducted as part of the University of Waterloo’s Co-operative (Co-op) Education model.  Like academic grades, overall evaluations are part of the assessment of a student’s progress in the co-op portion of their degree studies.  These assessments are completed using criteria set out by the University, not the employer, and do not reflect the employer’s criteria or assessment metrics.";
                    PdfContentByte cb = arg0.getDirectContent();
                    ColumnText ct = new ColumnText(cb);

                    Paragraph para = new Paragraph(disclaimerText,
                            new Font(Font.HELVETICA, 8, Font.BOLD));
                    ct.setLeading(8, 0);
                    ct.setText(para);
                    ct.setSimpleColumn(document.leftMargin(),
                            arg1.bottom(), document.right(), 0);
                    ct.go();
                }
                catch (Exception e)
                {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }

            }

            @Override
            public void onOpenDocument(PdfWriter writer,
                    Document document)
            {
            }

            @Override
            public void onStartPage(PdfWriter writer,
                    Document document)
            {
            }

            @Override
            public void onCloseDocument(PdfWriter writer,
                    Document document)
            {
            }

            @Override
            public void onParagraph(PdfWriter writer,
                    Document document, float paragraphPosition)
            {
            }

            @Override
            public void onParagraphEnd(PdfWriter writer,
                    Document document, float paragraphPosition)
            {
            }

            @Override
            public void onChapter(PdfWriter writer,
                    Document document, float paragraphPosition,
                    Paragraph title)
            {
            }

            @Override
            public void onChapterEnd(PdfWriter writer,
                    Document document, float paragraphPosition)
            {
            }

            @Override
            public void onSection(PdfWriter writer,
                    Document document, float paragraphPosition,
                    int depth, Paragraph title)
            {
            }

            @Override
            public void onSectionEnd(PdfWriter writer,
                    Document document, float paragraphPosition)
            {
            }

            @Override
            public void onGenericTag(PdfWriter writer,
                    Document document, Rectangle rect, String text)
            {
            }

        });

        // setup Job Info Section
        PdfPTable infoSection = new PdfPTable(1);
        NHelper.applyDefaultTableSettingsOneColumn(infoSection);
        NHelper.addTableTitleOneColumn(infoSection,
                "University of Waterloo");
        NHelper.addTableTitleOneColumn(infoSection,
                "Co-operative Work Terms");
        NHelper.addEmptyCell(infoSection);

        PdfPTable nameSection = new PdfPTable(1);
        NHelper.applyDefaultTableSettingsOneColumn(nameSection);
        addCell(nameSection, (firstName + " " + lastName),
                Element.ALIGN_CENTER, NHelper.SM_BOLD_FONT, 0);
        addCell(nameSection, username, Element.ALIGN_CENTER,
                NHelper.SM_BOLD_FONT, 0);
        addCell(nameSection, (yearLevel + " " + plan),
                Element.ALIGN_CENTER, NHelper.SM_BOLD_FONT, 0);
        if (!StringUtils.isEmpty(planB))
        {
            addCell(nameSection, planB, Element.ALIGN_CENTER,
                    NHelper.SM_BOLD_FONT, 0);
        }
        if (!StringUtils.isEmpty(advancedCreditForRequiredWTsUG)
                && !advancedCreditForRequiredWTsUG
                        .equalsIgnoreCase("n/a"))
        {
            addCell(nameSection, "" + advancedCreditForRequiredWTsUG
                    + " undergrad work term transfers granted.",
                    Element.ALIGN_CENTER, NHelper.SM_NORMAL_FONT,
                    0);
        }
        if (!StringUtils.isEmpty(advancedCreditForRequiredWTsGR)
                && !advancedCreditForRequiredWTsGR
                        .equalsIgnoreCase("n/a"))
        {
            addCell(nameSection, "" + advancedCreditForRequiredWTsGR
                    + " graduate work term transfers granted.",
                    Element.ALIGN_CENTER, NHelper.SM_NORMAL_FONT,
                    0);
        }
        NHelper.addEmptyCell(nameSection);
        NHelper.addEmptyCell(nameSection);

        PdfPTable contentSection = new PdfPTable(3);
        contentSection.setWidths(new int[] { 1, 2, 3 });
        contentSection.setTotalWidth(
                PageSize.LETTER.getWidth() - 72f * 2);
        contentSection.setLockedWidth(true);
        contentSection.getDefaultCell()
                .setBorder(Rectangle.NO_BORDER);
        contentSection.getDefaultCell().setMinimumHeight(15);
        contentSection.setSplitRows(true);
        contentSection.setSplitLate(false);

        List wtr = PortalUtils.getHt().find(
                "select wtr.seq.actualTerm.name, wtr.s11, wtr.s8, wtr.s1, wtr.s2, wtr.id, wtr.seq.customTermLabel, wtr.s6, wtr.s10"
                        + " from CoopWtrMain wtr"
                        + " where wtr.seq.admission.student.id=?"
                        + " and wtr.seq.admission.coopTermProgram.term.module=?"
                        + " and wtr.seq.customTermLabel in ('EI','EJ','ER','ES','EW','EF','EA','F','EP')"
                        + " order by wtr.workTermStartDate",
                new Object[] { id, coopModule });
        if (wtr.size() == 0)
        {
            contentSection = new PdfPTable(1);
            NHelper.applyDefaultTableSettingsOneColumn(
                    contentSection);
            addCell(contentSection,
                    "No Co-op Work Term History available",
                    Element.ALIGN_CENTER, NHelper.SM_NORMAL_FONT,
                    0);
        }
        else
        {
            addCell(contentSection, "Work Term", Element.ALIGN_LEFT,
                    NHelper.SM_BOLD_FONT, 0);
            addCell(contentSection, "Employer", Element.ALIGN_LEFT,
                    NHelper.SM_BOLD_FONT, 0);
            addCell(contentSection, "Evaluation",
                    Element.ALIGN_RIGHT, NHelper.SM_BOLD_FONT, 0);

            for (Iterator iterator2 = wtr.iterator(); iterator2
                    .hasNext();)
            {
                Object[] r = (Object[]) iterator2.next();

                String termName = (String) r[0];
                String jobTitle = StringUtils
                        .replaceSpecialCharacters((String) r[1]);
                String addressPt1 = StringUtils
                        .replaceSpecialCharacters((String) r[2]);
                String organization = StringUtils
                        .replaceSpecialCharacters((String) r[3]);
                String division = StringUtils
                        .replaceSpecialCharacters((String) r[4]);
                Integer wtrId = (Integer) r[5];
                String customTermLabel = (String) r[6];
                String addressPt2 = StringUtils
                        .replaceSpecialCharacters((String) r[7]);
                String addressPt3 = StringUtils
                        .replaceSpecialCharacters((String) r[8]);

                String evalS = "";
                if (!StringUtils.isEmpty(customTermLabel)
                        && customTermLabel.startsWith("E"))
                {
                    List eval = PortalUtils.getHt().find(
                            "select e.s3 from CoopWtrFinalEmployer e where e.mainRecordId=? and e.status = 'Approved'",
                            new Object[] { wtrId });
                    if (eval.size() > 0)
                    {
                        evalS = (String) eval.get(0);
                    }
                }

                String term = getTerm(termName);
                addCell(contentSection, term, Element.ALIGN_LEFT,
                        NHelper.SM_NORMAL_FONT, 0);
                if (!StringUtils.isEmpty(customTermLabel)
                        && customTermLabel.equals("F"))
                {
                    organization = "Failed work term";
                }
                addCell(contentSection, organization,
                        Element.ALIGN_LEFT, NHelper.SM_NORMAL_FONT,
                        0);
                addCell(contentSection, evalS, Element.ALIGN_RIGHT,
                        NHelper.SM_NORMAL_FONT, 0);

                addCell(contentSection, "", Element.ALIGN_LEFT,
                        NHelper.SM_NORMAL_FONT, 0);
                addCell(contentSection,
                        "F".equals(customTermLabel) ? "" : division,
                        Element.ALIGN_LEFT, NHelper.SM_NORMAL_FONT,
                        0);
                addCell(contentSection, "", Element.ALIGN_RIGHT,
                        NHelper.SM_NORMAL_FONT, 0);

                String address = "";
                if (addressPt1 != null)
                {
                    address = addressPt1;
                }
                if (!StringUtils.isEmpty(addressPt2))
                {
                    if (!StringUtils.isEmpty(address))
                    {
                        address += " ";
                    }
                    address += addressPt2;
                }
                if (!StringUtils.isEmpty(addressPt3))
                {
                    if (!StringUtils.isEmpty(address))
                    {
                        address += " ";
                    }
                    address += addressPt3;
                }
                addCell(contentSection, "", Element.ALIGN_LEFT,
                        NHelper.SM_NORMAL_FONT, 0);
                addCell(contentSection,
                        "F".equals(customTermLabel) ? "" : address,
                        Element.ALIGN_LEFT, NHelper.SM_NORMAL_FONT,
                        0);
                addCell(contentSection, "", Element.ALIGN_RIGHT,
                        NHelper.SM_NORMAL_FONT, 0);

                addCell(contentSection, "", Element.ALIGN_LEFT,
                        NHelper.SM_NORMAL_FONT, 0);

                addCell(contentSection,
                        "F".equals(customTermLabel) ? "" : jobTitle,
                        Element.ALIGN_LEFT, NHelper.SM_NORMAL_FONT,
                        0);
                addCell(contentSection, "", Element.ALIGN_RIGHT,
                        NHelper.SM_NORMAL_FONT, 0);

                addCell(contentSection, "", Element.ALIGN_LEFT,
                        NHelper.SM_NORMAL_FONT, 0);
                addCell(contentSection, "", Element.ALIGN_LEFT,
                        NHelper.SM_NORMAL_FONT, 0);
                addCell(contentSection, "", Element.ALIGN_RIGHT,
                        NHelper.SM_NORMAL_FONT, 0);
            }
        }

        document.add(infoSection);
        document.add(nameSection);
        document.add(contentSection);

        if (cwt != null)
        {
            List futures = PortalUtils.getHt().find(
                    "select cas.actualTerm.name from CoopAdmissionSeq cas where cas.admission.student.id=? and cas.actualTerm.seq>? and cas.termType='Work' order by cas.actualTerm.seq",
                    new Object[] { id, cwt.getSeq() });
            if (!futures.isEmpty())
            {
                PdfPTable futureSection = new PdfPTable(1);
                futureSection.setWidths(new int[] { 1 });
                futureSection.setTotalWidth(
                        PageSize.LETTER.getWidth() - 72f * 2);
                futureSection.setLockedWidth(true);
                futureSection.getDefaultCell()
                        .setBorder(Rectangle.NO_BORDER);
                futureSection.getDefaultCell().setMinimumHeight(15);
                futureSection.setSplitRows(true);
                futureSection.setSplitLate(false);

                addCell(futureSection,
                        "Planned Future Work Term(s)",
                        Element.ALIGN_CENTER, NHelper.LG_BOLD_FONT,
                        0);

                for (Iterator iterator2 = futures
                        .iterator(); iterator2.hasNext();)
                {
                    String tName = (String) iterator2.next();
                    addCell(futureSection, getTerm(tName),
                            Element.ALIGN_CENTER,
                            NHelper.SM_NORMAL_FONT, 0);
                }
                document.add(futureSection);
            }

        }

        document.close();

        String pdfPath = PortalUtils.getRealPath("/")
                + TRANSCRIPT_DIRECTORY + "/" + username + "_wtr"
                + ".pdf";
        if (FileUtils.fileExists(pdfPath))
        {
            FileUtils.deleteFile(pdfPath);
        }

        boolean fileWritten = FileUtils.writeFile(pdfPath,
                baos.toByteArray());

        int docOwnerId = WaterlooTranscriptProcessor
                .getCreateOwner(username, coopModule.getPostingModule().getDocModule());
        if (docOwnerId > 0)
        {
            List<Doc> docs = PortalUtils.getHt().find(
                    "from Doc d where d.docType=? and d.owner.id=? and d.description='Waterloo Co-op Work Term Record' order by d.id",
                    new Object[] { dt, docOwnerId });

            if (docs.size() == 0)
            {
                FilePath fp = FilePathUtils
                        .getOrCreateFilePath(TRANSCRIPT_DIRECTORY
                                + "/" + username + "_wtr" + ".pdf");
                Doc ftDoc = new Doc();
                DocOwner docOwner = (DocOwner) PortalUtils.getHt()
                        .load(DocOwner.class, docOwnerId);
                ftDoc.setOwner(docOwner);
                ftDoc.setPackage(false);
                ftDoc.setFile(username + "_wtr" + ".pdf");
                ftDoc.setName(username
                        + "_Waterloo Co-op Work Term Record");
                ftDoc.setDescription(
                        "Waterloo Co-op Work Term Record");
                ftDoc.setDocType(dt);
                ftDoc.setCreated(new Date());
                ftDoc.setUrl(fp);
                ftDoc.setPdfUrl(fp);
                ftDoc.setFileStatus(Doc.FILE_STATUS_READY);

                PortalUtils.getHt().save(ftDoc);
            }
            else if (fileWritten)
            {
                Doc ftDoc = docs.get(0);
                ftDoc.setCreated(new Date());
                ftDoc.setFileStatus(Doc.FILE_STATUS_READY);
                PortalUtils.getHt().update(ftDoc);
            }
        }
    }

    private static String getTerm(String termName)
    {
        String term = "";
        if (termName.indexOf("Winter") > -1)
        {
            term = "Jan - Apr ";
        }
        else if (termName.indexOf("Spring") > -1)
        {
            term = "May - Aug ";
        }
        if (termName.indexOf("Fall") > -1)
        {
            term = "Sep - Dec ";
        }

        term += termName.substring(0, 4);
        return term;
    }

    public static void createDoc(String relativeUrl, String username,
            List txDocTypes)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        for (Iterator iterator = txDocTypes.iterator(); iterator.hasNext();)
        {
            DocType2 transcriptDocType = (DocType2) iterator.next();

            int docOwnerId = WaterlooTranscriptProcessor.getCreateOwner(username,
                    transcriptDocType.getModule());
            if (docOwnerId > 0)
            {

                List docs = ht.find(
                        "select d.id from Doc d where d.docType=? and d.owner.id=? order by d.id",
                        new Object[] { transcriptDocType, docOwnerId });

                if (docs.size() == 0)
                {
                    FilePath fp = FilePathUtils.getOrCreateFilePath(relativeUrl);
                    Doc ftDoc = new Doc();
                    DocOwner docOwner = (DocOwner) PortalUtils.getHt()
                            .load(DocOwner.class, docOwnerId);
                    ftDoc.setOwner(docOwner);
                    ftDoc.setPackage(false);
                    ftDoc.setFile(username + ".pdf");
                    ftDoc.setName(username + "_Waterloo Student Transcript");
                    ftDoc.setDescription("Waterloo Student Transcript");
                    ftDoc.setDocType(transcriptDocType);
                    ftDoc.setCreated(new Date());
                    ftDoc.setUrl(fp);
                    ftDoc.setPdfUrl(fp);
                    ftDoc.setFileStatus(Doc.FILE_STATUS_READY);

                    ht.save(ftDoc);
                }
            }
        }
    }

    private static void updateTranscriptDocsDateCreatedIfExists(String username)
    {
        PortalUtils.getHt().<Doc> findAndStream(
                "from Doc d where d.docType.docHandlerId=? and d.owner.owner.username=?",
                new Object[] { DocType2.HANDLER_TRANSCRIPT, username })
                .forEach(doc -> {
                    doc.setCreated(new Date());
                    doc.setFileStatus(Doc.FILE_STATUS_READY);
                    PortalUtils.getHt().update(doc);
                });
    }

    public static int getCreateOwner(String username, DocModule module)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        List owners = ht.find(
                "select d.id from DocOwner d where d.owner.username=? and d.module=? order by d.id",
                new Object[] { username, module });
        int ret = 0;
        if (owners.size() > 0)
        {
            ret = (Integer) owners.get(0);
        }
        else
        {
            UserDetailsImpl user = UserDetailsHelper.getUserByUsername(username);
            if (user != null)
            {
                DocOwner docOwner = new DocOwner();
                docOwner.setOwner(user);
                docOwner.setModule(module);
                ht.save(docOwner);
                ret = docOwner.getId();
            }
        }
        return ret;
    }

    public static void addCell(PdfPTable table, String content, int alignment,
            Font font, Integer bottomPadding)
    {
        PdfPCell cell = new PdfPCell(table.getDefaultCell());
        cell.setHorizontalAlignment(alignment);
        cell.setPhrase(new Paragraph(content, font));
        if (bottomPadding > 0)
        {
            cell.setPaddingBottom(bottomPadding);
        }
        table.addCell(cell);
    }
}
