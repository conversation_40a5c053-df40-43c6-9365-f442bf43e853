package com.orbis.web.content.clients.udes;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.orm.hibernate.HibernateTemplate;
import org.springframework.web.servlet.ModelAndView;

import com.google.common.collect.Lists;
import com.lowagie.text.Document;
import com.lowagie.text.Element;
import com.lowagie.text.PageSize;
import com.lowagie.text.Rectangle;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfWriter;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.security.AuthenticationUtils;
import com.orbis.utils.DBUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.FilePathUtils;
import com.orbis.utils.FileUtils;
import com.orbis.utils.RequestUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.OrbisController;
import com.orbis.web.content.acrm.AcrmProgram;
import com.orbis.web.content.acrm.AcrmUserProgram;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.clients.waterloo.WaterlooTranscriptProcessor;
import com.orbis.web.content.coop.CoopModule;
import com.orbis.web.content.coop.CoopProgram;
import com.orbis.web.content.coop.CoopStatus;
import com.orbis.web.content.coop.CoopTerm;
import com.orbis.web.content.coop.CoopTermProgram;
import com.orbis.web.content.coop.CoopWtrModelMain;
import com.orbis.web.content.crm.Company;
import com.orbis.web.content.crm.Organization;
import com.orbis.web.content.doc.Doc;
import com.orbis.web.content.doc.DocModule;
import com.orbis.web.content.doc.DocOwner;
import com.orbis.web.content.doc.DocType2;
import com.orbis.web.content.doc.docHandlers.PDFTranscriptProcessor;
import com.orbis.web.content.file.FilePath;
import com.orbis.web.content.integration.IntegrationTable9;
import com.orbis.web.content.interaction.InteractionHelper;
import com.orbis.web.content.interaction.InteractionRole;
import com.orbis.web.content.interaction.InteractionTeamMember;
import com.orbis.web.content.interaction.InteractionTeamMemberRole;
import com.orbis.web.content.np.NCluster;
import com.orbis.web.content.np.NHelper;
import com.orbis.web.content.np.NPostingModule;
import com.orbis.web.content.np.NProgram;
import com.orbis.web.content.np.NTerm;
import com.orbis.web.content.np.NTermClusterProgram;

import net.sf.acegisecurity.AuthenticationManager;
import net.sf.acegisecurity.providers.AuthenticationProvider;
import net.sf.acegisecurity.providers.encoding.PasswordEncoder;

public class UDESController extends OrbisController
{
    public static final String CUSTOM_QUERY_TASK_KEY = "udesCustomQueries";

    private UDESDAO registrarDAO;

    private String service = "https://dev42.orbiscommunications.com/udes.htm?action=login",
            validator = "https://cas-demo.si.sti.usherbrooke.ca/";

    private PasswordEncoder passwordEncoder;

    public void setService(String service)
    {
        this.service = service;
    }

    public void setValidator(String validator)
    {
        this.validator = validator;
    }

    public void setPasswordEncoder(PasswordEncoder passwordEncoder)
    {
        this.passwordEncoder = passwordEncoder;
    }

    public void setAuthenticationManager(
            AuthenticationManager authenticationManager)
    {
    }

    public void setAuthenticationProvider(
            AuthenticationProvider authenticationProvider)
    {
    }

    public void setRegistrarDAO(UDESDAO registrarDAO)
    {
        this.registrarDAO = registrarDAO;
    }

    public ModelAndView displayHome(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(RequestUtils.getRedirectView(request));
        return mv;
    }

    public ModelAndView runScheduledTasks(HttpServletRequest request,
            HttpServletResponse response)
    {
        if (!StringUtils.isEmpty(request.getParameter("testRun")))
        {
            testRunStudentSync(request);
        }
        else
        {
            runScheduledTasks();
        }

        return displayHome(request, response);
    }

    private void testRunStudentSync(HttpServletRequest request)
    {
        synchUsers("Student", "IntegrationTable1", request);
    }

    public ModelAndView importHistoricPlacements(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = displayHome(request, response);

        UDESImportHistoricalPlacementsThread thread = importHistoricPlacements(
                PortalUtils.getUserLoggedIn(request));

        if (thread != null)
        {
            mv = displayOrbisThreadProgress(thread);
            mv.addObject("successMessage",
                    "Import Historical Placements task successfully started");
        }

        return mv;
    }

    public ModelAndView runTranscriptSync(HttpServletRequest request,
            HttpServletResponse response)
    {
        runTranscriptSync();
        return displayHome(request, response);
    }

    public void runScheduledTasks()
    {
        logger.info("UDES - Import Started.");

        createTerms();
        createPrograms();

        synchPortalStaff();
        synchUsers("Student", "IntegrationTable1", null);

        getJt().execute(
                "update coop_admission set active=0 from coop_admission inner join user_details on coop_admission.student=user_details.USER_DETAILS_ID and user_details.enabled=0");

        runTranscriptSync();
        processWtrHistoryDocs("/content/private/placement_history_records",
                getServletContext());

        logger.info("UDES - Import Finished.");
    }

    private void runTranscriptSync()
    {
        logger.info("Processing transcripts started");
        PDFTranscriptProcessor.processTranscripts(getServletContext());
        logger.info("Processing transcripts finished");
    }

    public void processWtrHistoryDocs(String directory, ServletContext ctx)
    {
        logger.info("Processing historical placement documents started.");
        List coopModules = PortalUtils.getHt()
                .find("from CoopModule cm order by cm.id");
        CoopModule coopModule = coopModules.size() > 0
                ? (CoopModule) coopModules.get(0)
                : null;
        if (coopModule != null)
        {
            DocModule docModule = coopModule.getPostingModule().getDocModule();
            List docTypes = PortalUtils.getHt().find(
                    "from DocType2 dt where dt.docHandlerId=0 and dt.module=? and dt.l2Name='UofS Historical Placements'",
                    docModule);
            if (docTypes.size() > 0)
            {
                try
                {
                    DocType2 dt = (DocType2) docTypes.get(0);
                    List ids = PortalUtils.getHt().find(
                            "select distinct admission.student.id, admission.student.username, admission.student.firstName, admission.student.lastName from CoopAdmission admission where admission.coopTermProgram.term.module=?",
                            coopModule);
                    logger.info(
                            "Number of documents to create/update: " + ids.size());
                    Integer docsCreated = 0;
                    Integer docsUpdated = 0;
                    for (Iterator iterator = ids.iterator(); iterator.hasNext();)
                    {
                        Object[] row = (Object[]) iterator.next();
                        Integer id = (Integer) row[0];
                        String username = (String) row[1];

                        Rectangle pageSize = PageSize.LETTER;
                        Document document = new Document(pageSize, 72f, 72f, 72f,
                                72f);
                        ByteArrayOutputStream baos = new ByteArrayOutputStream();
                        PdfWriter.getInstance(document, baos);
                        document.open();

                        // setup Job Info Section
                        PdfPTable infoSection = new PdfPTable(1);
                        NHelper.applyDefaultTableSettingsOneColumn(infoSection);
                        NHelper.addTableTitleOneColumn(infoSection,
                                "Université de Sherbrooke");
                        NHelper.addTableTitleOneColumn(infoSection,
                                "Historique des stages");
                        NHelper.addEmptyCell(infoSection);

                        PdfPTable nameSection = new PdfPTable(1);
                        NHelper.applyDefaultTableSettingsOneColumn(nameSection);
                        WaterlooTranscriptProcessor.addCell(nameSection,
                                (username + "   " + row[3] + ", " + row[2]),
                                Element.ALIGN_CENTER, NHelper.SM_BOLD_FONT, 0);
                        NHelper.addEmptyCell(nameSection);
                        NHelper.addEmptyCell(nameSection);

                        PdfPTable contentSection = new PdfPTable(4);
                        contentSection.setWidths(new int[] { 1, 2, 2, 1 });
                        contentSection.setTotalWidth(
                                PageSize.LETTER.getWidth() - 72f * 2);
                        contentSection.setLockedWidth(true);
                        contentSection.getDefaultCell()
                                .setBorder(Rectangle.NO_BORDER);
                        contentSection.getDefaultCell().setMinimumHeight(15);
                        contentSection.setSplitRows(true);
                        contentSection.setSplitLate(false);

                        List wtr = PortalUtils.getHt().find(
                                "select wtr.organization.name, wtr.division.s3, wtr.s4, wtr.seq.actualTerm.name, wtr.s38, wtr.seq.termLabel, wtr.seq.admission.coopTermProgram.program.name from CoopWtrMain wtr left join wtr.organization as organization left join wtr.division as division where wtr.seq.admission.student.id=? and wtr.seq.admission.coopTermProgram.term.module=? order by wtr.seq.actualTerm.seq desc",
                                new Object[] { id, coopModule });
                        if (wtr.size() == 0)
                        {
                            contentSection = new PdfPTable(1);
                            NHelper.applyDefaultTableSettingsOneColumn(
                                    contentSection);
                            WaterlooTranscriptProcessor.addCell(contentSection,
                                    "Aucun stage effectué à date",
                                    Element.ALIGN_CENTER, NHelper.SM_NORMAL_FONT,
                                    0);
                        }
                        else
                        {
                            for (Iterator iterator2 = wtr.iterator(); iterator2
                                    .hasNext();)
                            {
                                Object[] r = (Object[]) iterator2.next();
                                String orgName = (r[0] != null ? (String) r[0]
                                        : "");
                                String jobLocation = (r[1] != null ? (String) r[1]
                                        : (r[2] != null ? (String) r[2] : ""));
                                String term = (r[3] != null ? (String) r[3] : "");
                                String grade = (r[4] != null ? (String) r[4] : "");
                                String termLabel = (r[5] != null
                                        ? r[5].toString().replace('W', 'T')
                                        : "");
                                String program = (r[6] != null ? (String) r[6]
                                        : "");

                                WaterlooTranscriptProcessor.addCell(contentSection,
                                        term, Element.ALIGN_LEFT,
                                        NHelper.SM_NORMAL_FONT, 0);
                                WaterlooTranscriptProcessor.addCell(contentSection,
                                        termLabel, Element.ALIGN_LEFT,
                                        NHelper.SM_NORMAL_FONT, 0);
                                WaterlooTranscriptProcessor.addCell(contentSection,
                                        program, Element.ALIGN_LEFT,
                                        NHelper.SM_NORMAL_FONT, 0);
                                WaterlooTranscriptProcessor.addCell(contentSection,
                                        grade, Element.ALIGN_LEFT,
                                        NHelper.SM_NORMAL_FONT, 0);

                                WaterlooTranscriptProcessor.addCell(contentSection,
                                        "", Element.ALIGN_LEFT,
                                        NHelper.SM_NORMAL_FONT, 30);
                                WaterlooTranscriptProcessor.addCell(contentSection,
                                        orgName, Element.ALIGN_LEFT,
                                        NHelper.SM_NORMAL_FONT, 30);
                                WaterlooTranscriptProcessor.addCell(contentSection,
                                        jobLocation, Element.ALIGN_LEFT,
                                        NHelper.SM_NORMAL_FONT, 30);
                                WaterlooTranscriptProcessor.addCell(contentSection,
                                        "", Element.ALIGN_LEFT,
                                        NHelper.SM_NORMAL_FONT, 30);
                            }
                        }

                        document.add(infoSection);
                        document.add(nameSection);
                        document.add(contentSection);

                        document.close();

                        String storageFolderPath = FileUtils.fixFileName(
                                PortalUtils.getRealPath("/") + directory);
                        File storageFolder = new File(storageFolderPath);
                        storageFolder.mkdirs();

                        String fileName = username + "_histplac" + ".pdf";
                        String pdfPath = PortalUtils.getRealPath("/") + directory
                                + "/" + fileName;
                        if (FileUtils.fileExists(pdfPath))
                        {
                            FileUtils.deleteFile(pdfPath);
                            docsUpdated++;
                        }
                        else
                        {
                            docsCreated++;
                        }
                        FileUtils.writeFile(pdfPath, baos.toByteArray());

                        int docOwnerId = WaterlooTranscriptProcessor
                                .getCreateOwner(username, docModule);
                        if (docOwnerId > 0)
                        {

                            List docs = PortalUtils.getHt().find(
                                    "from Doc d where d.docType=? and d.owner.id=? and d.description='UofS Historical Placement Record' order by d.id",
                                    new Object[] { dt, docOwnerId });

                            if (docs.size() == 0)
                            {
                                FilePath fp = FilePathUtils
                                        .getOrCreateFilePath(directory + "/"
                                                + username + "_histplac" + ".pdf");
                                Doc ftDoc = new Doc();
                                DocOwner docOwner = (DocOwner) PortalUtils.getHt()
                                        .load(DocOwner.class, docOwnerId);
                                ftDoc.setOwner(docOwner);
                                ftDoc.setPackage(false);
                                ftDoc.setFile(fileName);
                                ftDoc.setName(username
                                        + "_UofS Historical Placement Record");
                                ftDoc.setDescription(
                                        "UofS Historical Placement Record");
                                ftDoc.setDocType(dt);
                                ftDoc.setCreated(new Date());
                                ftDoc.setUrl(fp);
                                ftDoc.setPdfUrl(fp);
                                ftDoc.setFileStatus(Doc.FILE_STATUS_READY);

                                PortalUtils.getHt().save(ftDoc);
                            }
                            else if (docs.size() > 0)
                            {
                                Doc ftDoc = (Doc) docs.get(0);
                                ftDoc.setDocType(dt);
                                ftDoc.setCreated(new Date());
                                ftDoc.setFileStatus(Doc.FILE_STATUS_READY);
                                PortalUtils.getHt().update(ftDoc);
                            }
                        }
                    }
                    logger.info("Number of documents created: " + docsCreated);
                    logger.info("Number of documents updated: " + docsUpdated);
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
        }
        logger.info("Processing historical placement documents finished.");
    }

    private void createPrograms()
    {
        logger.info("UDES - Creating Programs.");

        HibernateTemplate ht = PortalUtils.getHt();

        List<CoopModule> cmodules = ht.loadAll(CoopModule.class);
        List<NPostingModule> pmodules = ht.loadAll(NPostingModule.class);

        List<Object[]> pls = ht.find(
                "select distinct t.t1, t.t2 from IntegrationTable4 t where t.t99 is null");
        for (Object[] p : pls)
        {
            try
            {
                String code = p[0].toString();
                String name = p[1].toString();

                for (CoopModule cm : cmodules)
                {
                    createCoopProgram(cm, code, name);
                }

                for (NPostingModule npm : pmodules)
                {
                    createNPostingPrograms(npm, code, name);
                }
            }
            catch (Exception e)
            {
            }
        }

        PortalUtils.getJt().execute("update integration_t4 set t99 = '" + DateUtils
                .formatDate(new Date(), DBUtils.DB_DATE_TIME_FORMAT, null) + "'");

        logger.info("UDES - Finished Creating Programs.");
    }

    private NCluster getNCluster(NPostingModule npm, String name)
    {
        NCluster nc = null;

        List<NCluster> ncs = PortalUtils.getHt().find(
                "from NCluster nc where nc.NPostingModule=? and nc.title=?",
                new Object[] { npm, name });
        if (ncs.isEmpty())
        {
            nc = new NCluster();
            nc.setNPostingModule(npm);
            nc.setTitle(name);
            nc.setL2Title(name);
            PortalUtils.getHt().saveOrUpdate(nc);
        }
        else
        {
            nc = ncs.get(0);
        }

        return nc;
    }

    private NProgram createNPostingPrograms(NPostingModule npm, String code,
            String name)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        NProgram p = null;
        name = StringUtils.isEmpty(name) ? code : name;

        List<NProgram> programs = ht.find(
                "from NProgram np where np.NPostingModule=? and np.title=? and np.code=?",
                new Object[] { npm, name, code });
        if (programs.isEmpty())
        {
            p = new NProgram();
            p.setCode(code);
            p.setTitle(name);
            p.setL2Title(name);
            p.setNPostingModule(npm);
            ht.saveOrUpdate(p);

            NCluster nc = getNCluster(npm, "All Programs");
            List<NTerm> nterms = ht.find("from NTerm nt where nt.NPostingModule=?",
                    npm);
            for (NTerm nt : nterms)
            {
                NTermClusterProgram ntcp = new NTermClusterProgram();
                ntcp.setCluster(nc);
                ntcp.setProgram(p);
                ntcp.setTerm(nt);
                ht.saveOrUpdate(ntcp);
            }
        }

        return p;
    }

    private CoopProgram createCoopProgram(CoopModule cm, String code, String name)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        CoopProgram p = null;
        name = StringUtils.isEmpty(name) ? code : name;

        List<CoopProgram> programs = ht.find(
                "from CoopProgram cp where cp.module=? and cp.name=? and cp.code=?",
                new Object[] { cm, name, code });
        if (programs.isEmpty())
        {
            p = new CoopProgram();
            p.setCode(code);
            p.setName(name);
            p.setL2Name(name);
            p.setModule(cm);
            ht.saveOrUpdate(p);

            List<CoopTerm> terms = ht.find("from CoopTerm ct where ct.module=?",
                    cm);
            for (CoopTerm ct : terms)
            {
                CoopTermProgram ctp = new CoopTermProgram();
                ctp.setProgram(p);
                ctp.setTerm(ct);
                ht.saveOrUpdate(ctp);
            }
        }

        return p;
    }

    private void createTerms()
    {
        logger.info("UDES - Creating Terms.");

        HibernateTemplate ht = PortalUtils.getHt();

        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());

        List<CoopModule> coopModules = ht.loadAll(CoopModule.class);
        for (CoopModule cm : coopModules)
        {
            int seq = 1;
            for (int i = cal.get(Calendar.YEAR) - 40; i <= cal.get(Calendar.YEAR)
                    + 30; i++)
            {
                CoopTerm spring = createCoopTerm(cm, i + " - Hiver",
                        i + " - Winter", seq++);
                CoopTerm summer = createCoopTerm(cm, i + " - Été", i + " - Summer",
                        seq++);
                CoopTerm fall = createCoopTerm(cm, i + " - Automne", i + " - Fall",
                        seq++);

                if (cm.getPostingModule() != null)
                {
                    NTerm nspring = createNTerm(cm.getPostingModule(),
                            spring.getName(), spring.getL2Name(), spring.getSeq());
                    spring.setJobPostingTerm(nspring);
                    PortalUtils.getHt().saveOrUpdate(spring);

                    NTerm nsummer = createNTerm(cm.getPostingModule(),
                            summer.getName(), summer.getL2Name(), summer.getSeq());
                    summer.setJobPostingTerm(nsummer);
                    PortalUtils.getHt().saveOrUpdate(summer);

                    NTerm nfall = createNTerm(cm.getPostingModule(), fall.getName(),
                            fall.getL2Name(), fall.getSeq());
                    fall.setJobPostingTerm(nfall);
                    PortalUtils.getHt().saveOrUpdate(fall);
                }
            }
        }

        // List<NPostingModule> npmodules = ht
        // .find("from NPostingModule npm where npm.coOpJobModule=0");
        // for (NPostingModule npm : npmodules)
        // {
        // int ordinal = 1;
        // for (int i = cal.get(Calendar.YEAR) - 40; i <= cal.get(Calendar.YEAR)
        // + 30; i++)
        // {
        // createNTerm(npm, i + " - Winter", i + " - Hiver", ordinal++);
        // createNTerm(npm, i + " - Summer", i + " - été", ordinal++);
        // createNTerm(npm, i + " - Fall", i + " - Automne", ordinal++);
        // }
        // }

        logger.info("UDES - Finished Creating Terms.");
    }

    private NTerm createNTerm(NPostingModule postingModule, String title,
            String l2title, int ordinal)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        NTerm t = null;
        List<NTerm> terms = null;

        if (!StringUtils.isEmpty(title))
        {
            terms = ht.find("from NTerm t where t.title=? and t.NPostingModule=?",
                    new Object[] { title, postingModule });
        }
        else
        {
            terms = ht.find("from NTerm t where t.l2title=? and t.NPostingModule=?",
                    new Object[] { StringUtils.quoteReplacement(l2title, "'", "''"),
                            postingModule });

        }
        if (terms.isEmpty())
        {
            t = new NTerm();
            t.setOrdinal(ordinal);
            t.setTitle(title);
            t.setL2Title(l2title);
            t.setNPostingModule(postingModule);
            PortalUtils.getHt().saveOrUpdate(t);
        }
        else
        {
            t = terms.get(0);
        }

        return t;
    }

    private CoopTerm createCoopTerm(CoopModule cm, String name, String l2Name,
            int sequence)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        CoopTerm t = null;
        List<CoopTerm> terms = null;

        if (!StringUtils.isEmpty(name))
        {
            terms = ht.find("from CoopTerm t where t.name=? and t.module=?",
                    new Object[] { name, cm });
        }
        else if (!StringUtils.isEmpty(l2Name))
        {
            terms = ht.find("from CoopTerm t where t.l2Name=? and t.module=?",
                    new Object[] { StringUtils.quoteReplacement(l2Name, "'", "''"),
                            cm });
        }
        if (terms == null || terms.isEmpty())
        {
            t = new CoopTerm();
            t.setSeq(sequence);
            t.setName(name);
            t.setL2Name(l2Name);
            t.setModule(cm);
            ht.saveOrUpdate(t);
        }
        else
        {
            t = terms.get(0);
        }

        addStatuses(t);

        return t;
    }

    private void addStatuses(CoopTerm t)
    {
        HibernateTemplate ht = PortalUtils.getHt();

        List<CoopStatus> statuses = ht.find("from CoopStatus s where s.term=?", t);
        if (statuses.isEmpty())
        {
            CoopStatus first = new CoopStatus();
            first.setFirstStatus(true);
            first.setTerm(t);
            first.setName("Co-op Record Created");
            ht.save(first);

            CoopStatus last = new CoopStatus();
            last.setLastStatus(true);
            last.setTerm(t);
            last.setName("Co-op Record Approved");
            ht.save(last);
        }
    }

    private void synchUsers(String userType, String integrationTable,
            HttpServletRequest request)
    {
        int processedRecordsCount = 0;
        logger.info("UDES - " + userType + " Import Started.");

        HibernateTemplate ht = PortalUtils.getHt();

        String limitClause = "";
        if (request != null
                && !StringUtils.isEmpty(request.getParameter("testRun")))
        {
            limitClause = " and t.t5='" + request.getParameter("testRun") + "' ";
        }

        List<String> userIds = ht.find("select t.t5 from " + integrationTable
                + " t where (t.t99 is null or t.t99 = '') " + limitClause
                + " order by t.id");
        logger.info("Number of users to be imported: " + userIds.size());

        for (String userId : userIds)
        {
            try
            {
                UDESUser nu = (UDESUser) registrarDAO.getUserByUserId(userId);
                registrarDAO.synchronizeUserData(nu, request);
                processedRecordsCount++;
                // System.out.println(userId + " - " + processedRecordsCount
                // + " out of " + userIds.size());
            }
            catch (Exception e)
            {
                // Add detailed info on which record/s failed and why
                logger.info("Problem encountered syncing user " + userId + ": "
                        + ExceptionUtils.getFullStackTrace(e));
            }
        }

        logger.info("Number of records successfully imported: "
                + processedRecordsCount);
        logger.info("UDES - " + userType + " Import Finished.");
    }

    private void synchPortalStaff()
    {
        logger.info("UDES - Portal Staff Import Started.");
        logger.info("UDES - Portal Staff Import Finished.");
    }

    public ModelAndView login(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-store");
        response.setDateHeader("Expires", -1);

        String ticket = request.getParameter("ticket");

        UDESAuth udesAuth = new UDESAuth();
        boolean authSuccess = udesAuth.authenticate(ticket, service, validator);

        if (authSuccess)
        {
            UserDetailsImpl user = findAndSynchUser(udesAuth.getCip(), request);

            if (user == null)
            {
                user = findAndSynchUser(udesAuth.getStudentNumber(), request);
            }

            if (user != null)
            {

                String pwd = UserDetailsHelper.makePassword(user.getUsername(),
                        passwordEncoder);
                user.setPassword(passwordEncoder.encodePassword(pwd, null));
                user.setCleartextPassword(pwd);
                user.setEnabled(true);
                user.setAccountNonExpired(true);
                user.setCredentialsNonExpired(true);

                PortalUtils.getHt().saveOrUpdate(user);

                AuthenticationUtils.setUserLoggedIn(request, user, response);
            }
        }

        return displayHome(request, response);
    }

    public ModelAndView importLegacyUserServiceTeams(HttpServletRequest request,
            HttpServletResponse response)
    {
        HibernateTemplate ht = getHt();
        InteractionRole role = (InteractionRole) InteractionHelper
                .getRolesMapKeyedByLevel().get(InteractionRole.ROLE_ONE);
        List<Integer> tIds = ht.find("select t.id from IntegrationTable9 t");
        for (Integer tId : tIds)
        {
            IntegrationTable9 t = (IntegrationTable9) ht
                    .load(IntegrationTable9.class, tId);

            List<UserDetailsImpl> contact = ht
                    .find("from UserDetailsImpl u where u.legacyId=?", t.getT1());
            if (!contact.isEmpty() && contact.size() == 1)
            {
                UserDetailsImpl user = UserDetailsHelper
                        .getUserByUsername(t.getT2());
                List<AcrmProgram> programs = ht
                        .find("from AcrmProgram p where p.code=?", t.getT3());
                if (user != null && !programs.isEmpty())
                {
                    StringBuilder sb = new StringBuilder();
                    sb.append(
                            " from InteractionTeamMember itm where itm.member.id = ? ");
                    List<InteractionTeamMember> itms = ht.find(sb.toString(),
                            new Object[] { user.getId() });

                    for (InteractionTeamMember interactionTeamMember : itms)
                    {
                        AcrmProgram acrmProgram = programs.get(0);
                        InteractionTeamMemberRole tmr = InteractionHelper
                                .getOrCreateTeamMemberRole(interactionTeamMember,
                                        role, acrmProgram);
                        InteractionHelper.createContactServiceTeamMember(
                                contact.get(0), role, Lists.newArrayList(tmr),
                                request, programs.get(0), true, null);

                        if ((Integer) ht.find(
                                "select count(aup) from AcrmUserProgram aup where aup.acrmProgram=? and aup.user=?",
                                new Object[] { programs.get(0), contact.get(0) })
                                .get(0) == 0)
                        {
                            AcrmUserProgram aup = new AcrmUserProgram();
                            aup.setAcrmProgram(programs.get(0));
                            aup.setUser(contact.get(0));
                            ht.save(aup);
                        }

                    }

                }
            }
        }

        return displayHome(request, response);
    }

    private UserDetailsImpl findAndSynchUser(String nuid,
            HttpServletRequest request)
    {
        return registrarDAO.synchronizeUserData(registrarDAO.getUserByUserId(nuid),
                request);
    }

    private UDESImportHistoricalPlacementsThread importHistoricPlacements(
            UserDetailsImpl user)
    {
        UDESImportHistoricalPlacementsThread thread = null;
        HibernateTemplate ht = getHt();

        CoopModule cm = (CoopModule) ht.find("from CoopModule c").get(0);
        CoopWtrModelMain mm = getCoopWtrModelMain(cm);

        if (cm != null)
        {
            List<Integer> records = ht
                    .find("select t.id from IntegrationTable10 t");
            thread = new UDESImportHistoricalPlacementsThread(user, records, cm,
                    mm);
            thread.start();
        }

        return thread;
    }

    public static Company getDivision(String legacyId)
    {
        Company c = null;

        List<Company> companies = PortalUtils.getHt()
                .find("from Company c where c.legacyId=?", legacyId);
        if (!companies.isEmpty())
        {
            c = companies.get(0);
        }

        return c;
    }

    public static Organization getOrganization(String legacyId)
    {
        Organization o = null;

        List<Organization> orgs = PortalUtils.getHt()
                .find("from Organization o where o.legacyId=?", legacyId);
        if (!orgs.isEmpty())
        {
            o = orgs.get(0);
        }

        return o;
    }

    public static UserDetailsImpl getEmployer(String legacyId)
    {
        UserDetailsImpl employer = null;

        List<UserDetailsImpl> employers = PortalUtils.getHt()
                .find("from UserDetailsImpl user where user.legacyId=?", legacyId);
        if (!employers.isEmpty())
        {
            employer = employers.get(0);
        }

        return employer;
    }

    private CoopWtrModelMain getCoopWtrModelMain(CoopModule cm)
    {
        CoopWtrModelMain mm = null;
        List<CoopWtrModelMain> modelMain = PortalUtils.getHt()
                .find("from CoopWtrModelMain wtrm where wtrm.coopModule=?", cm);
        if (!modelMain.isEmpty())
        {
            mm = modelMain.get(0);
        }

        return mm;
    }

    public void runCustomQueries()
    {

        // Grouping Coop Queries together
        PortalUtils.getTransactionTemplate().execute(status -> {
            logger.info("UDES - Executing Custom Query #1");
            runCustomQuery1();
            logger.info("UDES - Finished Custom Query #1");

            logger.info("UDES - Executing Custom Query #2");
            runCustomQuery2();
            logger.info("UDES - Finished Custom Query #2");

            logger.info("UDES - Executing Custom Query #3");
            runCustomQuery3();
            logger.info("UDES - Finished Custom Query #3");

            logger.info("UDES - Executing Custom Query #4");
            runCustomQuery4();
            logger.info("UDES - Finished Custom Query #4");

            logger.info("UDES - Executing Custom Query #5");
            runCustomQuery5();
            logger.info("UDES - Finished Custom Query #5");

            return null;
        });

        PortalUtils.getTransactionTemplate().execute(status -> {

            logger.info("UDES - Executing Custom Query #6");
            runCustomQuery6();
            logger.info("UDES - Finished Custom Query #6");

            return null;
        });

        PortalUtils.getTransactionTemplate().execute(status -> {

            logger.info("UDES - Executing Custom Query #7 (Fields of Interest)");
            runCustomQuery7();
            logger.info("UDES - Finished Custom Query #7");

            return null;
        });

        PortalUtils.getTransactionTemplate().execute(status -> {

            logger.info("UDES - Executing Custom Query #8 (Saved Searches)");
            runCustomQuery8();
            logger.info("UDES - Finished Custom Query #8");

            return null;
        });

        PortalUtils.getTransactionTemplate().execute(status -> {

            logger.info("UDES - Executing Custom Query #9 (Job Favourites)");
            runCustomQuery9();
            logger.info("UDES - Finished Custom Query #9");

            return null;
        });

        PortalUtils.getTransactionTemplate().execute(status -> {

            logger.info("UDES - Executing Custom Query #10 (Jobs Blacklist)");
            runCustomQuery10();
            logger.info("UDES - Finished Custom Query #10");

            return null;
        });

        PortalUtils.getTransactionTemplate().execute(status -> {

            logger.info("UDES - Executing Custom Query #11 (Job Views)");
            runCustomQuery11();
            logger.info("UDES - Finished Custom Query #11");

            return null;
        });
        
        PortalUtils.getTransactionTemplate().execute(status -> {

            logger.info("UDES - Executing Custom Query #12 (Student Intention)");
            runCustomQuery12();
            logger.info("UDES - Finished Custom Query #12");

            return null;
        });

    }

    private void runCustomQuery1()
    {
        PortalUtils.getJt().execute(
                "DELETE FROM dbo.coop_admission_seq WHERE coop_admission_seq.admission IN (SELECT DISTINCT DuplicateLegacyWithoutWorkTerm.id FROM (SELECT coop_admission.id, coop_admission.active, coop_admission.legacyS1, coop_admission.dateCreated, SUM(CASE WHEN (coop_wtr_main.id is null) THEN 0 ELSE 1 END) as HasWorkterm, SUM(CASE WHEN (coop_student_tac.id is null) THEN 0 ELSE 1 END) as HasTAC FROM dbo.coop_admission inner join (SELECT user_details.username, user_details.firstname, user_details.lastname, coop_admission.legacyS1, COUNT(coop_admission.legacyS1) as decompte FROM dbo.coop_admission inner join dbo.user_details on user_details.USER_DETAILS_ID=coop_admission.student GROUP BY user_details.username, user_details.firstname, user_details.lastname, coop_admission.legacyS1 HAVING COUNT(coop_admission.legacyS1) > 1) DuplicateLegacyIDs on DuplicateLegacyIDs.legacyS1=coop_admission.legacyS1 inner join dbo.coop_admission_seq on coop_admission_seq.admission=coop_admission.id left join dbo.coop_wtr_main on coop_wtr_main.seq=coop_admission_seq.id left join dbo.coop_student_tac on coop_student_tac.sequence=coop_admission_seq.id GROUP BY coop_admission.id, coop_admission.active, coop_admission.legacyS1, coop_admission.dateCreated HAVING SUM(CASE WHEN (coop_wtr_main.id is null) THEN 0 ELSE 1 END) = 0 and SUM(CASE WHEN (coop_student_tac.id is null) THEN 0 ELSE 1 END) = 0) DuplicateLegacyWithoutWorkTerm inner join dbo.coop_admission on coop_admission.legacyS1=DuplicateLegacyWithoutWorkTerm.legacyS1 WHERE DuplicateLegacyWithoutWorkTerm.active=coop_admission.active and DuplicateLegacyWithoutWorkTerm.dateCreated < coop_admission.dateCreated); /* On supprime les log associés au coop_admission qu'on va supprimer par la suite */ DELETE FROM dbo.coop_admission_log WHERE coopAdmission IN (SELECT DISTINCT coop_admission.id FROM dbo.coop_admission left join dbo.coop_admission_seq on coop_admission_seq.admission=coop_admission.id WHERE coop_admission_seq.id is null); /* On supprime les élément dans lv_lastview associés au coop_admission qu'on va supprimer par la suite */ DELETE FROM dbo.lv_lastViewed WHERE admission IN (SELECT DISTINCT coop_admission.id FROM dbo.coop_admission left join dbo.coop_admission_seq on coop_admission_seq.admission=coop_admission.id WHERE coop_admission_seq.id is null); /* On supprime les dossier en tant que tel */ DELETE FROM dbo.coop_admission WHERE  coop_admission.id IN (SELECT DISTINCT coop_admission.id FROM dbo.coop_admission left join dbo.coop_admission_seq on coop_admission_seq.admission=coop_admission.id WHERE coop_admission_seq.id is null);");
    }

    private void runCustomQuery2()
    {
        PortalUtils.getJt().execute(
                "DELETE FROM dbo.coop_admission_seq WHERE id IN (SELECT coop_admission_seq.id FROM dbo.coop_admission_seq inner join (SELECT user_details.username, user_details.firstname, user_details.lastname, coop_admission.id as AdmissionID, coop_term.l2Name as TrimestreDoublon, MIN(coop_admission_seq.id) as MinimumID, MAX(coop_admission_seq.id) as MaximumID FROM dbo.user_details inner join dbo.coop_admission on coop_admission.student=user_details.USER_DETAILS_ID inner join dbo.coop_admission_seq on coop_admission_seq.admission=coop_admission.id inner join dbo.coop_term on coop_term.id=coop_admission_seq.actualTerm GROUP BY user_details.username, user_details.firstname, user_details.lastname, coop_admission.id, coop_term.l2Name HAVING COUNT(coop_term.l2Name)>1 and MIN(coop_admission_seq.seq)=MAX(coop_admission_seq.seq)) Subset on subset.MinimumID=coop_admission_seq.id WHERE coop_admission_seq.placed=0)  /* La deuxième requête supprime les doublons de \"coop_admission_seq\" (Ceux qui ont un numéro de séquence à 0) avec le même trimestre et même coop_admission tandis que les coop_admission_seq.seq conservés ne sont pas égal à 0 */ DELETE FROM coop_admission_seq WHERE id in (SELECT coop_admission_seq.id FROM coop_admission_seq inner join (SELECT coop_admission.id as AdmissionID, coop_term.id as termId, MIN(coop_admission_seq.seq) as Minimum, MAX(coop_admission_seq.seq) as Maximum, COUNT(coop_term.l2Name) as NombreDoublon FROM dbo.user_details inner join dbo.coop_admission on coop_admission.student=user_details.USER_DETAILS_ID inner join dbo.coop_admission_seq on coop_admission_seq.admission=coop_admission.id inner join dbo.coop_term on coop_term.id=coop_admission_seq.actualTerm WHERE coop_admission_seq.placed=0 GROUP BY coop_admission.id, coop_term.id, coop_term.l2Name HAVING COUNT(coop_term.l2Name)>1 ) subresult on coop_admission_seq.admission=subresult.AdmissionID and coop_admission_seq.seq=0 and coop_admission_seq.actualTerm=subresult.termID)");
    }

    private void runCustomQuery3()
    {
        PortalUtils.getJt().execute(
                "INSERT INTO dbo.coop_admission_log (coopAdmission,createdBy,coopStatus,dateCreated,activateMode,extraRecordStatus) (SELECT coop_admission_old_active.id as coopAdmission, 415729 as createdBy, 872 as coopStatus, GETDATE() as dateCreated, 'Deactivated' as activateMode, null as extraRecordStatus FROM dbo.coop_admission coop_admission_old_active inner join dbo.user_details on user_details.USER_DETAILS_ID=coop_admission_old_active.student inner join dbo.coop_admission coop_admission_new_active on coop_admission_new_active.student=user_details.USER_DETAILS_ID and coop_admission_new_active.id<>coop_admission_old_active.id WHERE coop_admission_old_active.active=1 and coop_admission_new_active.active=1 and coop_admission_old_active.dateCreated<coop_admission_new_active.dateCreated);  UPDATE dbo.integration_t6 SET t2='Inactive', t21='N' WHERE t20 IN ( SELECT coop_admission_old_active.legacyS1 FROM dbo.coop_admission coop_admission_old_active inner join dbo.user_details on user_details.USER_DETAILS_ID=coop_admission_old_active.student inner join dbo.coop_admission coop_admission_new_active on coop_admission_new_active.student=user_details.USER_DETAILS_ID and coop_admission_new_active.id<>coop_admission_old_active.id WHERE coop_admission_old_active.active=1 and coop_admission_new_active.active=1 and coop_admission_old_active.dateCreated<coop_admission_new_active.dateCreated);  UPDATE dbo.coop_admission SET active=0, dateUpdated=GETDATE() WHERE id IN ( SELECT coop_admission_old_active.id FROM dbo.coop_admission coop_admission_old_active inner join dbo.user_details on user_details.USER_DETAILS_ID=coop_admission_old_active.student inner join dbo.coop_admission coop_admission_new_active on coop_admission_new_active.student=user_details.USER_DETAILS_ID and coop_admission_new_active.id<>coop_admission_old_active.id WHERE coop_admission_old_active.active=1 and coop_admission_new_active.active=1 and coop_admission_old_active.dateCreated<coop_admission_new_active.dateCreated);");
    }

    private void runCustomQuery4()
    {
        PortalUtils.getJt().execute(
                "UPDATE coop_wtr_main_to_move SET coop_wtr_main_to_move.seq=active_coop_admission_seq.id FROM dbo.coop_admission_seq active_coop_admission_seq inner join dbo.coop_admission active_coop_admission on active_coop_admission.id=active_coop_admission_seq.admission and active_coop_admission.active=1 inner join dbo.coop_term active_coop_term on active_coop_term.id=active_coop_admission_seq.actualTerm inner join dbo.user_details on user_details.USER_DETAILS_ID = active_coop_admission.student inner join dbo.coop_term_program active_coop_term_program on active_coop_term_program.id=active_coop_admission.coopTermProgram inner join dbo.coop_program active_coop_program on active_coop_program.id=active_coop_term_program.program inner join dbo.coop_admission inactive_coop_admission on inactive_coop_admission.coopTermProgram=active_coop_admission.coopTermProgram and inactive_coop_admission.student=active_coop_admission.student and inactive_coop_admission.id<>active_coop_admission.id inner join dbo.coop_admission_seq inactive_coop_admission_seq on inactive_coop_admission_seq.admission=inactive_coop_admission.id and inactive_coop_admission_seq.actualTerm=active_coop_admission_seq.actualTerm and inactive_coop_admission_seq.id<>active_coop_admission_seq.id inner join dbo.coop_wtr_main coop_wtr_main_to_move on coop_wtr_main_to_move.seq=inactive_coop_admission_seq.id left join /* Robustness to avoid the cases where there are more than one active program for one student (it should not happen, but if it does we don't want to mess around with those) */ dbo.coop_admission second_active_coop_admission on second_active_coop_admission.id<>active_coop_admission.id and second_active_coop_admission.student=active_coop_admission.student and second_active_coop_admission.active=1 WHERE active_coop_admission_seq.placed=0 and active_coop_admission_seq.termType='Work' and inactive_coop_admission_seq.placed=1 and /* Robustness to avoid the cases where there are more than one active program for one student (it should not happen, but if it does we don't want to mess around with those) */ second_active_coop_admission.id is null;  /* Update the coop_admission_seq linked to the active admission program to reflect the fact that the workterm record moved to the active coop_admission_seq */ UPDATE active_coop_admission_seq SET active_coop_admission_seq.placed=inactive_coop_admission_seq.placed, active_coop_admission_seq.released=inactive_coop_admission_seq.released, active_coop_admission_seq.releaseHistory=inactive_coop_admission_seq.releaseHistory FROM dbo.coop_admission_seq active_coop_admission_seq inner join dbo.coop_admission active_coop_admission on active_coop_admission.id=active_coop_admission_seq.admission and active_coop_admission.active=1 inner join dbo.coop_term active_coop_term on active_coop_term.id=active_coop_admission_seq.actualTerm inner join dbo.user_details on user_details.USER_DETAILS_ID = active_coop_admission.student inner join dbo.coop_term_program active_coop_term_program on active_coop_term_program.id=active_coop_admission.coopTermProgram inner join dbo.coop_program active_coop_program on active_coop_program.id=active_coop_term_program.program inner join dbo.coop_admission inactive_coop_admission on inactive_coop_admission.coopTermProgram=active_coop_admission.coopTermProgram and inactive_coop_admission.student=active_coop_admission.student and inactive_coop_admission.id<>active_coop_admission.id inner join dbo.coop_admission_seq inactive_coop_admission_seq on inactive_coop_admission_seq.admission=inactive_coop_admission.id and inactive_coop_admission_seq.actualTerm=active_coop_admission_seq.actualTerm and inactive_coop_admission_seq.id<>active_coop_admission_seq.id inner join dbo.coop_wtr_main coop_wtr_main_to_move on coop_wtr_main_to_move.seq=active_coop_admission_seq.id left join /* Robustness to avoid the cases where there are more than one active program for one student (it should not happen, but if it does we don't want to mess around with those) */ dbo.coop_admission second_active_coop_admission on second_active_coop_admission.id<>active_coop_admission.id and second_active_coop_admission.student=active_coop_admission.student and second_active_coop_admission.active=1 WHERE active_coop_admission_seq.placed=0 and active_coop_admission_seq.termType='Work' and inactive_coop_admission_seq.placed=1 and /* Robustness to avoid the cases where there are more than one active program for one student (it should not happen, but if it does we don't want to mess around with those) */ second_active_coop_admission.id is null;  /* Update the coop_admission_seq linked to the inactive admission program to reflect the fact that the workterm record moved to the active coop_admission_seq */ UPDATE inactive_coop_admission_seq SET inactive_coop_admission_seq.placed=0, inactive_coop_admission_seq.released=0, inactive_coop_admission_seq.releaseHistory=Null FROM dbo.coop_admission_seq active_coop_admission_seq inner join dbo.coop_admission active_coop_admission on active_coop_admission.id=active_coop_admission_seq.admission and active_coop_admission.active=1 inner join dbo.coop_term active_coop_term on active_coop_term.id=active_coop_admission_seq.actualTerm inner join dbo.user_details on user_details.USER_DETAILS_ID = active_coop_admission.student inner join dbo.coop_term_program active_coop_term_program on active_coop_term_program.id=active_coop_admission.coopTermProgram inner join dbo.coop_program active_coop_program on active_coop_program.id=active_coop_term_program.program inner join dbo.coop_admission inactive_coop_admission on inactive_coop_admission.coopTermProgram=active_coop_admission.coopTermProgram and inactive_coop_admission.student=active_coop_admission.student and inactive_coop_admission.id<>active_coop_admission.id inner join dbo.coop_admission_seq inactive_coop_admission_seq on inactive_coop_admission_seq.admission=inactive_coop_admission.id and inactive_coop_admission_seq.actualTerm=active_coop_admission_seq.actualTerm and inactive_coop_admission_seq.id<>active_coop_admission_seq.id inner join dbo.coop_wtr_main coop_wtr_main_to_move on coop_wtr_main_to_move.seq=active_coop_admission_seq.id left join /* Robustness to avoid the cases where there are more than one active program for one student (it should not happen, but if it does we don't want to mess around with those) */ dbo.coop_admission second_active_coop_admission on second_active_coop_admission.id<>active_coop_admission.id and second_active_coop_admission.student=active_coop_admission.student and second_active_coop_admission.active=1 WHERE active_coop_admission_seq.placed=1 and active_coop_admission_seq.termType='Work' and inactive_coop_admission_seq.placed=1 and /* Robustness to avoid the cases where there are more than one active program for one student (it should not happen, but if it does we don't want to mess around with those) */ second_active_coop_admission.id is null;");
    }

    private void runCustomQuery5()
    {
        PortalUtils.getJt().execute(
                "INSERT into interaction_coop_admission_stmm (admission, teamMemberRole) SELECT active_coop_admission.id as admission, team_member_role_inactif.id as teamMemberRole FROM /* Dossier coop actif */ coop_admission active_coop_admission inner join user_details on user_details.USER_DETAILS_ID = active_coop_admission.student inner join coop_term_program active_coop_term_program on active_coop_term_program.id=active_coop_admission.coopTermProgram inner join coop_program active_coop_program on active_coop_program.id=active_coop_term_program.program inner join /* Dossier coop inactif */ dbo.coop_admission as inactive_coop_admission on inactive_coop_admission.student=user_details.USER_DETAILS_ID inner join dbo.coop_term_program as inactive_coop_term_program on inactive_coop_term_program.id=inactive_coop_admission.coopTermProgram inner join dbo.coop_program as inactive_coop_program on inactive_coop_program.id=inactive_coop_term_program.program inner join /* Pour lequel il y a un coach dans le dossier inactif (ancienne version)*/ interaction_coop_admission_stmm coop_admission_stmm_inactif on coop_admission_stmm_inactif.admission=inactive_coop_admission.id inner join interaction_team_member_role team_member_role_inactif on team_member_role_inactif.id=coop_admission_stmm_inactif.teamMemberRole inner join interaction_role role_inactif on role_inactif.id=team_member_role_inactif.role and role_inactif.l2Name='Conseiller coach' left join /* Pour lequel il n'y a pas de conseiller dans le dossier actif (nouvelle version)*/ interaction_coop_admission_stmm coop_admission_stmm_actif on coop_admission_stmm_actif.admission=active_coop_admission.id left join /* Robustness to avoid the cases where there are more than one active program for one student (it should not happen, but if it does we don't want to mess around with those) */ coop_admission second_active_coop_admission on second_active_coop_admission.id<>active_coop_admission.id and second_active_coop_admission.student=active_coop_admission.student and second_active_coop_admission.active=1 WHERE /* Dossier coop actif */ active_coop_admission.active=1 and /* Aucun conseiller sur le dossier coop actif */ coop_admission_stmm_actif.id is null and /* Robustness to avoid the cases where there are more than one active program for one student (it should not happen, but if it does we don't want to mess around with those) */ second_active_coop_admission.id is null;");
    }

    private void runCustomQuery6()
    {
        PortalUtils.getJt().execute(
                "DELETE from dbo.res_avail WHERE res_avail.id in ( SELECT idToDelete FROM (SELECT MAX(res_avail.id) as idToDelete, res_res.l2Name, res_avail.fromDate, res_avail.toDate, COUNT(res_avail.id) as Nombre, MIN(res_avail.status) as MINstatus, MAX(res_avail.status) as MAXstatus FROM dbo.res_avail inner join dbo.res_res on res_res.id=res_avail.res GROUP BY res_res.l2Name, res_avail.fromDate, res_avail.toDate HAVING COUNT(res_avail.id)>1 ) ToSelect)");
    }

    private void runCustomQuery7()
    {

        // Fields of interest - Transfer from STUDENT record to ALUMNI record
        PortalUtils.getJt().execute(
                "UPDATE Alumni SET Alumni.T1 = Student.T1 FROM user_details Alumni inner join user_details_groups udgAlumni on udgAlumni.userdetailsid=Alumni.User_Details_ID inner join user_group ugAlumni on ugAlumni.id=udgAlumni.usergroupid inner join user_details_link on user_details_link.user2=Alumni.USER_DETAILS_ID inner join user_details Student on Student.USER_DETAILS_ID=user_details_link.user1 and Student.USER_DETAILS_ID!=Alumni.USER_DETAILS_ID  inner join  user_details_groups udgStudent on udgStudent.userdetailsid=Student.User_Details_ID inner join user_group ugStudent on ugStudent.id=udgStudent.usergroupid  WHERE (Student.T1 is not null and Alumni.T1 is null and Student.userStatus != 'Active'  and ugStudent.name='Student' and ugAlumni.name='Alumni');");

        // Fields of interest - Transfer from ALUMNI record to STUDENT record
        PortalUtils.getJt().execute(
                "UPDATE Student SET Student.T1 = Alumni.T1 FROM     user_details Alumni inner join user_details_groups udgAlumni on udgAlumni.userdetailsid=Alumni.User_Details_ID inner join user_group ugAlumni on ugAlumni.id=udgAlumni.usergroupid inner join user_details_link on user_details_link.user2=Alumni.USER_DETAILS_ID inner join user_details Student on Student.USER_DETAILS_ID=user_details_link.user1 and Student.USER_DETAILS_ID!=Alumni.USER_DETAILS_ID inner join  user_details_groups udgStudent on udgStudent.userdetailsid=Student.User_Details_ID inner join user_group ugStudent on ugStudent.id=udgStudent.usergroupid WHERE (Student.T1 is null and Alumni.T1 is not null and Student.userStatus = 'Active' and ugStudent.name='Student' and ugAlumni.name='Alumni');");
    }

    private void runCustomQuery8()
    {
        // SAVED SEARCH - Transfer from STUDENT record to ALUMNI record
        PortalUtils.getJt().execute(
                "UPDATE StudentSavedSearch SET StudentSavedSearch.uzer = Alumni.USER_DETAILS_ID FROM  user_details Alumni inner join user_details_groups udgAlumni on udgAlumni.userdetailsid=Alumni.User_Details_ID inner join user_group ugAlumni on ugAlumni.id=udgAlumni.usergroupid inner join user_details_link on user_details_link.user2=Alumni.USER_DETAILS_ID inner join user_details Student on Student.USER_DETAILS_ID=user_details_link.user1 and Student.USER_DETAILS_ID!=Alumni.USER_DETAILS_ID inner join np_search StudentSavedSearch on StudentSavedSearch.uzer=Student.USER_DETAILS_ID and StudentSavedSearch.module=15 inner join user_details_groups udgStudent on udgStudent.userdetailsid=Student.User_Details_ID inner join user_group ugStudent on ugStudent.id=udgStudent.usergroupid  WHERE (Student.userStatus != 'Active' and ugStudent.name='Student' and ugAlumni.name='Alumni');");

        // SAVED SEARCH - Transfer from ALUMNI record to STUDENT record
        PortalUtils.getJt().execute(
                "UPDATE AlumniSavedSearch SET AlumniSavedSearch.uzer = Student.USER_DETAILS_ID FROM     user_details Alumni inner join user_details_groups udgAlumni on udgAlumni.userdetailsid=Alumni.User_Details_ID inner join user_group ugAlumni on ugAlumni.id=udgAlumni.usergroupid inner join user_details_link on user_details_link.user2=Alumni.USER_DETAILS_ID inner join user_details Student on Student.USER_DETAILS_ID=user_details_link.user1 and Student.USER_DETAILS_ID!=Alumni.USER_DETAILS_ID inner join np_search AlumniSavedSearch on AlumniSavedSearch.uzer=Alumni.USER_DETAILS_ID and AlumniSavedSearch.module=15 inner join  user_details_groups udgStudent on udgStudent.userdetailsid=Student.User_Details_ID inner join user_group ugStudent on ugStudent.id=udgStudent.usergroupid WHERE (Student.userStatus = 'Active' and ugStudent.name='Student' and ugAlumni.name='Alumni');");
    }

    private void runCustomQuery9()
    {
        // Jobs FAVORITES - Transfer from STUDENT record to ALUMNI record
        PortalUtils.getJt().execute(
                "UPDATE StudentFavorite SET StudentFavorite.uzer = Alumni.USER_DETAILS_ID FROM  user_details Alumni inner join  user_details_groups udgAlumni on udgAlumni.userdetailsid=Alumni.User_Details_ID inner join  user_group ugAlumni on ugAlumni.id=udgAlumni.usergroupid inner join     user_details_link on user_details_link.user2=Alumni.USER_DETAILS_ID inner join  user_details Student on Student.USER_DETAILS_ID=user_details_link.user1 and Student.USER_DETAILS_ID!=Alumni.USER_DETAILS_ID inner join  np_posting_user_favorite StudentFavorite on StudentFavorite.uzer=Student.USER_DETAILS_ID inner join     np_posting Job on Job.id=StudentFavorite.posting and Job.NPostingModule=15  inner join      user_details_groups udgStudent on udgStudent.userdetailsid=Student.User_Details_ID inner join   user_group ugStudent on ugStudent.id=udgStudent.usergroupid WHERE   (Student.userStatus != 'Active'     /* 2020-11-10 Orbis a suggéré la ligne suivante (ORB-1997) */   and ugStudent.name='Student' and ugAlumni.name='Alumni');");

        // Jobs FAVORITES - Transfer from ALUMNI record to STUDENT record
        PortalUtils.getJt().execute(
                "UPDATE AlumniFavorite SET AlumniFavorite.uzer = Student.USER_DETAILS_ID FROM   user_details Alumni inner join  user_details_groups udgAlumni on udgAlumni.userdetailsid=Alumni.User_Details_ID inner join  user_group ugAlumni on ugAlumni.id=udgAlumni.usergroupid inner join     user_details_link on user_details_link.user2=Alumni.USER_DETAILS_ID inner join  user_details Student on Student.USER_DETAILS_ID=user_details_link.user1 and Student.USER_DETAILS_ID!=Alumni.USER_DETAILS_ID inner join  np_posting_user_favorite AlumniFavorite on AlumniFavorite.uzer=Alumni.USER_DETAILS_ID inner join    np_posting Job on Job.id=AlumniFavorite.posting and Job.NPostingModule=15   inner join      user_details_groups udgStudent on udgStudent.userdetailsid=Student.User_Details_ID inner join   user_group ugStudent on ugStudent.id=udgStudent.usergroupid WHERE   (Student.userStatus = 'Active'  and ugStudent.name='Student' and ugAlumni.name='Alumni');");

    }

    private void runCustomQuery10()
    {
        // Jobs BLACKLIST - Transfer from STUDENT record to ALUMNI record
        PortalUtils.getJt().execute(
                "UPDATE StudentBlacklist SET StudentBlacklist.uzer = Alumni.USER_DETAILS_ID FROM    user_details Alumni inner join  user_details_groups udgAlumni on udgAlumni.userdetailsid=Alumni.User_Details_ID inner join  user_group ugAlumni on ugAlumni.id=udgAlumni.usergroupid inner join     user_details_link on user_details_link.user2=Alumni.USER_DETAILS_ID inner join  user_details Student on Student.USER_DETAILS_ID=user_details_link.user1 and Student.USER_DETAILS_ID!=Alumni.USER_DETAILS_ID inner join  np_posting_user_blacklist StudentBlacklist on StudentBlacklist.uzer=Student.USER_DETAILS_ID inner join  np_posting Job on Job.id=StudentBlacklist.posting and Job.NPostingModule=15     inner join      user_details_groups udgStudent on udgStudent.userdetailsid=Student.User_Details_ID inner join   user_group ugStudent on ugStudent.id=udgStudent.usergroupid  WHERE  (Student.userStatus != 'Active'     and ugStudent.name='Student' and ugAlumni.name='Alumni');");

        // Jobs BLACKLIST - Transfer from ALUMNI record to STUDENT record
        PortalUtils.getJt().execute(
                "UPDATE AlumniBlacklist SET AlumniBlacklist.uzer = Student.USER_DETAILS_ID FROM     user_details Alumni inner join  user_details_groups udgAlumni on udgAlumni.userdetailsid=Alumni.User_Details_ID inner join  user_group ugAlumni on ugAlumni.id=udgAlumni.usergroupid inner join     user_details_link on user_details_link.user2=Alumni.USER_DETAILS_ID inner join  user_details Student on Student.USER_DETAILS_ID=user_details_link.user1 and Student.USER_DETAILS_ID!=Alumni.USER_DETAILS_ID inner join  np_posting_user_blacklist AlumniBlacklist on AlumniBlacklist.uzer=Alumni.USER_DETAILS_ID inner join     np_posting Job on Job.id=AlumniBlacklist.posting and Job.NPostingModule=15      inner join      user_details_groups udgStudent on udgStudent.userdetailsid=Student.User_Details_ID inner join   user_group ugStudent on ugStudent.id=udgStudent.usergroupid  WHERE  (Student.userStatus = 'Active'  and ugStudent.name='Student' and ugAlumni.name='Alumni')");

    }

    private void runCustomQuery11()
    {
        // Jobs views - Transfer from STUDENT record to ALUMNI record
        PortalUtils.getJt().execute(
                "UPDATE StudentView SET StudentView.uzer = Alumni.USER_DETAILS_ID FROM        user_details Alumni inner join        user_details_link on user_details_link.user2=Alumni.USER_DETAILS_ID inner join        user_details Student on Student.USER_DETAILS_ID=user_details_link.user1 and Student.USER_DETAILS_ID!=Alumni.USER_DETAILS_ID inner join        np_posting_view StudentView on StudentView.uzer=Student.USER_DETAILS_ID inner join        np_posting Job on Job.id=StudentView.posting and Job.NPostingModule=15 inner join        user_details_groups udgAlumni on udgAlumni.userdetailsid=Alumni.User_Details_ID inner join user_group ugAlumni on ugAlumni.id=udgAlumni.usergroupid inner join user_details_groups udgStudent on udgStudent.userdetailsid=Student.User_Details_ID inner join user_group ugStudent on ugStudent.id=udgStudent.usergroupid WHERE       (Student.userStatus != 'Active')      and (ugStudent.name='Student' and ugAlumni.name='Alumni');");

        // Jobs views - Transfer from ALUMNI record to STUDENT record
        PortalUtils.getJt().execute(
                "UPDATE AlumniView SET AlumniView.uzer = Student.USER_DETAILS_ID FROM user_details Alumni inner join        user_details_link on user_details_link.user2=Alumni.USER_DETAILS_ID inner join        user_details Student on Student.USER_DETAILS_ID=user_details_link.user1 and Student.USER_DETAILS_ID!=Alumni.USER_DETAILS_ID inner join        np_posting_view AlumniView on AlumniView.uzer=Alumni.USER_DETAILS_ID inner join        np_posting Job on Job.id=AlumniView.posting and Job.NPostingModule=15 inner join  user_details_groups udgAlumni on udgAlumni.userdetailsid=Alumni.User_Details_ID inner join user_group ugAlumni on ugAlumni.id=udgAlumni.usergroupid inner join user_details_groups udgStudent on udgStudent.userdetailsid=Student.User_Details_ID inner join user_group ugStudent on ugStudent.id=udgStudent.usergroupid WHERE       (Student.userStatus = 'Active')      and (ugStudent.name='Student' and ugAlumni.name='Alumni');");
    }
    
    private void runCustomQuery12()
    {
        // We release the new active Coop term and copy the history log
        PortalUtils.getJt().execute(
                "UPDATE active_coop_admission_seq SET active_coop_admission_seq.released=inactive_coop_admission_seq.released, active_coop_admission_seq.releaseHistory=inactive_coop_admission_seq.releaseHistory FROM dbo.coop_admission_seq active_coop_admission_seq inner join dbo.coop_admission active_coop_admission on active_coop_admission.id=active_coop_admission_seq.admission and active_coop_admission.active=1 inner join dbo.coop_term active_coop_term on active_coop_term.id=active_coop_admission_seq.actualTerm inner join dbo.user_details on user_details.USER_DETAILS_ID = active_coop_admission.student inner join dbo.coop_term_program active_coop_term_program on active_coop_term_program.id=active_coop_admission.coopTermProgram inner join dbo.coop_program active_coop_program on active_coop_program.id=active_coop_term_program.program inner join dbo.coop_admission inactive_coop_admission on inactive_coop_admission.coopTermProgram=active_coop_admission.coopTermProgram and inactive_coop_admission.student=active_coop_admission.student and inactive_coop_admission.id<>active_coop_admission.id inner join dbo.coop_admission_seq inactive_coop_admission_seq on inactive_coop_admission_seq.admission=inactive_coop_admission.id and inactive_coop_admission_seq.actualTerm=active_coop_admission_seq.actualTerm and inactive_coop_admission_seq.id<>active_coop_admission_seq.id inner join dbo.coop_student_intention coop_student_intention_to_move on coop_student_intention_to_move.sequence=inactive_coop_admission_seq.id left join dbo.coop_admission second_active_coop_admission on second_active_coop_admission.id<>active_coop_admission.id and second_active_coop_admission.student=active_coop_admission.student and second_active_coop_admission.active=1 WHERE second_active_coop_admission.id is null;");

        // We transfer the student intention on the new active coop term
        PortalUtils.getJt().execute(
                "UPDATE coop_student_intention_to_move SET coop_student_intention_to_move.sequence=active_coop_admission_seq.id FROM dbo.coop_admission_seq active_coop_admission_seq inner join dbo.coop_admission active_coop_admission on active_coop_admission.id=active_coop_admission_seq.admission and active_coop_admission.active=1 inner join dbo.coop_term active_coop_term on active_coop_term.id=active_coop_admission_seq.actualTerm inner join dbo.user_details on user_details.USER_DETAILS_ID = active_coop_admission.student inner join dbo.coop_term_program active_coop_term_program on active_coop_term_program.id=active_coop_admission.coopTermProgram inner join dbo.coop_program active_coop_program on active_coop_program.id=active_coop_term_program.program inner join dbo.coop_admission inactive_coop_admission on inactive_coop_admission.coopTermProgram=active_coop_admission.coopTermProgram and inactive_coop_admission.student=active_coop_admission.student and inactive_coop_admission.id<>active_coop_admission.id inner join dbo.coop_admission_seq inactive_coop_admission_seq on inactive_coop_admission_seq.admission=inactive_coop_admission.id and inactive_coop_admission_seq.actualTerm=active_coop_admission_seq.actualTerm and inactive_coop_admission_seq.id<>active_coop_admission_seq.id inner join dbo.coop_student_intention coop_student_intention_to_move on coop_student_intention_to_move.sequence=inactive_coop_admission_seq.id left join dbo.coop_admission second_active_coop_admission on second_active_coop_admission.id<>active_coop_admission.id and second_active_coop_admission.student=active_coop_admission.student and second_active_coop_admission.active=1 WHERE second_active_coop_admission.id is null;");

        // We transfer the TAC (Terms and conditions) on the new active coop term
        PortalUtils.getJt().execute(
                "UPDATE coop_student_tac_to_move SET coop_student_tac_to_move.sequence=active_coop_admission_seq.id FROM dbo.coop_admission_seq active_coop_admission_seq inner join dbo.coop_admission active_coop_admission on active_coop_admission.id=active_coop_admission_seq.admission and active_coop_admission.active=1 inner join dbo.coop_term active_coop_term on active_coop_term.id=active_coop_admission_seq.actualTerm inner join dbo.user_details on user_details.USER_DETAILS_ID = active_coop_admission.student inner join dbo.coop_term_program active_coop_term_program on active_coop_term_program.id=active_coop_admission.coopTermProgram inner join dbo.coop_program active_coop_program on active_coop_program.id=active_coop_term_program.program inner join dbo.coop_admission inactive_coop_admission on inactive_coop_admission.coopTermProgram=active_coop_admission.coopTermProgram and inactive_coop_admission.student=active_coop_admission.student and inactive_coop_admission.id<>active_coop_admission.id inner join dbo.coop_admission_seq inactive_coop_admission_seq on inactive_coop_admission_seq.admission=inactive_coop_admission.id and inactive_coop_admission_seq.actualTerm=active_coop_admission_seq.actualTerm and inactive_coop_admission_seq.id<>active_coop_admission_seq.id inner join dbo.coop_student_tac coop_student_tac_to_move on coop_student_tac_to_move.sequence=inactive_coop_admission_seq.id left join dbo.coop_admission second_active_coop_admission on second_active_coop_admission.id<>active_coop_admission.id and second_active_coop_admission.student=active_coop_admission.student and second_active_coop_admission.active=1 WHERE second_active_coop_admission.id is null;");
    }

}