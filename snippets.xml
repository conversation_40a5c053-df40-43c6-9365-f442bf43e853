 < ? x m l   v e r s i o n = " 1 . 0 "   e n c o d i n g = " U T F - 1 6 "   s t a n d a l o n e = " n o " ? > 
 
 < s n i p p e t s > 
 
         < c a t e g o r y   f i l t e r s = " * "   i d = " c a t e g o r y _ 1 3 6 9 6 7 0 1 5 3 9 0 9 "   i n i t i a l _ s t a t e = " 2 "   l a b e l = " O r b i s - J A V A "   l a r g e i c o n = " "   s m a l l i c o n = " " > 
 
                 < d e s c r i p t i o n / > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 6 9 6 7 0 1 5 3 9 0 9 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 6 9 7 5 2 3 8 2 9 5 3 "   l a b e l = " c u r r e n t U s e r - A c r m "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ N H e l p e r . g e t A c r m U s e r F o r U s e r ( P o r t a l U t i l s . g e t U s e r L o g g e d I n ( ) ,   g e t H t ( ) ) ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 6 9 6 7 0 1 5 3 9 0 9 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 6 9 7 5 2 4 0 4 3 7 5 "   l a b e l = " c u r r e n t U s e r "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ P o r t a l U t i l s . g e t U s e r L o g g e d I n ( ) ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 6 9 6 7 0 1 5 3 9 0 9 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 6 9 7 9 4 6 6 9 5 7 6 "   l a b e l = " l o a d "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ ( )   g e t H t ( ) . l o a d ( . c l a s s ,   n e w   I n t e g e r ( r e q u e s t . g e t P a r a m e t e r ( " " ) ) ) ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 6 9 6 7 0 1 5 3 9 0 9 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 6 9 6 7 0 2 3 4 6 7 9 "   l a b e l = " m e t h o d "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ p u b l i c   M o d e l A n d V i e w   d i s p l a y H o m e ( H t t p S e r v l e t R e q u e s t   r e q u e s t ,   H t t p S e r v l e t R e s p o n s e   r e s p o n s e ) 
 
 { 
 
 	 M o d e l A n d V i e w   m v   =   n e w   M o d e l A n d V i e w ( " f p / f p _ c o o r d i n a t o r H o m e " ) ; 
 
         r e t u r n   m v ; 
 
 } ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 6 9 6 7 0 1 5 3 9 0 9 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 2 5 2 3 0 9 2 6 3 2 "   l a b e l = " P o r t a l U t i l s . g e t H t ( ) "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ P o r t a l U t i l s . g e t H t ( ) ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 6 9 6 7 0 1 5 3 9 0 9 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 6 9 7 5 3 2 5 7 4 8 7 "   l a b e l = " r e q u e s t . g e t P a r a m e t e r "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ r e q u e s t . g e t P a r a m e t e r ( " " ) ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 6 9 6 7 0 1 5 3 9 0 9 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 2 5 2 3 1 5 9 1 0 9 "   l a b e l = " s a v e O r U p d a t e "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ P o r t a l U t i l s . g e t H t ( ) . s a v e O r U p d a t e ( e n t i t y ) ; ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 6 9 6 7 0 1 5 3 9 0 9 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 2 7 7 7 9 6 3 7 0 2 "   l a b e l = " S y s t e m . o u t . p r i n t l n ( ) ; "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ S y s t e m . o u t . p r i n t l n ( ) ; ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
         < / c a t e g o r y > 
 
         < c a t e g o r y   f i l t e r s = " * "   i d = " c a t e g o r y _ 1 3 6 9 6 7 0 6 2 8 7 9 8 "   i n i t i a l _ s t a t e = " 0 "   l a b e l = " O r b i s - S Q L "   l a r g e i c o n = " "   s m a l l i c o n = " " > 
 
                 < d e s c r i p t i o n / > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 6 9 6 7 0 6 2 8 7 9 8 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 6 9 6 7 0 6 4 0 9 5 7 "   l a b e l = " u p d a t e - p a s s w o r d - t o - 1 "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ u p d a t e   u s e r _ d e t a i l s   s e t   p a s s w o r d = ' c 4 c a 4 2 3 8 a 0 b 9 2 3 8 2 0 d c c 5 0 9 a 6 f 7 5 8 4 9 b ' ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
         < / c a t e g o r y > 
 
         < c a t e g o r y   f i l t e r s = " * "   i d = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   i n i t i a l _ s t a t e = " 2 "   l a b e l = " O r b i s - J S P "   l a r g e i c o n = " "   s m a l l i c o n = " " > 
 
                 < d e s c r i p t i o n / > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 6 9 6 6 8 9 9 2 9 5 7 "   l a b e l = " a : h r e f "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < a   h r e f = " j a v a s c r i p t : v o i d ( 0 ) "   o n c l i c k = " o r b i s A p p . b u i l d F o r m ( { a c t i o n : ' d i s p l a y H o m e ' } ) . s u b m i t ( ) " > L i n k < / a > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 2 2 7 1 4 8 8 0 5 5 "   l a b e l = " a c t i o n s "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < d i v   c l a s s = " o r b i s - p o s t i n g - a c t i o n s " > 
 
 	 < d i v   s t y l e = " t e x t - a l i g n :   c e n t e r " > 
 
 	 	 < a   h r e f = " j a v a s c r i p t : v o i d ( 0 ) "   c l a s s = " b t n   b t n - p r i m a r y "   o n c l i c k = " o r b i s A p p . b u i l d F o r m ( { a c t i o n : ' d i s p l a y S u b s c r i p t i o n E d i t ' } ) . s u b m i t ( ) " > A d d   N e w   S u b s c r i p t i o n < / a > 
 
 	 < / d i v > 
 
 < / d i v > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 2 2 7 1 2 2 2 6 4 0 "   l a b e l = " b l u e   r o w "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < d i v   c l a s s = " a l e r t   a l e r t - i n f o " > 
 
 	 N o   R e c o r d s   F o u n d . 
 
 < / d i v > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 6 9 6 6 9 7 9 8 0 4 8 "   l a b e l = " b o x "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < d i v   c l a s s = " b o x " > 
 
 	 < d i v   c l a s s = " b o x T i t l e " > 
 
 	 	 T i t l e 
 
 	 < / d i v > 
 
 	 < d i v   c l a s s = " b o x C o n t e n t " > 
 
 	 	 C o n t e n t 
 
 	 < / d i v > 
 
 < / d i v > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 6 9 6 6 9 6 8 7 6 0 1 "   l a b e l = " c : f o r E a c h "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < c : f o r E a c h   v a r = " t "   i t e m s = " $ { t e r m s } " > 
 
 < / c : f o r E a c h > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 2 4 3 5 1 0 6 2 3 8 "   l a b e l = " c : i f "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < c : i f   t e s t = " $ { } " > 
 
 < / c : i f > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 2 2 7 2 1 5 2 9 7 2 "   l a b e l = " c k - e d i t o r "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < c : s e t   v a r = " e d i t o r I d "   v a l u e = " d e s c r i p t i o n " / > 
 
 < c : s e t   v a r = " e d i t o r V a l u e " > < c : o u t   v a l u e = " $ { s u b s c r i p t i o n . d e s c r i p t i o n } "   e s c a p e X m l = " f a l s e "   / > < / c : s e t > 
 
 < c : s e t   v a r = " e d i t o r H e i g h t "   v a l u e = " 1 5 0 " / > 
 
 < c : s e t   v a r = " e d i t o r W i d t h "   v a l u e = " 5 0 0 " / > 
 
 < c : s e t   v a r = " u s e F i n d e r "   v a l u e = " t r u e " / > 
 
 < % @   i n c l u d e   f i l e = " / W E B - I N F / j s p / c k e d i t o r . j s p " % > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 2 5 2 2 2 4 9 9 6 3 "   l a b e l = " c l a s s = & q u o t ; b t n   b t n - p r i m a r y & q u o t ; "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ c l a s s = " b t n   b t n - p r i m a r y " ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 2 3 9 3 5 3 3 8 2 3 "   l a b e l = " f m t : f o r m a t D a t e "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < f m t : f o r m a t D a t e   v a l u e = " $ { e m a i l [ 0 ] } "   p a t t e r n = " $ { o r b i s D a t e A n d T i m e } "   / > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 6 9 6 6 9 8 3 2 3 5 7 "   l a b e l = " f o r m "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < f o r m   e n c t y p e = " m u l t i p a r t / f o r m - d a t a "   i d = " d i r e c t P l a c e F o r m "   m e t h o d = " p o s t "   c l a s s = " f o r m - h o r i z o n t a l " > 
 
 	 < i n p u t   t y p e = " h i d d e n "   n a m e = " a c t i o n "   v a l u e = " s a v e D i r e c t P l a c e J o b "   / > 
 
 	 < d i v   c l a s s = " c o n t r o l - g r o u p " > 
 
 	 	 < l a b e l   c l a s s = " c o n t r o l - l a b e l "   f o r = " e m p l o y e r " > E m p l o y e r : < / l a b e l > 
 
 	 	 < d i v   c l a s s = " c o n t r o l s " > 
 
 	 	 	 < i n p u t   t y p e = " t e x t "   i d = " e m p l o y e r "   c l a s s = " r e q u i r e d "   n a m e = " e m p l o y e r "   v a l u e = " $ { e m p l o y e r } " / > 
 
 	 	 < / d i v > 
 
 	 < / d i v > 
 
 	 < d i v   c l a s s = " c o n t r o l - g r o u p " > 
 
 	 	 < d i v   c l a s s = " c o n t r o l s " > 
 
 	 	 	 < i n p u t   t y p e = " s u b m i t "   c l a s s = " b t n   b t n - p r i m a r y "   / > 
 
 	 	 < / d i v > 
 
 	 < / d i v > 
 
 < / f o r m > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 6 9 6 6 9 7 2 6 0 1 0 "   l a b e l = " g r e e n   r o w "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < d i v   c l a s s = " a l e r t   a l e r t - s u c c e s s " > 
 
 	 S u c c e s s . 
 
 < / d i v > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 6 9 7 5 5 4 4 6 9 9 6 "   l a b e l = " h i d d e n "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < i n p u t   t y p e = " h i d d e n "   n a m e = " "   v a l u e = " $ { } "   / > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 0 0 2 1 3 3 2 9 1 7 "   l a b e l = " j a v a s c r i p t "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < s c r i p t   t y p e = " t e x t / j a v a s c r i p t " > 
 
 < / s c r i p t > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 2 2 7 2 4 2 3 6 7 4 "   l a b e l = " j q u e r y - v a l i d a t e "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < o r b i s : a d d C o m p o n e n t   c o m p o n e n t = " j q u e r y V a l i d a t e "   v e r s i o n = " 1 . 1 1 . 1 "   / > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 6 9 6 6 8 8 7 9 8 4 2 "   l a b e l = " o r b i s : n a v i g a t i o n "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < o r b i s : n a v i g a t i o n   t i t l e = " $ { o r b i s L o c a l e = = ' e n '   ?   s i t e E l e m e n t . e l e m e n t T i t l e   :   s i t e E l e m e n t . e l e m e n t T i t l e 2 } " > 
 
 	 < o r b i s : n a v B u t t o n   t i t l e = " B a c k   t o   H o m e "   p a r a m s = " { a c t i o n   :   ' d i s p l a y H o m e ' } "   p r i m a r y I c o n = " i c o n - h o m e "   / > 
 
 	 < o r b i s : n a v B u t t o n   t i t l e = " B a c k   t o   S t u d e n t   V i e w "   p a r a m s = " { a c t i o n   :   ' d i s p l a y S t u d e n t ' ,   s t u d e n t I d   :   ' $ { s e q u e n c e . s t u d e n t . i d } ' } "   p r i m a r y I c o n = " i c o n - c h e v r o n - l e f t "   / > 
 
 < / o r b i s : n a v i g a t i o n > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 6 9 6 6 8 9 4 2 6 6 7 "   l a b e l = " o r b i s : s u c c e s s M e s s a g e "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < c : i f   t e s t = " $ { n o t   e m p t y   } " > 
 
 	 < o r b i s : s u c c e s s M e s s a g e > S u c c e s s < / o r b i s : s u c c e s s M e s s a g e > 
 
 < / c : i f > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 2 4 3 1 4 4 2 8 1 7 "   l a b e l = " p e r m i s s i o n "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < c : i f   t e s t = " $ { n o t   e m p t y   c u r r e n t U s e r . a s s i g n e d T y p e s [ ' D a t a b a s e   -   C a n   A p p r o v e   A c c o u n t s ' ] } " > 
 
 < / c : i f > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 2 8 1 1 2 5 0 7 9 1 1 "   l a b e l = " s p r i n g : m e s s a g e "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n / > 
 
                         < c o n t e n t > < ! [ C D A T A [ < s p r i n g : m e s s a g e   c o d e = " i 1 8 n . "   / > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 0 4 4 1 6 9 9 0 4 7 "   l a b e l = " t a b l e "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < t a b l e   c l a s s = " t a b l e   t a b l e - b o r d e r e d   t a b l e - h o v e r " > 
 
 	 < t h e a d > 
 
 	 	 < t r > 
 
 	 	 	 < t h > < / t h > 
 
 	 	 < / t r > 
 
 	 < / t h e a d > 
 
 	 < t b o d y > 
 
 	 	 	 < c : f o r E a c h   v a r = " g "   i t e m s = " $ { } " > 
 
 	 	 	 	 < t r > 
 
 	 	 	 	 	 < t d > < / t d > 
 
 	 	 	 	 < / t r > 
 
 	 	 	 < / c : f o r E a c h > 	 	 	 	 
 
 	 < / t b o d y > 
 
 < / t a b l e > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 1 7 6 2 9 6 9 7 9 5 "   l a b e l = " t a b s - m a i n "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < d i v   c l a s s = " r o w - f l u i d " > 
 
 	 < d i v   c l a s s = " s p a n 1 2 " > 
 
 	 	 < d i v   c l a s s = " o r b i s T a b C o n t a i n e r " > 
 
 	 	 	 < d i v   c l a s s = " t a b b a b l e " > 
 
 	 	 	 	 < c : s e t   v a r = " c u r r e n t T a b "   v a l u e = " o v e r v i e w "   / > 
 
 	 	 	 	 < % @   i n c l u d e   f i l e = " _ t a b s . j s p " % > 
 
 	 	 	 	 < d i v   c l a s s = " t a b - c o n t e n t " > 
 
 	 	 	 	 	 < d i v   c l a s s = " t a b - p a n e   f a d e   i n   a c t i v e "   i d = " d a s h b o a r d " > 
 
 	 	 	 	 	 	 < d i v   c l a s s = " r o w - f l u i d " > 
 
 	 	 	 	 	 	 	 < d i v   c l a s s = " s p a n 8 " > 
 
 	 	 	 	 	 	 	 	 < d i v   c l a s s = " b o x " > 
 
 	 	 	 	 	 	 	 	 	 < d i v   c l a s s = " b o x C o n t e n t " > 	 C o n t e n t 
 
 	 	 	 	 	 	 	 	 	 < / d i v > 
 
 	 	 	 	 	 	 	 	 < / d i v > 
 
 	 	 	 	 	 	 	 < / d i v > 
 
 	 	 	 	 	 	 < / d i v > 
 
 	 	 	 	 	 < / d i v > 
 
 	 	 	 	 < / d i v > 
 
 	 	 	 < / d i v > 
 
 	 	 < / d i v > 
 
 	 < / d i v > 
 
 < / d i v > 
 
 ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 1 7 6 3 0 3 2 1 5 0 "   l a b e l = " t a b s - n a v "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < % @   i n c l u d e   f i l e = " / W E B - I N F / j s p / i n c l u d e . j s p " % > 
 
 < d i v   c l a s s = " o r b i s M o d u l e T a b s " > 
 
 	 < u l   c l a s s = " n a v   n a v - t a b s " > 
 
 	 	 < l i   < c : i f   t e s t = " $ { ' o v e r v i e w ' = = c u r r e n t T a b } " > c l a s s = " a c t i v e " < / c : i f > > 
 
 	 	 	 < a   h r e f = " # " 	 o n c l i c k = " o r b i s A p p . b u i l d F o r m ( { a c t i o n : ' p r e v i e w E m a i l ' , i d : ' $ { e m a i l . e m a i l . i d } ' } ) . s u b m i t ( ) ; " > 
 
 	 	 	 	 < i   c l a s s = " i c o n - h o m e   i c o n - l a r g e " > < / i >   C a m p a i g n   D e t a i l   < / a > 
 
 	 	 < / l i > 
 
 	 < / u l > 
 
 < / d i v > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 8 7 0 4 3 7 0 4 8 1 2 "   l a b e l = " t a b l e S o r t e r "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < o r b i s : a d d C o m p o n e n t   c o m p o n e n t = " t a b l e S o r t e r " / > 
 
 < s c r i p t   t y p e = " t e x t / j a v a s c r i p t " > 
 
 	 $ ( d o c u m e n t ) . r e a d y ( f u n c t i o n ( )   { 
 
 	 	 $ ( " # a g g r e g a t o r T a b l e " ) . t a b l e s o r t e r ( ) ; 
 
 	 } ) ; 
 
 < / s c r i p t > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
                 < i t e m   c a t e g o r y = " c a t e g o r y _ 1 3 0 6 9 3 9 0 3 4 3 1 0 "   c l a s s = " "   e d i t o r c l a s s = " "   i d = " i t e m _ 1 3 7 2 2 7 8 3 5 9 1 9 4 "   l a b e l = " u s e r   l o o k u p "   l a r g e i c o n = " "   s m a l l i c o n = " "   s n i p p e t P r o v i d e r = " o r g . e c l i p s e . w s t . c o m m o n . s n i p p e t s . u i . T e x t S n i p p e t P r o v i d e r " > 
 
                         < d e s c r i p t i o n > < ! [ C D A T A [ U n n a m e d   T e m p l a t e ] ] > < / d e s c r i p t i o n > 
 
                         < c o n t e n t > < ! [ C D A T A [ < s c r i p t   t y p e = " t e x t / j a v a s c r i p t " > 
 
 	 v a r   c o n t r o l l e r P a t h   =   " $ { s i t e E l e m e n t . f u l l P a t h } . h t m " ; 
 
 	 $ ( d o c u m e n t ) . r e a d y ( f u n c t i o n ( ) { 
 
 	 	 $ ( " # u s e r L o o k u p " ) . a u t o c o m p l e t e ( { 
 
 	 	 	 s o u r c e :   c o n t r o l l e r P a t h   +   " ? a c t i o n = l o o k u p U s e r s & r = "   +   ( M a t h . r a n d o m ( )   *   1 0 0 0 0 )   +   " & s o u r c e = d a s h b o a r d " , 
 
 	 	 	 m i n L e n g t h :   3 , 
 
 	 	 	 f o c u s :   f u n c t i o n   ( e v e n t ,   u i   )   { 
 
 	 	 	 	 / /   p r e v e n t   t h e   t e x t   c h a n g e   b l i p   t o   i t e m . v a l u e 
 
 	 	 	 	 r e t u r n   f a l s e ; 
 
 	 	 	 } , 
 
 	 	 	 s e l e c t :   f u n c t i o n (   e v e n t ,   u i   )   { 
 
 	 	 	 	 o r b i s A p p . b u i l d F o r m ( { 
 
 	 	 	 	 	 	 a c t i o n   :   " a d d S t u d e n t T o S u b s c r i p t i o n " , 
 
 	 	 	 	 	 	 q u i c k L o o k u p   :   " t r u e " , 
 
 	 	 	 	 	 	 s u b s c r i p t i o n I d   :   " $ { s u b s c r i p t i o n . i d } " , 
 
 	 	 	 	 	 	 a c r m U s e r I d   :   u i . i t e m . i d 
 
 	 	 	 	 	 } ) . s u b m i t ( ) ; 
 
 	 	 	 } 
 
 	 	 	 
 
 	 	 } ) ; 
 
 	 } ) ; 
 
 < / s c r i p t > 
 
 < d i v   c l a s s = " t e x t - c e n t e r " > 
 
 	 < d i v   c l a s s = " i n p u t - p r e p e n d " > 
 
 	   	 < s p a n   c l a s s = " a d d - o n " > 
 
 	 	 	 < i   c l a s s = " i c o n - u s e r   i c o n - l a r g e " > < / i > 
 
 	 	 < / s p a n > 
 
 	 	 < i n p u t   t y p e = " t e x t "   n a m e = " u s e r L o o k u p "   i d = " u s e r L o o k u p "   v a l u e = " "   s i z e = " 2 0 "   p l a c e h o l d e r = " U s e r   L o o k - u p " > 
 
 	 < / d i v > 
 
 < / d i v > ] ] > < / c o n t e n t > 
 
                 < / i t e m > 
 
         < / c a t e g o r y > 
 
 < / s n i p p e t s > 
 
