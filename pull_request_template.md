# Pull Request

## Issues addressed

https://orbisinc.atlassian.net/browse/OUTCOME-

## Type of change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Refactor (change made for optimization and/or clean code)
- [ ] Usability Update (small changes to existing features that make them easier)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] This changes requires a documentation update (Include a link to the onenote page you changed)

## Development Checklist

- My code follows the style guidelines of this project
- Ensure new i18n codes are added to both core messages files (use DeepL to translate en -> fr)
- I have performed a self-review of code changes
- Changes do not generate new warnings
- Allowed for turn on, use, turn off
- Deletion handled
- Cloning handled
- Special characters considered
- Explicit joins used
- Refactoring? Show team lead before commit
- Walked through changes in UI
- Database defaults/schema updates added
- Design/layout principles followed
- Included l2
- Didn’t re-invent the wheel
- Tested every instance of code in all branches
- Ticket title and description match work done
- Resolution details and screenshots added
- If bug – commit that caused it, linked ticket
- Security and privacy considerations
- AODA Accessibility
- [ ] By checking this box you agree that your commit meets all the requirements under Development Checklist.
- [ ] I have reviewed the new Hibernate rules and have ensured that all my work has followed the new rules.

## PR Checklist (Check One)
- [ ] My ticket is for a two point fix version(X.Y) and my PR is against main
- [ ] My ticket is for a three point fix version(X.Y.Z) OR a Praline ticket and this PR is ONLY against the X.YRelease or PralineRelease branch of the patch I am working on
- [ ] My ticket is a HF ticket (X.Y.Z-HF) and I have two PRs, one for (X.Y.Z and X.YRelease)
- [ ] My ticket is a Nutella ticket and I have made Nutella and Praline PRs
